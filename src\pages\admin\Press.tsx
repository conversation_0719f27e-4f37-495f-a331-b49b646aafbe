import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Newspaper,
  Upload,
  Image
} from 'lucide-react';
import { useSession } from '@/contexts/SessionContext';
import { supabase } from '@/integrations/supabase/client';
import { isAdmin, isAdminEmail } from '@/utils/adminUtils';
import { getTableColumns, createPressReleaseInsert } from '@/utils/databaseUtils';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface PressRelease {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  featured_image_url?: string;
  published_at?: string;
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
}

interface PressFormData {
  title: string;
  content: string;
  excerpt: string;
  featured_image_url: string;
  status: 'draft' | 'published';
}

const PressAdminPage = () => {
  const navigate = useNavigate();
  const { session, user } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pressReleases, setPressReleases] = useState<PressRelease[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRelease, setSelectedRelease] = useState<PressRelease | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [formData, setFormData] = useState<PressFormData>({
    title: '',
    content: '',
    excerpt: '',
    featured_image_url: '',
    status: 'draft'
  });
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    const checkAdminAndFetchReleases = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!session || !user) {
          setIsLoading(false);
          setError('You must be logged in to access the admin dashboard.');
          return;
        }

        const userId = session.user.id;
        const userEmail = user.email || '';

        // Check if user is admin
        try {
          const adminStatus = await isAdmin(userId, userEmail);

          if (adminStatus) {
            setIsAuthorized(true);
            fetchPressReleases();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        } catch (adminError) {
          console.error('Error checking admin status:', adminError);

          if (isAdminEmail(userEmail)) {
            setIsAuthorized(true);
            fetchPressReleases();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error in admin verification:', error);
        setIsAuthorized(false);
        setError('An error occurred while verifying admin permissions.');
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAndFetchReleases();
  }, [session, user, navigate]);

  const fetchPressReleases = async () => {
    try {
      const { data: releasesData, error: releasesError } = await supabase
        .from('press_releases')
        .select('*')
        .order('created_at', { ascending: false });

      if (releasesError) {
        console.error('Error fetching press releases:', releasesError);
        setError('Error fetching press releases.');
        return;
      }

      const transformedReleases = releasesData?.map(release => ({
        id: release.id,
        title: release.title || '',
        content: release.content || '',
        excerpt: release.excerpt || '',
        featured_image_url: release.featured_image_url || '',
        published_at: release.published_at || '',
        status: release.status || 'draft',
        created_at: release.created_at || '',
        updated_at: release.updated_at || ''
      })) || [];

      setPressReleases(transformedReleases);
    } catch (error) {
      console.error('Error fetching press releases:', error);
      setError('An error occurred while fetching press releases.');
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const filteredReleases = pressReleases.filter(release => {
    const matchesSearch = release.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         release.content.toLowerCase().includes(searchQuery.toLowerCase());

    if (activeTab === 'all') return matchesSearch;
    return matchesSearch && release.status === activeTab;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      published: 'default'
    } as const;

    return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>{status}</Badge>;
  };

  const handleImageUpload = async (file: File) => {
    setIsUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = `press-images/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('press-images')
        .upload(filePath, file);

      if (uploadError) {
        console.error('Error uploading image:', uploadError);
        setError('Error uploading image.');
        return null;
      }

      const { data: { publicUrl } } = supabase.storage
        .from('press-images')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('An error occurred while uploading the image.');
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const createPressRelease = async () => {
    try {
      // Get the actual table columns
      const tableColumns = await getTableColumns('press_releases');
      console.log('Available press_releases columns:', tableColumns);

      if (tableColumns.length === 0) {
        setError('Unable to determine table structure. Please check if the press_releases table exists.');
        return;
      }

      // Create insert data using the utility function
      const insertData = createPressReleaseInsert(formData, tableColumns);
      console.log('Press release insert data:', insertData);

      const { error } = await supabase
        .from('press_releases')
        .insert(insertData);

      if (error) {
        console.error('Error creating press release:', error);
        setError(`Error creating press release: ${error.message}`);
        return;
      }

      setFormData({
        title: '',
        content: '',
        excerpt: '',
        featured_image_url: '',
        status: 'draft'
      });
      setIsCreateDialogOpen(false);
      fetchPressReleases();
    } catch (error) {
      console.error('Error creating press release:', error);
      setError('An error occurred while creating the press release.');
    }
  };

  const deletePressRelease = async (id: string) => {
    try {
      const { error } = await supabase
        .from('press_releases')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting press release:', error);
        setError('Error deleting press release.');
        return;
      }

      setIsDeleteDialogOpen(false);
      setSelectedRelease(null);
      fetchPressReleases();
    } catch (error) {
      console.error('Error deleting press release:', error);
      setError('An error occurred while deleting the press release.');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!isAuthorized) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-5">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Newspaper className="h-6 w-6" />
            <h1 className="text-3xl font-bold tracking-tight">Press Releases</h1>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Release
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Press Releases</CardTitle>
            <CardDescription>
              Manage press releases and media coverage for your company.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <div className="flex items-center justify-between">
                <TabsList>
                  <TabsTrigger value="all">All Releases</TabsTrigger>
                  <TabsTrigger value="draft">Drafts</TabsTrigger>
                  <TabsTrigger value="published">Published</TabsTrigger>
                </TabsList>
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search releases..."
                    value={searchQuery}
                    onChange={handleSearch}
                    className="w-64"
                  />
                </div>
              </div>

              <TabsContent value={activeTab} className="space-y-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Published</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredReleases.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8">
                            No press releases found.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredReleases.map((release) => (
                          <TableRow key={release.id}>
                            <TableCell className="font-medium">
                              <div>
                                <div className="font-medium">{release.title}</div>
                                <div className="text-sm text-muted-foreground truncate max-w-xs">
                                  {release.excerpt}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{getStatusBadge(release.status)}</TableCell>
                            <TableCell>
                              {release.published_at ? (
                                <div className="flex items-center">
                                  <Calendar className="mr-2 h-4 w-4" />
                                  {new Date(release.published_at).toLocaleDateString()}
                                </div>
                              ) : (
                                <span className="text-muted-foreground">Not published</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {new Date(release.created_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedRelease(release);
                                    setIsViewDialogOpen(true);
                                  }}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedRelease(release);
                                    setIsEditDialogOpen(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedRelease(release);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Create Release Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Press Release</DialogTitle>
            <DialogDescription>
              Create a new press release for your company.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                placeholder="Enter release title..."
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="excerpt">Excerpt</Label>
              <Textarea
                id="excerpt"
                placeholder="Brief description of the release..."
                value={formData.excerpt}
                onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="featured_image">Featured Image</Label>
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      const imageUrl = await handleImageUpload(file);
                      if (imageUrl) {
                        setFormData(prev => ({ ...prev, featured_image_url: imageUrl }));
                      }
                    }
                  }}
                  disabled={isUploading}
                />
                {isUploading && <div className="text-sm text-muted-foreground">Uploading...</div>}
              </div>
              {formData.featured_image_url && (
                <div className="mt-2">
                  <img
                    src={formData.featured_image_url}
                    alt="Featured"
                    className="w-32 h-20 object-cover rounded border"
                  />
                </div>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
              </select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                placeholder="Write your press release content..."
                className="min-h-[300px]"
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsCreateDialogOpen(false);
              setFormData({
                title: '',
                content: '',
                excerpt: '',
                featured_image_url: '',
                status: 'draft'
              });
            }}>
              Cancel
            </Button>
            <Button onClick={createPressRelease} disabled={!formData.title || !formData.content}>
              Create Release
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Release Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>View Press Release</DialogTitle>
          </DialogHeader>
          {selectedRelease && (
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">{selectedRelease.title}</h3>
                <p className="text-sm text-muted-foreground">
                  Created: {new Date(selectedRelease.created_at).toLocaleDateString()}
                  {selectedRelease.published_at && ` • Published: ${new Date(selectedRelease.published_at).toLocaleDateString()}`}
                </p>
              </div>
              {selectedRelease.featured_image_url && (
                <div>
                  <img
                    src={selectedRelease.featured_image_url}
                    alt="Featured"
                    className="w-full max-w-md h-48 object-cover rounded border"
                  />
                </div>
              )}
              <div>
                <h4 className="font-medium mb-2">Excerpt</h4>
                <p className="text-sm">{selectedRelease.excerpt}</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Content</h4>
                <div className="prose prose-sm max-w-none">
                  <p className="whitespace-pre-wrap">{selectedRelease.content}</p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Press Release</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedRelease?.title}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedRelease && deletePressRelease(selectedRelease.id)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default PressAdminPage;
