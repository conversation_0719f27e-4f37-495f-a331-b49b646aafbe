import React, { useState, useEffect } from 'react';
import { Download, X, Share2, MoreVertical, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Add iOS detection helper
const isIOS = () => {
  if (typeof window === 'undefined') return false;

  return (
    /iPad|iPhone|iPod/i.test(navigator.userAgent) ||
    (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) ||
    (navigator.userAgent.includes("Mac") && "ontouchend" in document)
  );
};

// Add keyframes for pulse animation
const pulseKeyframes = `
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 166, 81, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 166, 81, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 166, 81, 0);
  }
}
`;

// Add style tag to head
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = pulseKeyframes;
  document.head.appendChild(style);
}

/**
 * A simplified PWA install button that works on all devices
 * This is a fallback component that will be used if the main PWAInstallPrompt component fails
 */
const SimplePWAInstall: React.FC = () => {
  const [showButton, setShowButton] = useState(false);
  const [installPrompt, setInstallPrompt] = useState<any>(null);

  useEffect(() => {
    try {
      // Detect browser
      const isChromeIOS = /CriOS/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/i.test(navigator.userAgent) && !(window as any).MSStream;

      // Don't show on iOS devices (we'll use IOSInstallButton instead)
      if (isIOS || isChromeIOS || isIOS()) {
        console.log('iOS device detected, not showing SimplePWAInstall');
        return;
      }

      // Don't show if already installed
      if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
        return;
      }

      // Don't show if recently dismissed
      const dismissedTime = parseInt(localStorage.getItem('pwa-simple-dismissed') || '0', 10);
      if (Date.now() - dismissedTime < 7 * 24 * 60 * 60 * 1000) {
        return;
      }

      // Listen for beforeinstallprompt event
      const handleBeforeInstallPrompt = (e: Event) => {
        e.preventDefault();
        setInstallPrompt(e);
        setShowButton(true);
      };

      // Only add event listener if not on iOS Chrome (which doesn't support it)
      if (!(isIOS && isChromeIOS)) {
        window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt as any);
      }

      // Show button after a delay for iOS devices
      if (isIOS) {
        // For iOS, show after a longer delay and only if they've visited before
        const visitCount = parseInt(localStorage.getItem('pwa-visit-count') || '0', 10);
        localStorage.setItem('pwa-visit-count', (visitCount + 1).toString());

        if (visitCount >= 1) {
          setTimeout(() => {
            setShowButton(true);
          }, 5000);
        }
      } else {
        // For other browsers, show after a shorter delay
        setTimeout(() => {
          setShowButton(true);
        }, 3000);
      }

      return () => {
        if (!(isIOS && isChromeIOS)) {
          window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt as any);
        }
      };
    } catch (error) {
      console.error('Error in SimplePWAInstall:', error);
      return () => {};
    }
  }, []);

  const handleInstall = async () => {
    try {
      // Always check for iOS first
      const userAgent = navigator.userAgent || '';
      const isIOS = /iPad|iPhone|iPod/i.test(userAgent) && !(window as any).MSStream;
      const isChrome = /CriOS/i.test(userAgent);

      console.log('Device detection:', { isIOS, isChrome, userAgent });

      // If it's iOS, show the custom modal
      if (isIOS) {
        console.log('iOS device detected, showing custom modal');
        showIOSInstructions(isChrome);
        return;
      }

      // For other platforms with installPrompt
      if (installPrompt) {
        await installPrompt.prompt();
        const { outcome } = await installPrompt.userChoice;

        if (outcome === 'accepted') {
          setShowButton(false);
        }
      } else {
        // Fallback for other browsers without installPrompt
        // Show generic instructions
        alert('To install this app on your device: tap the share icon and then "Add to Home Screen"');
      }
    } catch (error) {
      console.error('Error installing PWA:', error);
      alert('To install this app on your device: tap the share icon and then "Add to Home Screen"');
    }
  };

  const showIOSInstructions = (isChrome: boolean) => {
    // Check if dark mode is enabled
    const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

    // Create modal container
    const modal = document.createElement('div');
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    modal.style.zIndex = '100000'; // Ultra high z-index
    modal.style.display = 'flex';
    modal.style.alignItems = 'center';
    modal.style.justifyContent = 'center';
    modal.style.backdropFilter = 'blur(4px)';
    modal.style.WebkitBackdropFilter = 'blur(4px)'; // For Safari support

    // Create content container
    const content = document.createElement('div');
    content.style.backgroundColor = isDarkMode ? '#1f2937' : 'white';
    content.style.color = isDarkMode ? '#e5e7eb' : '#374151';
    content.style.borderRadius = '16px';
    content.style.padding = '28px';
    content.style.maxWidth = '90%';
    content.style.width = '450px';
    content.style.boxShadow = isDarkMode ?
      '0 10px 25px rgba(0, 0, 0, 0.5)' :
      '0 10px 25px rgba(0, 0, 0, 0.2)';
    content.style.overflowY = 'auto';
    content.style.maxHeight = '90vh';

    // Create header
    const header = document.createElement('div');
    header.style.display = 'flex';
    header.style.alignItems = 'center';
    header.style.justifyContent = 'space-between';
    header.style.marginBottom = '24px';

    const title = document.createElement('h3');
    title.textContent = 'Install Payvoicer';
    title.style.fontSize = '22px';
    title.style.fontWeight = 'bold';
    title.style.margin = '0';
    title.style.color = isDarkMode ? '#FFD700' : '#00A651';
    title.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';

    const closeButton = document.createElement('button');
    closeButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
    closeButton.style.background = 'transparent';
    closeButton.style.border = 'none';
    closeButton.style.cursor = 'pointer';
    closeButton.style.color = isDarkMode ? '#e5e7eb' : '#6b7280';
    closeButton.style.padding = '4px';
    closeButton.style.borderRadius = '4px';
    closeButton.style.display = 'flex';
    closeButton.style.alignItems = 'center';
    closeButton.style.justifyContent = 'center';

    closeButton.onclick = function() {
      document.body.removeChild(modal);
    };

    header.appendChild(title);
    header.appendChild(closeButton);

    // Create description
    const description = document.createElement('p');
    description.textContent = 'Follow these steps to install Payvoicer on your iOS device:';
    description.style.marginBottom = '24px';
    description.style.color = isDarkMode ? '#d1d5db' : '#4b5563';
    description.style.fontSize = '16px';
    description.style.textAlign = 'center';
    description.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';

    // Create steps container
    const stepsContainer = document.createElement('div');
    stepsContainer.style.backgroundColor = isDarkMode ? '#111827' : '#f9fafb';
    stepsContainer.style.borderRadius = '12px';
    stepsContainer.style.padding = '20px';
    stepsContainer.style.marginBottom = '24px';
    stepsContainer.style.boxShadow = isDarkMode ?
      '0 2px 8px rgba(0, 0, 0, 0.3)' :
      '0 2px 8px rgba(0, 0, 0, 0.05)';

    // Step 1
    const step1 = document.createElement('div');
    step1.style.display = 'flex';
    step1.style.alignItems = 'flex-start';
    step1.style.marginBottom = '16px';

    const step1Number = document.createElement('div');
    step1Number.textContent = '1';
    step1Number.style.backgroundColor = isDarkMode ? '#0c4a6e' : '#e0f2fe';
    step1Number.style.color = isDarkMode ? '#7dd3fc' : '#0284c7';
    step1Number.style.width = '28px';
    step1Number.style.height = '28px';
    step1Number.style.borderRadius = '50%';
    step1Number.style.display = 'flex';
    step1Number.style.alignItems = 'center';
    step1Number.style.justifyContent = 'center';
    step1Number.style.fontWeight = 'bold';
    step1Number.style.marginRight = '12px';
    step1Number.style.flexShrink = '0';

    const step1Text = document.createElement('div');
    step1Text.style.paddingTop = '4px';

    const step1Title = document.createElement('p');
    step1Title.style.margin = '0';
    step1Title.style.fontWeight = '500';

    if (isChrome) {
      step1Title.innerHTML = 'Tap the three dots menu <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: inline-block; vertical-align: middle;"><circle cx="12" cy="12" r="1"></circle><circle cx="12" cy="5" r="1"></circle><circle cx="12" cy="19" r="1"></circle></svg> in the top-right';
    } else {
      step1Title.innerHTML = 'Tap the Share icon <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: inline-block; vertical-align: middle;"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line><line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line></svg> at the bottom';
    }

    step1Text.appendChild(step1Title);
    step1.appendChild(step1Number);
    step1.appendChild(step1Text);

    // Step 2
    const step2 = document.createElement('div');
    step2.style.display = 'flex';
    step2.style.alignItems = 'flex-start';
    step2.style.marginBottom = '16px';

    const step2Number = document.createElement('div');
    step2Number.textContent = '2';
    step2Number.style.backgroundColor = isDarkMode ? '#0c4a6e' : '#e0f2fe';
    step2Number.style.color = isDarkMode ? '#7dd3fc' : '#0284c7';
    step2Number.style.width = '28px';
    step2Number.style.height = '28px';
    step2Number.style.borderRadius = '50%';
    step2Number.style.display = 'flex';
    step2Number.style.alignItems = 'center';
    step2Number.style.justifyContent = 'center';
    step2Number.style.fontWeight = 'bold';
    step2Number.style.marginRight = '12px';
    step2Number.style.flexShrink = '0';

    const step2Text = document.createElement('div');
    step2Text.style.paddingTop = '4px';

    const step2Title = document.createElement('p');
    step2Title.style.margin = '0';
    step2Title.style.fontWeight = '500';

    if (isChrome) {
      step2Title.innerHTML = 'Tap "Share..." then "Add to Home Screen"';
    } else {
      step2Title.innerHTML = 'Scroll down and tap "Add to Home Screen"';
    }

    step2Text.appendChild(step2Title);
    step2.appendChild(step2Number);
    step2.appendChild(step2Text);

    // Step 3
    const step3 = document.createElement('div');
    step3.style.display = 'flex';
    step3.style.alignItems = 'flex-start';

    const step3Number = document.createElement('div');
    step3Number.textContent = '3';
    step3Number.style.backgroundColor = isDarkMode ? '#0c4a6e' : '#e0f2fe';
    step3Number.style.color = isDarkMode ? '#7dd3fc' : '#0284c7';
    step3Number.style.width = '28px';
    step3Number.style.height = '28px';
    step3Number.style.borderRadius = '50%';
    step3Number.style.display = 'flex';
    step3Number.style.alignItems = 'center';
    step3Number.style.justifyContent = 'center';
    step3Number.style.fontWeight = 'bold';
    step3Number.style.marginRight = '12px';
    step3Number.style.flexShrink = '0';

    const step3Text = document.createElement('div');
    step3Text.style.paddingTop = '4px';

    const step3Title = document.createElement('p');
    step3Title.style.margin = '0';
    step3Title.style.fontWeight = '500';
    step3Title.textContent = 'Tap "Add" in the top right corner';

    step3Text.appendChild(step3Title);
    step3.appendChild(step3Number);
    step3.appendChild(step3Text);

    // Add steps to container
    stepsContainer.appendChild(step1);
    stepsContainer.appendChild(step2);
    stepsContainer.appendChild(step3);

    // Create button
    const button = document.createElement('button');
    button.textContent = 'Got it';
    button.style.backgroundColor = isDarkMode ? '#FFD700' : '#00A651';
    button.style.color = isDarkMode ? '#1f2937' : 'white';
    button.style.border = 'none';
    button.style.padding = '12px 24px';
    button.style.borderRadius = '8px';
    button.style.fontWeight = 'bold';
    button.style.cursor = 'pointer';
    button.style.width = '100%';
    button.style.fontSize = '16px';
    button.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
    button.style.boxShadow = isDarkMode ?
      '0 4px 12px rgba(255, 215, 0, 0.2)' :
      '0 4px 12px rgba(0, 166, 81, 0.2)';
    button.style.transition = 'all 0.2s ease';

    button.onmouseover = function() {
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = isDarkMode ?
        '0 6px 16px rgba(255, 215, 0, 0.3)' :
        '0 6px 16px rgba(0, 166, 81, 0.3)';
    };

    button.onmouseout = function() {
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = isDarkMode ?
        '0 4px 12px rgba(255, 215, 0, 0.2)' :
        '0 4px 12px rgba(0, 166, 81, 0.2)';
    };

    button.onclick = function() {
      document.body.removeChild(modal);
    };

    // Assemble modal
    content.appendChild(header);
    content.appendChild(description);
    content.appendChild(stepsContainer);
    content.appendChild(button);
    modal.appendChild(content);

    // Add to body
    document.body.appendChild(modal);
  };

  const handleDismiss = () => {
    setShowButton(false);
    try {
      localStorage.setItem('pwa-simple-dismissed', Date.now().toString());
    } catch (error) {
      console.error('Error saving dismissal:', error);
    }
  };

  if (!showButton) {
    return null;
  }

  // Detect if it's Chrome on iOS
  const isChromeIOS = /CriOS/i.test(navigator.userAgent);
  const isIOS = /iPad|iPhone|iPod/i.test(navigator.userAgent) && !(window as any).MSStream;

  // Use a simpler UI for Chrome on iOS to avoid rendering issues
  if (isIOS && isChromeIOS) {
    return (
      <div
        style={{
          position: 'fixed',
          bottom: '1.5rem',
          right: '1.5rem',
          zIndex: 9999,
          pointerEvents: 'auto',
          backgroundColor: '#00A651',
          color: 'white',
          padding: '0',
          borderRadius: '50%',
          boxShadow: '0 4px 10px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 166, 81, 0.25)',
          animation: 'pulse 2s infinite',
          width: '3.5rem',
          height: '3.5rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          willChange: 'transform',
          transition: 'all 0.2s ease'
        }}
        onMouseOver={(e) => {
          const target = e.currentTarget;
          target.style.transform = 'translateY(-3px) scale(1.05)';
          target.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.25), 0 15px 30px rgba(0, 166, 81, 0.3)';
          target.style.animation = 'none';
        }}
        onMouseOut={(e) => {
          const target = e.currentTarget;
          target.style.transform = 'none';
          target.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 166, 81, 0.25)';
          target.style.animation = 'pulse 2s infinite';
        }}
        onMouseDown={(e) => {
          const target = e.currentTarget;
          target.style.transform = 'translateY(0) scale(0.95)';
          target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 166, 81, 0.2)';
        }}
        onMouseUp={(e) => {
          const target = e.currentTarget;
          target.style.transform = 'translateY(-3px) scale(1.05)';
          target.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.25), 0 15px 30px rgba(0, 166, 81, 0.3)';
        }}
        onClick={handleInstall}
      >
        <Download size={24} color="white" />
      </div>
    );
  }

  // Regular UI for other browsers
  return (
    <div
      style={{
        position: 'fixed',
        bottom: '1.5rem',
        right: '1.5rem',
        zIndex: 9999,
        pointerEvents: 'auto',
        display: 'flex',
        alignItems: 'center',
        backgroundColor: '#00A651',
        color: 'white',
        padding: '0.75rem 1.25rem',
        borderRadius: '50px',
        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 166, 81, 0.25)',
        maxWidth: '250px',
        animation: 'pulse 2s infinite',
        willChange: 'transform',
        transition: 'all 0.2s ease'
      }}
      onMouseOver={(e) => {
        const target = e.currentTarget;
        target.style.transform = 'translateY(-3px) scale(1.05)';
        target.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.25), 0 15px 30px rgba(0, 166, 81, 0.3)';
        target.style.animation = 'none';
      }}
      onMouseOut={(e) => {
        const target = e.currentTarget;
        target.style.transform = 'none';
        target.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 166, 81, 0.25)';
        target.style.animation = 'pulse 2s infinite';
      }}
      onMouseDown={(e) => {
        const target = e.currentTarget;
        target.style.transform = 'translateY(0) scale(0.95)';
        target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 166, 81, 0.2)';
      }}
      onMouseUp={(e) => {
        const target = e.currentTarget;
        target.style.transform = 'translateY(-3px) scale(1.05)';
        target.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.25), 0 15px 30px rgba(0, 166, 81, 0.3)';
      }}
    >
      <Download size={20} style={{ marginRight: '8px' }} />
      <span style={{ marginRight: '8px', fontWeight: 500 }}>Install App</span>
      <div style={{ display: 'flex', gap: '8px' }}>
        <button
          onClick={handleInstall}
          style={{
            backgroundColor: 'white',
            color: '#00A651',
            border: 'none',
            padding: '4px 8px',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: 'bold'
          }}
        >
          Install
        </button>
        <button
          onClick={handleDismiss}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            padding: '0',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          aria-label="Dismiss"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

export default SimplePWAInstall;
