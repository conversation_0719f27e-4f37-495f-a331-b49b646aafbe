import { useState } from 'react';
import { GraInvoiceSubmission } from './GraInvoiceSubmission';
import { GraInvoiceResponse } from '@/services/graApiService.v8.1';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

interface InvoiceGraIntegrationExampleProps {
  invoice: any; // Your invoice data
}

export function InvoiceGraIntegrationExample({ invoice }: InvoiceGraIntegrationExampleProps) {
  const [graResponse, setGraResponse] = useState<GraInvoiceResponse | null>(null);
  const [error, setError] = useState<Error | null>(null);
  
  const handleSuccess = (response: GraInvoiceResponse) => {
    setGraResponse(response);
    setError(null);
    
    // You might want to update your invoice with the GRA response data
    // For example, save the signature, QR code, etc.
    console.log('Invoice submitted to GRA successfully:', response);
  };
  
  const handleError = (error: Error) => {
    setError(error);
    setGraResponse(null);
    console.error('Failed to submit invoice to GRA:', error);
  };
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Invoice #{invoice.invoiceNumber}
            {graResponse && (
              <Badge variant="success">GRA Verified</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="details">
            <TabsList>
              <TabsTrigger value="details">Invoice Details</TabsTrigger>
              <TabsTrigger value="gra">GRA Integration</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details">
              {/* Your existing invoice details UI */}
              <div className="py-4">
                <h3 className="text-lg font-medium">Customer: {invoice.customerName}</h3>
                <p className="text-sm text-gray-500">Date: {new Date(invoice.date).toLocaleDateString()}</p>
                
                <div className="mt-4">
                  <h4 className="font-medium">Items</h4>
                  <ul className="mt-2 space-y-2">
                    {invoice.items.map((item: any, index: number) => (
                      <li key={index} className="flex justify-between">
                        <span>{item.quantity} x {item.description}</span>
                        <span>${(item.quantity * item.unitPrice).toFixed(2)}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="mt-4 flex justify-between font-medium">
                  <span>Total:</span>
                  <span>${invoice.items.reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0).toFixed(2)}</span>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="gra">
              <GraInvoiceSubmission 
                invoice={invoice} 
                onSuccess={handleSuccess} 
                onError={handleError} 
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      {graResponse && (
        <Card>
          <CardHeader>
            <CardTitle>GRA E-VAT Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium">Invoice Details</h3>
                <p className="text-sm">Invoice Number: {graResponse.response.mesaage.num}</p>
                <p className="text-sm">Date: {graResponse.response.mesaage.ysdctime}</p>
                <p className="text-sm">Signature: {graResponse.response.mesaage.ysdcregsig}</p>
                <p className="text-sm">SDC ID: {graResponse.response.mesaage.ysdcid}</p>
                <p className="text-sm">Record Number: {graResponse.response.mesaage.ysdcrecnum}</p>
              </div>
              
              <div>
                <h3 className="font-medium">QR Code</h3>
                {graResponse.response.qr_code ? (
                  <img 
                    src={graResponse.response.qr_code} 
                    alt="GRA QR Code" 
                    className="mt-2 max-w-[150px] border border-gray-200 rounded"
                  />
                ) : (
                  <p className="text-sm text-gray-500">No QR code available</p>
                )}
              </div>
            </div>
            
            <div className="mt-4">
              <Button variant="outline" onClick={() => window.open(graResponse.response.qr_code, '_blank')}>
                Verify Invoice
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
