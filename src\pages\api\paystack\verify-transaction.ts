import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { verifyPayment } from '@/services/serverPaystackService';

/**
 * API endpoint to securely verify Paystack transactions
 * 
 * This endpoint handles the verification of Paystack transactions using the secret key
 * which should never be exposed to the client.
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // Create authenticated Supabase client
  const supabase = createServerSupabaseClient({ req, res });
  
  // Check if user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();
  
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    const { reference, subscriptionId } = req.body;
    
    if (!reference) {
      return res.status(400).json({ error: 'Transaction reference is required' });
    }
    
    // Verify the transaction using the server-side service
    const verificationResult = await verifyPayment(reference, session.user.id, subscriptionId);
    
    // Return the verification result
    return res.status(200).json(verificationResult);
  } catch (error) {
    console.error('Error verifying Paystack transaction:', error);
    return res.status(500).json({ 
      error: 'An error occurred while verifying the transaction',
      message: error.message 
    });
  }
}
