import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Filter,
  MoreHorizontal,
  Download,
  Eye,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
import { useSession } from '@/contexts/SessionContext';
import { supabase } from '@/integrations/supabase/client';
import { isAdmin, isAdminEmail } from '@/utils/adminUtils';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Payment {
  id: string;
  invoice_id: string;
  user_id: string;
  user_email: string;
  amount: number;
  currency: string;
  status: 'successful' | 'pending' | 'failed';
  payment_method: string;
  reference: string;
  created_at: string;
  updated_at: string;
  transaction_id?: string;
  payment_gateway?: string;
  metadata?: any;
}

const PaymentsPage = () => {
  const navigate = useNavigate();
  const { session, user } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    const checkAdminAndFetchPayments = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!session || !user) {
          setIsLoading(false);
          setError('You must be logged in to access the admin dashboard.');
          return;
        }

        const userId = session.user.id;
        const userEmail = user.email || '';

        // Check if user is admin
        try {
          const adminStatus = await isAdmin(userId, userEmail);

          if (adminStatus) {
            setIsAuthorized(true);
            fetchPayments();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            // Use a timeout to avoid immediate navigation which can cause issues
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        } catch (adminError) {
          console.error('Error checking admin status:', adminError);

          // Fallback to just checking email if there's an error with the full check
          if (isAdminEmail(userEmail)) {
            setIsAuthorized(true);
            fetchPayments();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            // Use a timeout to avoid immediate navigation which can cause issues
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error in admin verification:', error);
        setIsAuthorized(false);
        setError('An error occurred while verifying admin permissions.');
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAndFetchPayments();
  }, [session, user, navigate]);

  const fetchPayments = async () => {
    try {
      // Try to fetch from invoice_payments table first
      try {
        const { data: invoicePaymentsData, error: invoicePaymentsError } = await supabase
          .from('invoice_payments')
          .select(`
            *,
            invoices (id, user_id),
            profiles:invoices.user_id (email)
          `);

        if (!invoicePaymentsError && invoicePaymentsData && invoicePaymentsData.length > 0) {
          // Transform the data
          const transformedPayments = invoicePaymentsData.map(payment => ({
            id: payment.id,
            invoice_id: payment.invoice_id,
            user_id: payment.invoices?.user_id || '',
            user_email: payment.profiles?.email || '',
            amount: payment.amount || 0,
            currency: payment.currency || 'GHS',
            status: payment.status || 'pending',
            payment_method: payment.payment_method || 'card',
            reference: payment.reference || payment.transaction_id || '',
            transaction_id: payment.transaction_id || '',
            payment_gateway: payment.payment_gateway || 'Paystack',
            created_at: payment.created_at || '',
            updated_at: payment.updated_at || '',
            metadata: payment.metadata || {}
          }));

          setPayments(transformedPayments);
          return;
        }
      } catch (error) {
        console.error('Error fetching from invoice_payments:', error);
      }

      // If invoice_payments doesn't work, try subscription_transactions
      try {
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscription_transactions')
          .select(`
            *,
            profiles (email)
          `);

        if (!subscriptionError && subscriptionData && subscriptionData.length > 0) {
          // Transform the data
          const transformedPayments = subscriptionData.map(payment => ({
            id: payment.id,
            invoice_id: payment.subscription_id || '',
            user_id: payment.user_id || '',
            user_email: payment.profiles?.email || '',
            amount: payment.amount || 0,
            currency: payment.currency || 'GHS',
            status: payment.status || 'pending',
            payment_method: payment.payment_method || 'card',
            reference: payment.reference || payment.transaction_id || '',
            transaction_id: payment.transaction_id || '',
            payment_gateway: payment.payment_gateway || 'Paystack',
            created_at: payment.created_at || '',
            updated_at: payment.updated_at || '',
            metadata: payment.metadata || {}
          }));

          setPayments(transformedPayments);
          return;
        }
      } catch (error) {
        console.error('Error fetching from subscription_transactions:', error);
      }

      // If no payment data found in any table
      setPayments([]);
      setError('No payment data found in the database. This is normal if you haven\'t processed any payments yet.');

    } catch (error) {
      console.error('Error fetching payments:', error);
      setError('An error occurred while fetching payment data.');
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const filteredPayments = payments.filter(payment => {
    if (activeTab !== 'all' && payment.status !== activeTab) {
      return false;
    }

    const query = searchQuery.toLowerCase();
    return (
      payment.user_email.toLowerCase().includes(query) ||
      payment.reference.toLowerCase().includes(query) ||
      payment.invoice_id.toLowerCase().includes(query) ||
      payment.payment_method.toLowerCase().includes(query)
    );
  });

  const handleViewPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsViewDialogOpen(true);
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100); // Assuming amount is in cents
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'successful':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Successful
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Unknown
          </Badge>
        );
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-red-500 mb-4">{error}</div>
          <Button onClick={() => window.history.back()}>Go Back</Button>
        </div>
      </AdminLayout>
    );
  }

  if (!isAuthorized) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-red-500 mb-4">You do not have permission to access this page.</div>
          <Button onClick={() => navigate('/dashboard')}>Go to Dashboard</Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-5">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold tracking-tight">Payments Management</h1>
          <Button onClick={fetchPayments}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Payments</CardTitle>
            <CardDescription>View and manage all payment transactions.</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="mb-4" onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all">All Payments</TabsTrigger>
                <TabsTrigger value="successful">Successful</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="failed">Failed</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex items-center justify-between mb-4">
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search payments..."
                  className="pl-8 w-full"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
              <div className="flex items-center gap-2">
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Methods</SelectItem>
                    <SelectItem value="card">Card</SelectItem>
                    <SelectItem value="bank">Bank Transfer</SelectItem>
                    <SelectItem value="mobile_money">Mobile Money</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Reference</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPayments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4 text-muted-foreground">
                        No payments found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredPayments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">{payment.reference}</TableCell>
                        <TableCell>{payment.user_email}</TableCell>
                        <TableCell>{formatCurrency(payment.amount, payment.currency)}</TableCell>
                        <TableCell>{getStatusBadge(payment.status)}</TableCell>
                        <TableCell className="capitalize">{payment.payment_method}</TableCell>
                        <TableCell>{new Date(payment.created_at).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewPayment(payment)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => navigate(`/admin/invoices/${payment.invoice_id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Invoice
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                Download Receipt
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {filteredPayments.length} of {payments.length} payments
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" disabled>
                Next
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* View Payment Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Payment Details</DialogTitle>
            <DialogDescription>
              Detailed information about this payment.
            </DialogDescription>
          </DialogHeader>
          {selectedPayment && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Reference</h3>
                  <p className="text-sm">{selectedPayment.reference}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                  <p className="text-sm">{getStatusBadge(selectedPayment.status)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Amount</h3>
                  <p className="text-sm">{formatCurrency(selectedPayment.amount, selectedPayment.currency)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Payment Method</h3>
                  <p className="text-sm capitalize">{selectedPayment.payment_method}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">User</h3>
                  <p className="text-sm">{selectedPayment.user_email}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Invoice ID</h3>
                  <p className="text-sm">{selectedPayment.invoice_id}</p>
                </div>
                {selectedPayment.transaction_id && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Transaction ID</h3>
                    <p className="text-sm">{selectedPayment.transaction_id}</p>
                  </div>
                )}
                {selectedPayment.payment_gateway && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Payment Gateway</h3>
                    <p className="text-sm">{selectedPayment.payment_gateway}</p>
                  </div>
                )}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Created At</h3>
                  <p className="text-sm">{new Date(selectedPayment.created_at).toLocaleString()}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Updated At</h3>
                  <p className="text-sm">{new Date(selectedPayment.updated_at).toLocaleString()}</p>
                </div>
                {selectedPayment.metadata && Object.keys(selectedPayment.metadata).length > 0 && (
                  <div className="col-span-2">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Metadata</h3>
                    <div className="text-xs bg-muted p-2 rounded overflow-auto max-h-24">
                      <pre>{JSON.stringify(selectedPayment.metadata, null, 2)}</pre>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          <DialogFooter className="sm:justify-start">
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Download Receipt
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default PaymentsPage;
