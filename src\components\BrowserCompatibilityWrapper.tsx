import React, { useState, useEffect, ReactNode } from 'react';
import BrowserFallback from './BrowserFallback';
import browserCompatibility from '../utils/browserCompatibility';

interface BrowserCompatibilityWrapperProps {
  children: ReactNode;
}

/**
 * A wrapper component that checks for browser compatibility issues
 * and renders a fallback component if needed
 */
const BrowserCompatibilityWrapper: React.FC<BrowserCompatibilityWrapperProps> = ({ children }) => {
  const [hasCompatibilityIssue, setHasCompatibilityIssue] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      // Check browser compatibility
      const { compatible, issues } = browserCompatibility.checkBrowserCompatibility();

      if (!compatible) {
        console.warn('Browser compatibility issues detected:', issues);
        setHasCompatibilityIssue(true);
        setIsLoading(false);
        return;
      }

      // Apply fixes for Chrome on iOS
      if (browserCompatibility.isChromeIOS()) {
        browserCompatibility.applyChromeIOSFixes();

        // Load Chrome iOS specific CSS if not already loaded
        if (!document.getElementById('chrome-ios-fixes')) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = '/chrome-ios-fixes.css';
          link.id = 'chrome-ios-fixes';
          document.head.appendChild(link);
        }
      }

      // Check if the app renders properly
      const checkRendering = () => {
        // If the root element is empty after a delay, we have a rendering issue
        const rootElement = document.getElementById('root');
        if (rootElement && (!rootElement.children || rootElement.children.length === 0)) {
          console.warn('App failed to render properly');
          setHasCompatibilityIssue(true);
        }
        setIsLoading(false);
      };

      // Check after a short delay to allow the app to render
      const timeout = setTimeout(checkRendering, 2000);

      return () => {
        clearTimeout(timeout);
      };
    } catch (error) {
      console.error('Error in BrowserCompatibilityWrapper:', error);
      setHasCompatibilityIssue(true);
      setIsLoading(false);
    }
  }, []);

  // Show loading state
  if (isLoading) {
    return null; // Or a loading spinner if preferred
  }

  // Show fallback if there are compatibility issues
  if (hasCompatibilityIssue) {
    return <BrowserFallback />;
  }

  // Otherwise, render the children
  return <>{children}</>;
};

export default BrowserCompatibilityWrapper;
