-- Emergency fix for invoice status issues
-- This script focuses only on fixing the immediate issues

-- 1. Temporarily disable <PERSON><PERSON> to fix the data
ALTER TABLE invoices DISABLE ROW LEVEL SECURITY;

-- 2. Check if paid_at column exists and add it if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'invoices'
        AND column_name = 'paid_at'
    ) THEN
        ALTER TABLE invoices ADD COLUMN paid_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- 3. Fix invoices that were sent to GRA but are showing as draft
UPDATE invoices
SET status = 'sent'
WHERE status = 'draft'
AND (
    (gra_response IS NOT NULL AND gra_response->>'status' = 'success' AND gra_response->>'invoiceStatusCode' = '02')
    OR
    (gra_invoice_id IS NOT NULL AND gra_invoice_id != '')
);

-- 4. Create a minimal secure function for updating invoice status
CREATE OR REPLACE FUNCTION public.minimal_update_invoice_status(
    p_invoice_id UUID,
    p_status TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    result JSONB;
BEGIN
    -- Update the invoice status with minimal fields to avoid errors
    UPDATE invoices
    SET 
        status = p_status,
        updated_at = NOW()
    WHERE id = p_invoice_id
    RETURNING to_jsonb(invoices.*) INTO result;
    
    RETURN result;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.minimal_update_invoice_status(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.minimal_update_invoice_status(UUID, TEXT) TO anon;

-- 5. Re-enable RLS
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- 6. Create a minimal policy for users to update their own invoices
DROP POLICY IF EXISTS "Users can update their own invoices" ON "invoices";
CREATE POLICY "Users can update their own invoices" ON "invoices"
FOR UPDATE
USING (
    auth.uid() = user_id
);

-- 7. Create a minimal policy for public access to invoices with valid tokens
DROP POLICY IF EXISTS "Allow public access to invoices with valid token" ON "invoices";
CREATE POLICY "Allow public access to invoices with valid token" ON "invoices"
FOR SELECT
USING (
    public_access_token IS NOT NULL
    AND (
        public_access_expires_at IS NULL
        OR public_access_expires_at > NOW()
    )
);
