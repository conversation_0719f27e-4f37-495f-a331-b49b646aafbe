
import React, { useRef, useEffect } from 'react';
import { Star, Quote } from 'lucide-react';

// Assuming we're using the useInView hook from framer-motion
// If framer-motion installation failed, this component will still work without animations
let useInView: any;
let motion: any;
try {
  // Try to import from framer-motion
  const framerMotion = require('framer-motion');
  useInView = framerMotion.useInView;
  motion = framerMotion.motion;

  // Make sure we're using the create method for custom components
  if (!motion.create && typeof motion === 'function') {
    const originalMotion = motion;
    motion = {
      ...framerMotion.motion,
      create: originalMotion
    };
  }
} catch (error) {
  // Fallback if framer-motion is not available
  useInView = () => ({ ref: useRef(), isInView: true });
  motion = {
    div: (props: any) => <div {...props} />,
    h2: (props: any) => <h2 {...props} />,
    p: (props: any) => <p {...props} />,
    create: (Component: any) => (props: any) => <Component {...props} />
  };
}

// Testimonial card component with animation
const TestimonialCard = ({ testimonial, index }: any) => {
  const ref = useRef(null);
  const isInView = useInView ? useInView(ref, { once: true, amount: 0.3 }) : true;

  return (
    <div
      ref={ref}
      className={`bg-white/20 backdrop-blur-md rounded-xl p-8 border border-white/30 shadow-xl transition-all duration-500 hover:shadow-2xl hover:-translate-y-1 hover:bg-white/25 relative overflow-hidden ${
        isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      }`}
      style={{
        transitionDelay: `${index * 150}ms`,
        transform: isInView ? 'translateY(0)' : 'translateY(20px)'
      }}
    >
      {/* Background decorative elements */}
      <div className="absolute -top-10 -right-10 w-20 h-20 bg-ghana-gold/10 rounded-full blur-xl"></div>
      <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-white/5 rounded-full blur-xl"></div>

      <div className="relative z-10 text-shadow">
        <div className="flex justify-between items-start mb-6">
          <Quote className="h-10 w-10 text-ghana-gold" fill="rgba(255,215,0,0.2)" />
          <div className="flex">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star key={star} className="h-4 w-4 text-ghana-gold fill-ghana-gold" />
            ))}
          </div>
        </div>

        <p className="mb-8 text-lg leading-relaxed font-medium text-white">{testimonial.quote}</p>

        <div className="flex items-center">
          <div className="w-12 h-12 rounded-full bg-ghana-gold/20 flex items-center justify-center mr-4 overflow-hidden">
            <img
              src={`https://ui-avatars.com/api/?name=${encodeURIComponent(testimonial.author)}&background=FCD116&color=000&size=60`}
              alt={testimonial.author}
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <h4 className="font-semibold text-white">{testimonial.author}</h4>
            <p className="text-ghana-gold-light text-sm">{testimonial.role}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const Testimonial: React.FC = () => {
  const ref = useRef(null);
  const isInView = useInView ? useInView(ref, { once: true, amount: 0.1 }) : true;

  const testimonials = [
    {
      quote: "Payvoicer has simplified our entire billing process. The GRA compliance feature has saved us countless hours of paperwork and ensures we're always up to date with regulations.",
      author: "Akosua Mensah",
      role: "CEO, Accra Digital Solutions"
    },
    {
      quote: "As a freelancer, I needed an affordable way to create professional invoices. This platform is perfect - easy to use, fully compliant, and helps me get paid faster!",
      author: "Kwame Osei",
      role: "Freelance Graphic Designer"
    },
    {
      quote: "The real-time tax calculations and direct submission to GRA has transformed how we handle our finances. The customer support team is also incredibly responsive!",
      author: "Abena Poku",
      role: "Finance Director, TechGhana Ltd"
    }
  ];

  return (
    <section className="py-20 md:py-28 bg-gradient-to-b from-ghana-green via-ghana-green to-[#004d2e] text-white relative overflow-hidden">
      {/* Abstract pattern for background */}
      <div className="absolute inset-0 ghana-pattern opacity-10 z-0"></div>

      {/* Dark overlay for better contrast */}
      <div className="absolute inset-0 bg-black/15 z-0"></div>

      {/* Animated background elements */}
      <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-black/20 to-transparent z-0"></div>
      <div className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-black/20 to-transparent z-0"></div>

      {/* Decorative elements */}
      <div className="absolute top-20 right-20 w-32 h-32 bg-ghana-gold/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 left-20 w-40 h-40 bg-white/5 rounded-full blur-3xl animate-pulse"></div>

      <div className="container relative z-10">
        <div className="max-w-5xl mx-auto">
          <div
            ref={ref}
            className={`text-center mb-16 transition-all duration-700 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="inline-block bg-white/10 px-4 py-1.5 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-sm font-semibold text-ghana-gold-light flex items-center">
                <Star className="h-4 w-4 mr-1.5 fill-ghana-gold" />
                Customer Testimonials
              </span>
            </div>

            <h2 className="text-3xl md:text-4xl font-bold font-display mb-4 text-shadow">
              Trusted by Businesses Across Ghana
            </h2>
            <p className="text-white text-lg max-w-2xl mx-auto">
              See what our customers are saying about our invoice solution and how it's helping them streamline their business operations.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={index} testimonial={testimonial} index={index} />
            ))}
          </div>

          <div
            className={`mt-16 text-center transition-all duration-700 delay-500 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <a
              href="/testimonials"
              className="inline-flex items-center px-6 py-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full text-white font-medium transition-all duration-300 hover:shadow-lg border border-white/30 text-shadow"
            >
              View More Success Stories
              <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonial;
