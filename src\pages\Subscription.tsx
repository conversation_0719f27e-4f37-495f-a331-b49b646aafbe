import React from 'react';
import { <PERSON> } from 'react-router-dom';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import SubscriptionManagement from '@/components/SubscriptionManagement';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, CreditCard, Receipt, Clock, Shield, Lock, ArrowRight } from 'lucide-react';

// Pricing plan details
const PRICING_PLANS = [
  {
    name: 'Free',
    price: 'Free',
    description: 'For individuals just getting started',
    features: [
      '5 invoices per month',
      '3 clients',
      'Basic invoicing',
      'Dashboard access'
    ],
    color: 'bg-gray-100 border-gray-200',
    buttonVariant: 'outline' as const,
    buttonText: 'Current Plan'
  },
  {
    name: 'Freelancer',
    price: '₵49',
    period: '/month',
    description: 'For freelancers and small businesses',
    features: [
      '20 invoices per month',
      '10 clients',
      'Invoice customization',
      'Email notifications'
    ],
    color: 'bg-blue-50 border-blue-200',
    buttonVariant: 'default' as const,
    buttonText: 'Upgrade'
  },
  {
    name: 'Business',
    price: '₵99',
    period: '/month',
    description: 'For growing businesses',
    features: [
      '50 invoices per month',
      '50 clients',
      'Team members (1)',
      'Basic reports'
    ],
    color: 'bg-purple-50 border-purple-200',
    buttonVariant: 'default' as const,
    buttonText: 'Upgrade',
    popular: true
  },
  {
    name: 'Enterprise',
    price: '₵199',
    period: '/month',
    description: 'For large organizations',
    features: [
      'Unlimited invoices',
      'Unlimited clients',
      'Team members (5)',
      'Advanced reports',
      'API access',
      'Priority support'
    ],
    color: 'bg-green-50 border-green-200',
    buttonVariant: 'default' as const,
    buttonText: 'Upgrade'
  }
];

const SubscriptionPage: React.FC = () => {
  return (
    <DashboardLayout>
      <div className="container mx-auto py-6 space-y-8">
        <div>
          <h1 className="text-3xl font-bold">Subscription Management</h1>
          <p className="text-gray-500 mt-2">
            Manage your subscription plan and billing details
          </p>
        </div>

        <Tabs defaultValue="current">
          <TabsList className="mb-6 w-full grid grid-cols-3">
            <TabsTrigger value="current" className="text-xs sm:text-sm">Current Plan</TabsTrigger>
            <TabsTrigger value="plans" className="text-xs sm:text-sm">Available Plans</TabsTrigger>
            <TabsTrigger value="billing" className="text-xs sm:text-sm">Billing History</TabsTrigger>
          </TabsList>

          {/* Current Plan Tab */}
          <TabsContent value="current" className="space-y-6">
            <SubscriptionManagement />

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lock className="h-5 w-5 text-ghana-green" />
                  Enhanced Subscription Management
                </CardTitle>
                <CardDescription>
                  Use our secure subscription management system with enhanced security features
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-500">
                  Our secure subscription management system provides enhanced security features to protect your subscription from unauthorized changes and payment gateway bypass attempts.
                </p>
                <Link to="/subscription/manage" className="block w-full sm:w-auto">
                  <Button className="bg-ghana-green hover:bg-ghana-green/90 w-full sm:w-auto">
                    <span className="whitespace-nowrap">Secure Subscription Management</span>
                    <ArrowRight className="ml-2 h-4 w-4 flex-shrink-0" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-ghana-green" />
                  Need Help?
                </CardTitle>
                <CardDescription>
                  Contact our support team if you have any questions about your subscription
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full sm:w-auto">Contact Support</Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Available Plans Tab */}
          <TabsContent value="plans">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {PRICING_PLANS.map((plan, index) => (
                <Card
                  key={index}
                  className={`relative ${plan.color} ${plan.popular ? 'border-2 border-ghana-green' : ''}`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-ghana-green text-white text-xs px-3 py-1 rounded-full">
                      Most Popular
                    </div>
                  )}
                  <CardHeader>
                    <CardTitle>{plan.name}</CardTitle>
                    <div className="mt-2">
                      <span className="text-3xl font-bold">{plan.price}</span>
                      {plan.period && <span className="text-gray-500">{plan.period}</span>}
                    </div>
                    <CardDescription className="mt-2">{plan.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <ul className="space-y-2">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-ghana-green" />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <Button
                      variant={plan.buttonVariant}
                      className="w-full mt-4"
                      disabled={plan.name === 'Free' && plan.buttonText === 'Current Plan'}
                    >
                      {plan.buttonText}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Billing History Tab */}
          <TabsContent value="billing">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Receipt className="h-5 w-5" />
                  Billing History
                </CardTitle>
                <CardDescription>
                  View your past invoices and payment history
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Desktop View */}
                <div className="rounded-md border hidden sm:block">
                  <div className="bg-gray-50 p-4 text-sm font-medium grid grid-cols-5 gap-4">
                    <div>Date</div>
                    <div>Description</div>
                    <div>Amount</div>
                    <div>Status</div>
                    <div>Actions</div>
                  </div>
                  <div className="p-4 text-sm grid grid-cols-5 gap-4 border-t">
                    <div className="text-gray-500">No billing history available</div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                </div>

                {/* Mobile View */}
                <div className="sm:hidden text-center py-6">
                  <p className="text-gray-500">No billing history available</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default SubscriptionPage;
