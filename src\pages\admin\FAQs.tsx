import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  HelpCircle,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { useSession } from '@/contexts/SessionContext';
import { supabase } from '@/integrations/supabase/client';
import { isAdmin, isAdminEmail } from '@/utils/adminUtils';
import { getTableColumns, createFAQInsert } from '@/utils/databaseUtils';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
  order_index?: number;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

interface FAQFormData {
  question: string;
  answer: string;
  category: string;
  is_published: boolean;
}

const FAQsAdminPage = () => {
  const navigate = useNavigate();
  const { session, user } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFaq, setSelectedFaq] = useState<FAQ | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [formData, setFormData] = useState<FAQFormData>({
    question: '',
    answer: '',
    category: '',
    is_published: false
  });

  useEffect(() => {
    const checkAdminAndFetchFAQs = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!session || !user) {
          setIsLoading(false);
          setError('You must be logged in to access the admin dashboard.');
          return;
        }

        const userId = session.user.id;
        const userEmail = user.email || '';

        // Check if user is admin
        try {
          const adminStatus = await isAdmin(userId, userEmail);

          if (adminStatus) {
            setIsAuthorized(true);
            fetchFAQs();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        } catch (adminError) {
          console.error('Error checking admin status:', adminError);

          if (isAdminEmail(userEmail)) {
            setIsAuthorized(true);
            fetchFAQs();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error in admin verification:', error);
        setIsAuthorized(false);
        setError('An error occurred while verifying admin permissions.');
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAndFetchFAQs();
  }, [session, user, navigate]);

  const fetchFAQs = async () => {
    try {
      const { data: faqsData, error: faqsError } = await supabase
        .from('faqs')
        .select('*')
        .order('order_index', { ascending: true });

      if (faqsError) {
        console.error('Error fetching FAQs:', faqsError);
        setError('Error fetching FAQs.');
        return;
      }

      const transformedFAQs = faqsData?.map(faq => ({
        id: faq.id,
        question: faq.question || '',
        answer: faq.answer || '',
        category: faq.category || '',
        order_index: faq.order_index || 0,
        is_published: faq.is_published || false,
        created_at: faq.created_at || '',
        updated_at: faq.updated_at || ''
      })) || [];

      setFaqs(transformedFAQs);
    } catch (error) {
      console.error('Error fetching FAQs:', error);
      setError('An error occurred while fetching FAQs.');
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.category?.toLowerCase().includes(searchQuery.toLowerCase());

    if (activeTab === 'all') return matchesSearch;
    if (activeTab === 'published') return matchesSearch && faq.is_published;
    if (activeTab === 'draft') return matchesSearch && !faq.is_published;
    return matchesSearch;
  });

  const getStatusBadge = (isPublished: boolean) => {
    return (
      <Badge variant={isPublished ? 'default' : 'secondary'}>
        {isPublished ? 'Published' : 'Draft'}
      </Badge>
    );
  };

  const moveItem = async (index: number, direction: 'up' | 'down') => {
    const newFaqs = [...faqs];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;

    if (targetIndex >= 0 && targetIndex < newFaqs.length) {
      [newFaqs[index], newFaqs[targetIndex]] = [newFaqs[targetIndex], newFaqs[index]];

      // Update order_index in database
      try {
        const updates = [
          { id: newFaqs[index].id, order_index: index },
          { id: newFaqs[targetIndex].id, order_index: targetIndex }
        ];

        for (const update of updates) {
          await supabase
            .from('faqs')
            .update({ order_index: update.order_index })
            .eq('id', update.id);
        }

        setFaqs(newFaqs);
      } catch (error) {
        console.error('Error updating FAQ order:', error);
        setError('Error updating FAQ order.');
      }
    }
  };

  const createFAQ = async () => {
    try {
      // Get the actual table columns
      const tableColumns = await getTableColumns('faqs');
      console.log('Available faqs columns:', tableColumns);

      if (tableColumns.length === 0) {
        setError('Unable to determine table structure. Please check if the faqs table exists.');
        return;
      }

      // Create insert data using the utility function
      const insertData = createFAQInsert(formData, tableColumns, faqs.length);
      console.log('FAQ insert data:', insertData);

      const { error } = await supabase
        .from('faqs')
        .insert(insertData);

      if (error) {
        console.error('Error creating FAQ:', error);
        setError(`Error creating FAQ: ${error.message}`);
        return;
      }

      setFormData({
        question: '',
        answer: '',
        category: '',
        is_published: false
      });
      setIsCreateDialogOpen(false);
      fetchFAQs();
    } catch (error) {
      console.error('Error creating FAQ:', error);
      setError('An error occurred while creating the FAQ.');
    }
  };

  const deleteFAQ = async (id: string) => {
    try {
      const { error } = await supabase
        .from('faqs')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting FAQ:', error);
        setError('Error deleting FAQ.');
        return;
      }

      setIsDeleteDialogOpen(false);
      setSelectedFaq(null);
      fetchFAQs();
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      setError('An error occurred while deleting the FAQ.');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!isAuthorized) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-5">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">FAQ Management</h1>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add FAQ
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
            <CardDescription>
              Manage your FAQ content to help users find answers to common questions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <div className="flex items-center justify-between">
                <TabsList>
                  <TabsTrigger value="all">All FAQs</TabsTrigger>
                  <TabsTrigger value="published">Published</TabsTrigger>
                  <TabsTrigger value="draft">Drafts</TabsTrigger>
                </TabsList>
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search FAQs..."
                    value={searchQuery}
                    onChange={handleSearch}
                    className="w-64"
                  />
                </div>
              </div>

              <TabsContent value={activeTab} className="space-y-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Question</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Order</TableHead>
                        <TableHead>Updated</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredFAQs.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            No FAQs found.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredFAQs.map((faq, index) => (
                          <TableRow key={faq.id}>
                            <TableCell className="font-medium">
                              <div>
                                <div className="flex items-center">
                                  <HelpCircle className="mr-2 h-4 w-4" />
                                  {faq.question}
                                </div>
                                <div className="text-sm text-muted-foreground truncate max-w-xs">
                                  {faq.answer.substring(0, 100)}...
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {faq.category ? (
                                <Badge variant="outline">{faq.category}</Badge>
                              ) : (
                                <span className="text-muted-foreground">Uncategorized</span>
                              )}
                            </TableCell>
                            <TableCell>{getStatusBadge(faq.is_published)}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={() => moveItem(index, 'up')}
                                  disabled={index === 0}
                                >
                                  <ArrowUp className="h-3 w-3" />
                                </Button>
                                <span className="text-sm">{index + 1}</span>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={() => moveItem(index, 'down')}
                                  disabled={index === filteredFAQs.length - 1}
                                >
                                  <ArrowDown className="h-3 w-3" />
                                </Button>
                              </div>
                            </TableCell>
                            <TableCell>
                              {new Date(faq.updated_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedFaq(faq);
                                    setIsViewDialogOpen(true);
                                  }}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedFaq(faq);
                                    setIsEditDialogOpen(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedFaq(faq);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Create FAQ Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New FAQ</DialogTitle>
            <DialogDescription>
              Add a new frequently asked question to help your users.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="question">Question</Label>
              <Input
                id="question"
                placeholder="Enter the question..."
                value={formData.question}
                onChange={(e) => setFormData(prev => ({ ...prev, question: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="category">Category</Label>
              <Input
                id="category"
                placeholder="e.g., Billing, Features, Support..."
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="answer">Answer</Label>
              <Textarea
                id="answer"
                placeholder="Provide a detailed answer..."
                className="min-h-[150px]"
                value={formData.answer}
                onChange={(e) => setFormData(prev => ({ ...prev, answer: e.target.value }))}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is_published"
                checked={formData.is_published}
                onChange={(e) => setFormData(prev => ({ ...prev, is_published: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <Label htmlFor="is_published">Publish immediately</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsCreateDialogOpen(false);
              setFormData({
                question: '',
                answer: '',
                category: '',
                is_published: false
              });
            }}>
              Cancel
            </Button>
            <Button onClick={createFAQ} disabled={!formData.question || !formData.answer}>
              Create FAQ
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View FAQ Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>FAQ Details</DialogTitle>
          </DialogHeader>
          {selectedFaq && (
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-2">Question</h3>
                <p className="text-sm bg-muted p-3 rounded">{selectedFaq.question}</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Answer</h3>
                <div className="prose prose-sm max-w-none">
                  <p className="whitespace-pre-wrap">{selectedFaq.answer}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-1">Category</h4>
                  <p className="text-sm">{selectedFaq.category || 'Uncategorized'}</p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Status</h4>
                  <p className="text-sm">{getStatusBadge(selectedFaq.is_published)}</p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete FAQ</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedFaq?.question}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedFaq && deleteFAQ(selectedFaq.id)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default FAQsAdminPage;
