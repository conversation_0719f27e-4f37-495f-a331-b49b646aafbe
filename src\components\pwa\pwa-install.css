/* PWA Install Button Styles */

/* Container to ensure the button is always visible */
.pwa-install-container {
  position: static;
  display: block;
  width: 0;
  height: 0;
  overflow: visible;
}

/* iOS Chrome specific fixes */
@supports (-webkit-touch-callout: none) {
  #universal-install-button {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 999999 !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

.pwa-install-button {
  position: fixed !important;
  bottom: 1.5rem !important;
  right: 1.5rem !important;
  z-index: 999999 !important; /* Ultra high z-index */
  transform-origin: bottom right;
  will-change: transform;
  transition: transform 0.2s ease;
  pointer-events: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  /* iOS-specific fixes */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
  -webkit-perspective: 1000px !important;
  perspective: 1000px !important;
}

.pwa-install-button button,
.pwa-install-fab {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  padding: 0.75rem 1.25rem !important;
  border-radius: 9999px !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 166, 81, 0.25) !important;
  transition: all 0.2s ease !important;
  animation: pulse 2s infinite !important;
  min-width: 3.5rem !important;
  min-height: 3.5rem !important;
  border: none !important;
  outline: none !important;
  cursor: pointer !important;
  -webkit-tap-highlight-color: transparent !important; /* Remove tap highlight on mobile */
  -webkit-appearance: none !important; /* Fix for iOS */
  appearance: none !important;
  backface-visibility: hidden !important; /* Prevent flickering on transform */
  /* iOS-specific fixes */
  transform: translateZ(0) !important;
  -webkit-transform: translateZ(0) !important;
  background-color: #00A651 !important; /* Ensure background color is explicitly set */
  color: white !important; /* Ensure text color is explicitly set */
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 166, 81, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 166, 81, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 166, 81, 0);
  }
}

.pwa-install-button button:hover,
.pwa-install-fab:hover {
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25), 0 15px 30px rgba(0, 166, 81, 0.3) !important;
  animation: none !important;
}

.pwa-install-button button:active,
.pwa-install-fab:active {
  transform: translateY(0) scale(0.95) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 166, 81, 0.2) !important;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .pwa-install-button button,
  .pwa-install-fab {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), 0 8px 20px rgba(255, 215, 0, 0.15) !important;
    animation: pulse-dark 2s infinite !important;
    background-color: #FFD700 !important; /* Ghana gold */
    color: #1f2937 !important; /* Dark text */
  }

  @keyframes pulse-dark {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
  }

  .pwa-install-button button:hover,
  .pwa-install-fab:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3), 0 15px 30px rgba(255, 215, 0, 0.2) !important;
    animation: none !important;
  }

  .pwa-install-button button:active,
  .pwa-install-fab:active {
    transform: translateY(0) scale(0.95) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 5px 15px rgba(255, 215, 0, 0.1) !important;
  }
}

/* Touch device feedback */
@media (hover: none) {
  .pwa-install-button button:active,
  .pwa-install-fab:active {
    transform: scale(0.95) !important;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .pwa-install-button {
    bottom: 1.25rem !important;
    right: 1.25rem !important;
  }

  .pwa-install-button button,
  .pwa-install-fab {
    padding: 0.75rem !important;
    min-width: 3.5rem !important;
    min-height: 3.5rem !important;
    width: 3.5rem !important;
    height: 3.5rem !important;
  }

  .pwa-install-button button span,
  .pwa-install-fab span {
    display: none !important;
  }

  .pwa-install-button button svg,
  .pwa-install-fab svg {
    margin: 0 !important;
    width: 1.5rem !important;
    height: 1.5rem !important;
  }
}

/* Ensure proper spacing from footer */
@media (max-height: 500px) {
  .pwa-install-button {
    bottom: 1rem !important;
  }
}

/* Installation instructions modal */
.pwa-install-modal-header {
  background: linear-gradient(135deg, #00A651, rgba(0, 166, 81, 0.8));
  padding: 1.25rem;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pwa-install-step {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.pwa-install-step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  font-weight: 700;
  flex-shrink: 0;
}

.pwa-install-step-content {
  flex: 1;
}

.pwa-install-step-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.pwa-install-step-description {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Platform-specific styles */
.pwa-install-ios .pwa-install-step-number {
  background-color: #e0f2fe;
  color: #0284c7;
}

/* Chrome on iOS */
.pwa-install-ios-chrome .pwa-install-step-number {
  background-color: #e0f2fe;
  color: #0284c7;
}

.pwa-install-android .pwa-install-step-number {
  background-color: #dcfce7;
  color: #16a34a;
}

.pwa-install-desktop .pwa-install-step-number {
  background-color: #f3e8ff;
  color: #9333ea;
}

/* Dark mode for modal */
@media (prefers-color-scheme: dark) {
  .pwa-install-modal-header {
    background: linear-gradient(135deg, #FFD700, rgba(255, 215, 0, 0.8));
    color: #121212;
  }

  .pwa-install-step-description {
    color: #9ca3af;
  }

  .pwa-install-ios .pwa-install-step-number {
    background-color: #0c4a6e;
    color: #7dd3fc;
  }

  /* Chrome on iOS - dark mode */
  .pwa-install-ios-chrome .pwa-install-step-number {
    background-color: #0c4a6e;
    color: #7dd3fc;
  }

  .pwa-install-android .pwa-install-step-number {
    background-color: #14532d;
    color: #86efac;
  }

  .pwa-install-desktop .pwa-install-step-number {
    background-color: #581c87;
    color: #d8b4fe;
  }
}
