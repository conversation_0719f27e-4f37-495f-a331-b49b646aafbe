import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSession } from './SessionContext';
import { supabase } from '@/integrations/supabase/client';
import { Tables } from '@/integrations/supabase/types';
import { toast } from '@/components/ui/sonner';

// Define notification types
export type NotificationType =
  | 'invoice_created'
  | 'invoice_sent'
  | 'invoice_paid'
  | 'invoice_overdue'
  | 'payment_received'
  | 'subscription_renewal'
  | 'subscription_expiring'
  | 'system_message'
  | 'feature_update';

// Define notification interface
export interface Notification extends Tables<"in_app_notifications"> {
  id: string;
  user_id: string | null;
  organization_id: string | null;
  title: string;
  message: string;
  notification_type: NotificationType;
  link: string | null;
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

// Define notification context type
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  refreshNotifications: () => Promise<void>;
  addNotification: (notification: Omit<Notification, 'id' | 'created_at' | 'updated_at' | 'is_read'>) => Promise<void>;
}

// Create the context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Create the provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useSession();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);

  // Load notifications when user changes
  useEffect(() => {
    if (user) {
      loadNotifications();
      setupRealtimeSubscription();
    } else {
      setNotifications([]);
      setUnreadCount(0);
      setIsLoading(false);
    }

    return () => {
      // Clean up subscription
      supabase.removeAllChannels();
    };
  }, [user]);

  // Set up realtime subscription for notifications
  const setupRealtimeSubscription = () => {
    if (!user) return;

    try {
      // Check if the table exists first
      supabase
        .from('in_app_notifications')
        .select('id')
        .limit(1)
        .then(({ error }) => {
          if (error) {
            console.warn('Notification table might not be ready for realtime subscription:', error);
            return;
          }

          // Only set up subscription if table exists
          const channel = supabase
            .channel('in-app-notifications')
            .on('postgres_changes', {
              event: 'INSERT',
              schema: 'public',
              table: 'in_app_notifications',
              filter: `user_id=eq.${user.id}`
            }, (payload) => {
          try {
            // Add new notification to state
            const newNotification = payload.new as Notification;
            setNotifications(prev => [newNotification, ...prev]);
            setUnreadCount(prev => prev + 1);

            // Show toast notification
            toast(newNotification.title, {
              description: newNotification.message,
              action: newNotification.link ? {
                label: 'View',
                onClick: () => {
                  window.location.href = newNotification.link as string;
                  markAsRead(newNotification.id);
                }
              } : undefined
            });
          } catch (error) {
            console.error('Error processing new notification:', error);
          }
        })
        .on('postgres_changes', {
          event: 'UPDATE',
          schema: 'public',
          table: 'in_app_notifications',
          filter: `user_id=eq.${user.id}`
        }, (payload) => {
          try {
            // Update notification in state
            const updatedNotification = payload.new as Notification;
            setNotifications(prev =>
              prev.map(notification =>
                notification.id === updatedNotification.id ? updatedNotification : notification
              )
            );

            // Update unread count
            calculateUnreadCount();
          } catch (error) {
            console.error('Error processing updated notification:', error);
          }
        })
        .on('postgres_changes', {
          event: 'DELETE',
          schema: 'public',
          table: 'in_app_notifications',
          filter: `user_id=eq.${user.id}`
        }, (payload) => {
          try {
            // Remove notification from state
            const deletedNotificationId = payload.old.id;
            setNotifications(prev =>
              prev.filter(notification => notification.id !== deletedNotificationId)
            );

            // Update unread count
            calculateUnreadCount();
          } catch (error) {
            console.error('Error processing deleted notification:', error);
          }
        })
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log('Successfully subscribed to notifications');
          } else if (status === 'CHANNEL_ERROR') {
            console.warn('Error subscribing to notifications channel');
          }
        });
        })
        .catch(error => {
          console.error('Error checking notification table:', error);
        });
    } catch (error) {
      console.error('Error setting up notification subscription:', error);
    }
  };

  // Load notifications from database
  const loadNotifications = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Check if the table exists first to avoid errors
      const { data: tableExists, error: tableCheckError } = await supabase
        .from('in_app_notifications')
        .select('id')
        .limit(1)
        .maybeSingle();

      if (tableCheckError) {
        // Table might not exist yet or RLS policies are preventing access
        console.warn('Notification table might not be ready:', tableCheckError);
        setNotifications([]);
        setUnreadCount(0);
        setIsLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('in_app_notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('Error loading notifications:', error);
        setNotifications([]);
        return;
      }

      setNotifications(data as Notification[]);
      calculateUnreadCount();
    } catch (error) {
      console.error('Error loading notifications:', error);
      setNotifications([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate unread count
  const calculateUnreadCount = () => {
    const count = notifications.filter(notification => !notification.is_read).length;
    setUnreadCount(count);
  };

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('in_app_notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as read:', error);
        return;
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user || notifications.length === 0) return;

    try {
      const { error } = await supabase
        .from('in_app_notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return;
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      );

      // Update unread count
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Delete notification
  const deleteNotification = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('in_app_notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Error deleting notification:', error);
        return;
      }

      // Update local state
      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(notification => notification.id !== notificationId));

      // Update unread count if needed
      if (deletedNotification && !deletedNotification.is_read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // Refresh notifications
  const refreshNotifications = async () => {
    await loadNotifications();
  };

  // Add a new notification
  const addNotification = async (notification: Omit<Notification, 'id' | 'created_at' | 'updated_at' | 'is_read'>) => {
    try {
      const { data, error } = await supabase
        .from('in_app_notifications')
        .insert({
          ...notification,
          is_read: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error adding notification:', error);
        return;
      }

      // The realtime subscription will handle updating the state
    } catch (error) {
      console.error('Error adding notification:', error);
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        isLoading,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        refreshNotifications,
        addNotification
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

// Custom hook to use the notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
