import React, { useEffect, useState } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { LucideIcon } from 'lucide-react';
import PublicPageTransition from '@/components/PublicPageTransition';
import { motion, useScroll, useSpring } from 'framer-motion';

interface PageTemplateProps {
  title: string;
  subtitle: string;
  icon: LucideIcon;
  iconLabel: string;
  children: React.ReactNode;
  backgroundPattern?: boolean;
}

const PageTemplate: React.FC<PageTemplateProps> = ({
  title,
  subtitle,
  icon: Icon,
  iconLabel,
  children,
  backgroundPattern = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  
  // Scroll progress indicator
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  useEffect(() => {
    // Trigger animations after component mounts
    setIsVisible(true);
    
    // Scroll to top when page loads
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Progress bar */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-ghana-green dark:bg-ghana-gold z-50 origin-left"
        style={{ scaleX }}
      />

      <Navbar />
      <PublicPageTransition>
        <main className="flex-grow pt-16">
          {/* Hero Section */}
          <section className="relative overflow-hidden bg-gradient-to-b from-white via-gray-50 to-white dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 py-24 md:py-32">
            {/* Animated background elements */}
            {backgroundPattern && <div className="absolute inset-0 ghana-pattern opacity-10 z-0"></div>}
            <div className="absolute top-20 left-10 w-64 h-64 bg-ghana-gold/20 dark:bg-ghana-gold/30 rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 right-10 w-72 h-72 bg-ghana-green/20 dark:bg-ghana-green/30 rounded-full blur-3xl"></div>

            <div className="container relative z-10 px-4">
              <div className={`text-center max-w-4xl mx-auto transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
                <div className="inline-block bg-ghana-gold/20 dark:bg-ghana-gold/30 px-4 py-1.5 rounded-full mb-6">
                  <span className="text-sm font-semibold text-ghana-black dark:text-ghana-gold flex items-center">
                    <Icon className="h-4 w-4 mr-1.5 text-ghana-gold" />
                    {iconLabel}
                  </span>
                </div>

                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-display mb-6 leading-tight dark:text-foreground">
                  {title.split(' ').map((word, i, arr) => 
                    i === arr.length - 1 ? (
                      <span key={i} className="text-ghana-green dark:text-ghana-gold relative">
                        {word}
                        <span className="absolute -bottom-2 left-0 w-full h-1 bg-ghana-gold/60 rounded-full"></span>
                      </span>
                    ) : (
                      <span key={i}>{word} </span>
                    )
                  )}
                </h1>

                <p className="text-xl text-gray-600 dark:text-gray-300 mb-10 max-w-3xl mx-auto leading-relaxed">
                  {subtitle}
                </p>
              </div>
            </div>
          </section>

          {/* Content Sections */}
          {children}
        </main>
      </PublicPageTransition>
      <Footer />
    </div>
  );
};

export default PageTemplate;
