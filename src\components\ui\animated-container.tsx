import React, { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { duration: 0.4, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    transition: { duration: 0.2, ease: "easeIn" }
  }
};

const slideUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    y: 20,
    transition: { duration: 0.2, ease: "easeIn" }
  }
};

const slideDown = {
  hidden: { opacity: 0, y: -20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.2, ease: "easeIn" }
  }
};

const slideInLeft = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.4, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    x: -20,
    transition: { duration: 0.2, ease: "easeIn" }
  }
};

const slideInRight = {
  hidden: { opacity: 0, x: 20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.4, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    x: 20,
    transition: { duration: 0.2, ease: "easeIn" }
  }
};

const scale = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.4, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    transition: { duration: 0.2, ease: "easeIn" }
  }
};

// Animation types
type AnimationType = 'fade' | 'slideUp' | 'slideDown' | 'slideInLeft' | 'slideInRight' | 'scale';

// Props interface
interface AnimatedContainerProps {
  children: ReactNode;
  type?: AnimationType;
  className?: string;
  delay?: number;
  duration?: number;
  isVisible?: boolean;
  onExitComplete?: () => void;
}

/**
 * AnimatedContainer - A reusable component for adding animations to any content
 */
const AnimatedContainer = ({
  children,
  type = 'fade',
  className,
  delay = 0,
  duration,
  isVisible = true,
  onExitComplete
}: AnimatedContainerProps) => {
  // Select the animation variant based on type
  const getVariant = () => {
    switch (type) {
      case 'fade':
        return fadeIn;
      case 'slideUp':
        return slideUp;
      case 'slideDown':
        return slideDown;
      case 'slideInLeft':
        return slideInLeft;
      case 'slideInRight':
        return slideInRight;
      case 'scale':
        return scale;
      default:
        return fadeIn;
    }
  };

  // Apply custom duration if provided
  const variant = getVariant();
  if (duration) {
    variant.visible.transition.duration = duration;
    variant.exit.transition.duration = duration;
  }

  return (
    <AnimatePresence onExitComplete={onExitComplete}>
      {isVisible && (
        <motion.div
          className={cn(className)}
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={variant}
          transition={{ delay }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AnimatedContainer;
