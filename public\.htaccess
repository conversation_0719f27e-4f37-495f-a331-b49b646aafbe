# Proper MIME type for all files
<IfModule mod_mime.c>
  # JavaScript
  AddType application/javascript              js
  AddType application/json                    json

  # Media files
  AddType image/svg+xml                       svg svgz
  AddType image/webp                          webp
  AddType image/x-icon                        ico
  AddType image/png                           png
  AddType image/jpeg                          jpg jpeg
  AddType image/gif                           gif

  # Fonts
  AddType font/woff                           woff
  AddType font/woff2                          woff2
  AddType application/vnd.ms-fontobject       eot
  AddType font/ttf                            ttf
  AddType font/collection                     ttc
  AddType font/otf                            otf
</IfModule>

# Enable CORS
<IfModule mod_headers.c>
  <FilesMatch "\.(ttf|ttc|otf|eot|woff|woff2|font\.css|css|js|json)$">
    Header set Access-Control-Allow-Origin "*"
  </FilesMatch>
</IfModule>

# Serve correct encoding type for service worker
<Files "service-worker.js">
  AddType application/javascript .js
  Header set Service-Worker-Allowed "/"
</Files>

# Serve correct encoding type for manifest
<Files "manifest.json">
  AddType application/manifest+json .json
</Files>

# Disable caching for service worker
<Files "service-worker.js">
  FileETag None
  Header unset ETag
  Header set Cache-Control "max-age=0, no-cache, no-store, must-revalidate"
  Header set Pragma "no-cache"
  Header set Expires "Wed, 11 Jan 1984 05:00:00 GMT"
</Files>

# Handle Single Page Application routing
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  
  # If the request is not for a real file or directory
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  
  # Exclude API routes from rewrite
  RewriteCond %{REQUEST_URI} !^/api/
  
  # Rewrite all other requests to index.html
  RewriteRule ^ index.html [L]
</IfModule>

# Compress text files
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript
</IfModule>

# Set security headers
<IfModule mod_headers.c>
  # Protect against XSS attacks
  Header set X-XSS-Protection "1; mode=block"
  
  # Prevent MIME-sniffing
  Header set X-Content-Type-Options "nosniff"
  
  # Referrer policy
  Header set Referrer-Policy "strict-origin-when-cross-origin"
  
  # Content Security Policy
  Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' https://*.supabase.co; manifest-src 'self';"
</IfModule>
