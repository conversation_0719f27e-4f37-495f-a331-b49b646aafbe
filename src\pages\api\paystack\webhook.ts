import { NextApiRequest, NextApiResponse } from 'next';
import crypto from 'crypto';
import { supabase } from '@/integrations/supabase/client';

// Paystack webhook secret from environment variables
const PAYSTACK_SECRET = process.env.PAYSTACK_WEBHOOK_SECRET || '';

/**
 * Verify Paystack webhook signature
 */
const verifySignature = (
  signature: string,
  requestBody: string
): boolean => {
  try {
    const hash = crypto
      .createHmac('sha512', PAYSTACK_SECRET)
      .update(requestBody)
      .digest('hex');
    
    return hash === signature;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
};

/**
 * Paystack webhook handler
 * 
 * This endpoint handles webhook events from Paystack for subscription management.
 * It verifies the webhook signature to ensure the request is from Paystack.
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    // Get the signature from the headers
    const signature = req.headers['x-paystack-signature'] as string;
    
    if (!signature) {
      console.warn('Missing Paystack signature');
      return res.status(400).json({ error: 'Missing Paystack signature' });
    }
    
    // Get the raw request body
    const rawBody = JSON.stringify(req.body);
    
    // Verify the signature
    const isValid = verifySignature(signature, rawBody);
    
    if (!isValid) {
      console.warn('Invalid Paystack webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }
    
    // Process the webhook event
    const event = req.body;
    const eventType = event.event;
    const data = event.data;
    
    console.log(`Received Paystack webhook: ${eventType}`);
    
    switch (eventType) {
      case 'charge.success':
        await handleChargeSuccess(data);
        break;
        
      case 'subscription.create':
        await handleSubscriptionCreate(data);
        break;
        
      case 'subscription.disable':
        await handleSubscriptionDisable(data);
        break;
        
      default:
        console.log(`Unhandled webhook event: ${eventType}`);
    }
    
    return res.status(200).json({ received: true });
  } catch (error) {
    console.error('Error processing Paystack webhook:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle charge.success event
 */
async function handleChargeSuccess(data: any) {
  try {
    const { reference, metadata } = data;
    
    // If this is a subscription payment
    if (metadata?.subscription_id) {
      // Update the subscription
      await supabase
        .from('subscriptions')
        .update({
          status: 'active',
          payment_status: 'active',
          last_payment_date: new Date().toISOString(),
          last_payment_transaction_id: reference
        })
        .eq('id', metadata.subscription_id);
      
      // Log the transaction
      await supabase
        .from('subscription_transactions')
        .insert({
          user_id: metadata.user_id,
          subscription_id: metadata.subscription_id,
          transaction_type: 'payment_success',
          payment_gateway: 'paystack',
          payment_gateway_reference: reference,
          payment_gateway_response: data,
          verification_status: 'verified',
          amount: data.amount / 100 // Convert from kobo to GHS
        });
    }
  } catch (error) {
    console.error('Error handling charge.success webhook:', error);
  }
}

/**
 * Handle subscription.create event
 */
async function handleSubscriptionCreate(data: any) {
  try {
    const { customer, plan, subscription_code } = data;
    
    // Find the subscription by customer email
    const { data: subscriptions } = await supabase
      .from('subscriptions')
      .select('id, user_id')
      .eq('customer_email', customer.email)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (subscriptions && subscriptions.length > 0) {
      const { id: subscriptionId, user_id: userId } = subscriptions[0];
      
      // Update the subscription
      await supabase
        .from('subscriptions')
        .update({
          status: 'active',
          payment_status: 'active',
          paystack_subscription_code: subscription_code
        })
        .eq('id', subscriptionId);
      
      // Log the transaction
      await supabase
        .from('subscription_transactions')
        .insert({
          user_id: userId,
          subscription_id: subscriptionId,
          transaction_type: 'create',
          payment_gateway: 'paystack',
          payment_gateway_reference: subscription_code,
          payment_gateway_response: data,
          verification_status: 'verified'
        });
    }
  } catch (error) {
    console.error('Error handling subscription.create webhook:', error);
  }
}

/**
 * Handle subscription.disable event
 */
async function handleSubscriptionDisable(data: any) {
  try {
    const { subscription_code } = data;
    
    // Find the subscription by subscription code
    const { data: subscriptions } = await supabase
      .from('subscriptions')
      .select('id, user_id')
      .eq('paystack_subscription_code', subscription_code)
      .limit(1);
    
    if (subscriptions && subscriptions.length > 0) {
      const { id: subscriptionId, user_id: userId } = subscriptions[0];
      
      // Update the subscription
      await supabase
        .from('subscriptions')
        .update({
          status: 'cancelled',
          cancellation_date: new Date().toISOString()
        })
        .eq('id', subscriptionId);
      
      // Log the transaction
      await supabase
        .from('subscription_transactions')
        .insert({
          user_id: userId,
          subscription_id: subscriptionId,
          transaction_type: 'cancel',
          payment_gateway: 'paystack',
          payment_gateway_reference: subscription_code,
          payment_gateway_response: data,
          verification_status: 'verified'
        });
    }
  } catch (error) {
    console.error('Error handling subscription.disable webhook:', error);
  }
}
