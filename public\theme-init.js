// This script initializes the theme before the React app loads
// to prevent flash of wrong theme
(function() {
  // Try to get the theme from localStorage
  const storedTheme = localStorage.getItem('theme');
  
  // If we have a stored theme, use it
  if (storedTheme === 'dark' || storedTheme === 'light') {
    document.documentElement.classList.add(storedTheme);
    return;
  }
  
  // If we have 'system' or no preference, check system preference
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  document.documentElement.classList.add(prefersDark ? 'dark' : 'light');
})();
