/**
 * Apply Database Migration Script
 * 
 * This script applies the SQL migration to add the invoice_settings column to the user_preferences table.
 * It uses the Supabase client with the service role key to execute the SQL directly.
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key is missing.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_SERVICE_ROLE_KEY are set in your .env file.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20240718_add_invoice_settings_to_user_preferences.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    console.log('Running migration to add invoice_settings column to user_preferences table...');
    
    // Execute the SQL directly
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSql });
    
    if (error) {
      console.error('Error running migration:', error);
      
      // If the rpc method doesn't exist, try using the REST API directly
      console.log('Trying alternative method...');
      
      // Split the SQL into separate statements
      const statements = migrationSql.split(';').filter(stmt => stmt.trim().length > 0);
      
      for (const statement of statements) {
        const { error } = await supabase.from('_exec_sql').select('*').eq('sql', statement);
        if (error) {
          console.error('Error executing SQL statement:', error);
          console.error('Statement:', statement);
          process.exit(1);
        }
      }
    }
    
    console.log('Migration completed successfully!');
    console.log('The invoice_settings column has been added to the user_preferences table.');
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

runMigration();
