/**
 * Payvoicer Service Worker
 * This service worker provides offline functionality and caching for the application
 */

// Cache name includes version to allow for easy updates
const CACHE_NAME = 'payvoicer-cache-v1';

// Assets to cache immediately on service worker install
const PRECACHE_ASSETS = [
  '/',
  '/index.html',
  '/browser-compatibility.js',
  '/theme-init.js',
  '/manifest.json',
  '/chrome-ios-fixes.css',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/icons/apple-touch-icon.png',
  '/icons/favicon.ico'
];

// Install event - precache static assets
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(PRECACHE_ASSETS);
      })
      .then(() => self.skipWaiting()) // Activate worker immediately
      .catch(error => {
        console.error('Pre-caching failed:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  const cacheWhitelist = [CACHE_NAME];
  
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            // Delete old caches
            return caches.delete(cacheName);
          }
        })
      );
    })
    .then(() => self.clients.claim()) // Take control of clients immediately
  );
});

// Helper function to determine if a request is an API call
const isApiRequest = (url) => {
  return url.pathname.startsWith('/api/') || 
         url.hostname.includes('api.') || 
         url.hostname.includes('supabase');
};

// Helper function to determine if a request is for a static asset
const isStaticAsset = (url) => {
  const staticExtensions = [
    '.js', '.css', '.png', '.jpg', '.jpeg', '.gif', 
    '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot'
  ];
  
  return staticExtensions.some(ext => url.pathname.endsWith(ext)) ||
         url.pathname.startsWith('/static/') ||
         url.pathname.startsWith('/assets/') ||
         url.pathname.startsWith('/icons/');
};

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin) && 
      !event.request.url.includes('fonts.googleapis.com') &&
      !event.request.url.includes('fonts.gstatic.com')) {
    return;
  }
  
  // Parse the URL
  const requestUrl = new URL(event.request.url);
  
  // Different strategies based on request type
  if (isApiRequest(requestUrl)) {
    // Network-first strategy for API requests
    event.respondWith(
      fetch(event.request)
        .catch(error => {
          console.log('Fetch failed; returning offline page instead.', error);
          
          // If network request fails, try to serve from cache
          return caches.match(event.request)
            .then(response => {
              if (response) {
                return response;
              }
              
              // If the API request isn't in the cache, return a basic offline response
              if (event.request.headers.get('accept').includes('application/json')) {
                return new Response(JSON.stringify({ error: 'You are offline' }), {
                  headers: { 'Content-Type': 'application/json' }
                });
              }
            });
        })
    );
  } else if (isStaticAsset(requestUrl)) {
    // Cache-first strategy for static assets
    event.respondWith(
      caches.match(event.request)
        .then(response => {
          // Return cached response if found
          if (response) {
            return response;
          }
          
          // If not in cache, fetch from network and cache
          return fetch(event.request)
            .then(networkResponse => {
              // Check if we received a valid response
              if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
                return networkResponse;
              }
              
              // Clone the response as it can only be consumed once
              const responseToCache = networkResponse.clone();
              
              caches.open(CACHE_NAME)
                .then(cache => {
                  cache.put(event.request, responseToCache);
                });
                
              return networkResponse;
            });
        })
    );
  } else {
    // For HTML navigation requests, use network-first with fallback to cache
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Cache the latest version
          const responseToCache = response.clone();
          caches.open(CACHE_NAME)
            .then(cache => {
              cache.put(event.request, responseToCache);
            });
          return response;
        })
        .catch(error => {
          console.log('Fetch failed; returning cached page instead.', error);
          return caches.match(event.request)
            .then(response => {
              if (response) {
                return response;
              }
              // If not in cache, fallback to offline page
              return caches.match('/index.html');
            });
        })
    );
  }
});

// Handle messages from clients
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
