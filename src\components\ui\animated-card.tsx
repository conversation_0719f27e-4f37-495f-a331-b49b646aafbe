import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface AnimatedCardProps {
  children?: ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  footerClassName?: string;
  title?: ReactNode;
  description?: ReactNode;
  footer?: ReactNode;
  animationType?: 'hover' | 'static' | 'none';
  delay?: number;
  onClick?: () => void;
}

/**
 * AnimatedCard - An enhanced card component with animations
 */
export function AnimatedCard({
  children,
  className,
  headerClassName,
  contentClassName,
  footerClassName,
  title,
  description,
  footer,
  animationType = 'hover',
  delay = 0,
  onClick
}: AnimatedCardProps) {
  // Animation variants
  const getAnimationProps = () => {
    switch (animationType) {
      case 'hover':
        return {
          whileHover: { 
            y: -5, 
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
          },
          transition: { 
            type: 'spring', 
            stiffness: 300, 
            damping: 20,
            delay 
          }
        };
      case 'static':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          transition: { 
            duration: 0.5, 
            ease: 'easeOut',
            delay 
          }
        };
      case 'none':
      default:
        return {};
    }
  };

  return (
    <motion.div 
      {...getAnimationProps()}
      onClick={onClick}
      className={cn(
        onClick && 'cursor-pointer',
        'h-full'
      )}
    >
      <Card className={cn('h-full border transition-colors duration-300', className)}>
        {(title || description) && (
          <CardHeader className={headerClassName}>
            {title && <CardTitle>{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
        )}
        
        {children && (
          <CardContent className={contentClassName}>
            {children}
          </CardContent>
        )}
        
        {footer && (
          <CardFooter className={footerClassName}>
            {footer}
          </CardFooter>
        )}
      </Card>
    </motion.div>
  );
}

export default AnimatedCard;
