-- Add billing_cycle and start_date columns to the subscriptions table
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS billing_cycle VARCHAR(10),
ADD COLUMN IF NOT EXISTS start_date TIMESTAMP WITH TIME ZONE;

-- Update existing records to set default values
UPDATE subscriptions
SET 
  billing_cycle = 'monthly',
  start_date = created_at
WHERE billing_cycle IS NULL OR start_date IS NULL;

-- Add an index on start_date and current_period_end for faster queries
CREATE INDEX IF NOT EXISTS idx_subscriptions_dates ON subscriptions(start_date, current_period_end);

-- Create a function to check for expired subscriptions
CREATE OR REPLACE FUNCTION check_subscription_expiry()
RETURNS TRIGGER AS $$
BEGIN
  -- If the current_period_end has passed, set status to 'expired'
  IF NEW.current_period_end < NOW() AND NEW.status = 'active' THEN
    NEW.status := 'expired';
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update subscription status
DROP TRIGGER IF EXISTS subscription_expiry_trigger ON subscriptions;
CREATE TRIGGER subscription_expiry_trigger
BEFORE UPDATE ON subscriptions
FOR EACH ROW
EXECUTE FUNCTION check_subscription_expiry();

-- Create a scheduled function to automatically expire subscriptions
CREATE OR REPLACE FUNCTION expire_subscriptions()
RETURNS void AS $$
BEGIN
  UPDATE subscriptions
  SET status = 'expired'
  WHERE current_period_end < NOW() AND status = 'active';
END;
$$ LANGUAGE plpgsql;
