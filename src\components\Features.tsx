
import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { motion, useAnimation, useInView } from 'framer-motion';
import {
  FileText, Clock, Home, CreditCard, PieChart, Users,
  CheckCircle, ArrowRight, Shield, Smartphone, Globe
} from 'lucide-react';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  index: number;
}

const MotionCard = motion.create(Card);

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description, index }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start("visible");
    }
  }, [isInView, controls]);

  return (
    <MotionCard
      ref={ref}
      className="border border-gray-100 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden group"
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.5,
            delay: index * 0.1
          }
        }
      }}
    >
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-ghana-gold via-ghana-green to-ghana-gold-light dark:from-ghana-gold dark:via-ghana-gold dark:to-ghana-gold-light transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
      <CardHeader>
        <div className="w-14 h-14 bg-ghana-gold/10 dark:bg-ghana-gold/20 rounded-xl flex items-center justify-center mb-4 group-hover:bg-ghana-gold/20 dark:group-hover:bg-ghana-gold/30 transition-colors duration-300 shadow-sm">
          {icon}
        </div>
        <CardTitle className="text-xl group-hover:text-ghana-green dark:text-white dark:group-hover:text-ghana-gold transition-colors duration-300">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 dark:text-gray-300">{description}</p>
        <div className="mt-4 flex items-center text-ghana-green dark:text-ghana-gold opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-sm font-medium">
          <span>Learn more</span>
          <ArrowRight className="ml-1 h-4 w-4" />
        </div>
      </CardContent>
    </MotionCard>
  );
};

const Features: React.FC = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.1 });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start("visible");
    }
  }, [isInView, controls]);

  const features = [
    {
      icon: <FileText size={28} className="text-ghana-green dark:text-ghana-gold" />,
      title: 'GRA VAT-Compliant Invoices',
      description: 'Generate legally compliant invoices that meet all GRA requirements, complete with authenticated QR codes and digital signatures.'
    },
    {
      icon: <Clock size={28} className="text-ghana-green dark:text-ghana-gold" />,
      title: 'Real-Time Tax Calculations',
      description: 'Automatically calculate VAT and other taxes in real-time as you create invoices, ensuring accuracy and compliance.'
    },
    {
      icon: <Home size={28} className="text-ghana-green dark:text-ghana-gold" />,
      title: 'Client Management',
      description: 'Store and manage client information for quick invoice creation. Keep track of payment history and outstanding balances.'
    },
    {
      icon: <CreditCard size={28} className="text-ghana-green dark:text-ghana-gold" />,
      title: 'Paystack Integration',
      description: 'Get paid faster with direct payment links on invoices. Accept payments via mobile money, card, and bank transfers.'
    },
    {
      icon: <PieChart size={28} className="text-ghana-green dark:text-ghana-gold" />,
      title: 'Comprehensive Dashboard',
      description: 'View your business performance at a glance with charts showing revenue, tax collected, and outstanding payments.'
    },
    {
      icon: <Users size={28} className="text-ghana-green dark:text-ghana-gold" />,
      title: 'Team Roles & Permissions',
      description: 'Set up different access levels for team members. Perfect for businesses with multiple departments or employees.'
    }
  ];

  return (
    <section className="py-20 md:py-28 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-950 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-ghana-green/5 dark:bg-ghana-green/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-ghana-gold/5 dark:bg-ghana-gold/10 rounded-full blur-3xl"></div>

      {/* Animated dots */}
      <div className="absolute top-20 left-10 w-3 h-3 bg-ghana-gold/30 dark:bg-ghana-gold/40 rounded-full animate-pulse"></div>
      <div className="absolute top-40 right-20 w-2 h-2 bg-ghana-green/30 dark:bg-ghana-green/40 rounded-full animate-float"></div>
      <div className="absolute bottom-20 left-1/4 w-4 h-4 bg-ghana-green/20 dark:bg-ghana-green/30 rounded-full animate-bounce"></div>

      <div className="container relative z-10">
        <motion.div
          ref={ref}
          className="text-center max-w-3xl mx-auto mb-16"
          initial="hidden"
          animate={controls}
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: {
              opacity: 1,
              y: 0,
              transition: { duration: 0.5 }
            }
          }}
        >
          <div className="inline-block bg-ghana-green/10 dark:bg-ghana-green/20 px-4 py-1.5 rounded-full mb-4">
            <span className="text-sm font-semibold text-ghana-green dark:text-ghana-gold flex items-center">
              <CheckCircle className="h-4 w-4 mr-1.5" />
              Comprehensive Features
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold font-display mb-4 dark:text-white">
            Features Built for <span className="text-ghana-green dark:text-ghana-gold">Ghanaian Businesses</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-300 text-lg">
            Everything you need to manage invoices, stay compliant with GRA regulations, and get paid faster.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              index={index}
            />
          ))}
        </div>

        <motion.div
          className="mt-16 bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 border border-gray-100 dark:border-gray-700 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 40 }}
          animate={{
            opacity: isInView ? 1 : 0,
            y: isInView ? 0 : 40,
            transition: { delay: 0.6, duration: 0.6 }
          }}
        >
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div>
              <h3 className="text-2xl font-bold mb-2 dark:text-white">Ready to streamline your invoicing?</h3>
              <p className="text-gray-600 dark:text-gray-300">Join thousands of businesses using Payvoicer for GRA-compliant invoicing.</p>
            </div>
            <div className="flex gap-4">
              <a href="/auth" className="bg-ghana-green hover:bg-ghana-green/90 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center">
                Get Started Free
                <ArrowRight className="ml-2 h-4 w-4" />
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Features;
