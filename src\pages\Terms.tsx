import React from 'react';
import PageTemplate from '@/components/PageTemplate';
import { FileText, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Terms: React.FC = () => {
  return (
    <PageTemplate
      title="Terms of Service"
      subtitle="Please read these terms carefully before using Payvoicer"
      icon={FileText}
      iconLabel="Legal"
    >
      {/* Terms Content Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 border border-gray-100 dark:border-gray-700">
              <div className="prose prose-lg dark:prose-invert max-w-none">
                <p className="text-gray-500 dark:text-gray-400 mb-8">
                  Last Updated: May 15, 2025
                </p>

                <h2>1. Introduction</h2>
                <p>
                  Welcome to Payvoicer ("we," "our," or "us"). These Terms of Service ("Terms") govern your access to and use of the Payvoicer website, mobile applications, and services (collectively, the "Service").
                </p>
                <p>
                  By accessing or using the Service, you agree to be bound by these Terms. If you do not agree to these Terms, you may not access or use the Service.
                </p>

                <h2>2. Definitions</h2>
                <p>
                  <strong>"Account"</strong> means a unique account created for you to access our Service.
                </p>
                <p>
                  <strong>"User"</strong> means any individual who accesses or uses the Service, including you.
                </p>
                <p>
                  <strong>"Content"</strong> means any information, data, text, software, graphics, messages, or other materials that are uploaded, transmitted, or otherwise made available through the Service.
                </p>

                <h2>3. Account Registration</h2>
                <p>
                  To use certain features of the Service, you must register for an Account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.
                </p>
                <p>
                  You are responsible for safeguarding your Account credentials and for all activities that occur under your Account. You agree to notify us immediately of any unauthorized use of your Account or any other breach of security.
                </p>

                <h2>4. Service Usage</h2>
                <p>
                  You may use the Service only for lawful purposes and in accordance with these Terms. You agree not to:
                </p>
                <ul>
                  <li>Use the Service in any way that violates any applicable law or regulation</li>
                  <li>Use the Service to transmit any material that is defamatory, obscene, or offensive</li>
                  <li>Use the Service to impersonate any person or entity or falsely state or misrepresent your affiliation with a person or entity</li>
                  <li>Interfere with or disrupt the Service or servers or networks connected to the Service</li>
                  <li>Attempt to gain unauthorized access to any portion of the Service or any other systems or networks connected to the Service</li>
                </ul>

                <h2>5. Subscription and Payments</h2>
                <p>
                  Some features of the Service require a subscription. By subscribing to the Service, you agree to pay the applicable fees as they become due.
                </p>
                <p>
                  All payments are non-refundable except as expressly set forth in these Terms or as required by applicable law. We may change the fees for the Service at any time, but any price changes will apply to billing cycles after the current cycle.
                </p>

                <h2>6. Intellectual Property</h2>
                <p>
                  The Service and its original content, features, and functionality are and will remain the exclusive property of Payvoicer and its licensors. The Service is protected by copyright, trademark, and other laws of Ghana and foreign countries.
                </p>
                <p>
                  You may not duplicate, copy, or reuse any portion of the HTML, CSS, JavaScript, or visual design elements of the Service without express written permission from us.
                </p>

                <h2>7. User Content</h2>
                <p>
                  You retain all rights to any Content you submit, post, or display on or through the Service. By submitting, posting, or displaying Content on or through the Service, you grant us a worldwide, non-exclusive, royalty-free license to use, reproduce, modify, adapt, publish, translate, create derivative works from, distribute, and display such Content in connection with providing the Service.
                </p>
                <p>
                  You represent and warrant that you have all rights, power, and authority necessary to grant the rights granted herein to any Content that you submit.
                </p>

                <h2>8. Privacy</h2>
                <p>
                  Your use of the Service is also governed by our Privacy Policy, which is incorporated into these Terms by reference. Please review our Privacy Policy to understand our practices regarding your personal information.
                </p>

                <h2>9. Termination</h2>
                <p>
                  We may terminate or suspend your Account and access to the Service immediately, without prior notice or liability, for any reason, including if you breach these Terms.
                </p>
                <p>
                  Upon termination, your right to use the Service will immediately cease. If you wish to terminate your Account, you may simply discontinue using the Service or contact us to request Account deletion.
                </p>

                <h2>10. Limitation of Liability</h2>
                <p>
                  In no event shall Payvoicer, its directors, employees, partners, agents, suppliers, or affiliates be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the Service.
                </p>

                <h2>11. Governing Law</h2>
                <p>
                  These Terms shall be governed by and construed in accordance with the laws of Ghana, without regard to its conflict of law provisions.
                </p>

                <h2>12. Changes to Terms</h2>
                <p>
                  We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.
                </p>
                <p>
                  By continuing to access or use our Service after any revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, you are no longer authorized to use the Service.
                </p>

                <h2>13. Contact Us</h2>
                <p>
                  If you have any questions about these Terms, please contact us at <a href="mailto:<EMAIL>" className="text-ghana-green dark:text-ghana-gold"><EMAIL></a>.
                </p>
              </div>
            </div>

            <div className="mt-12 flex flex-col md:flex-row justify-center gap-6">
              <Link to="/privacy">
                <Button variant="outline" className="flex items-center w-full md:w-auto">
                  Privacy Policy <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link to="/compliance">
                <Button variant="outline" className="flex items-center w-full md:w-auto">
                  GRA Compliance <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link to="/security-policy">
                <Button variant="outline" className="flex items-center w-full md:w-auto">
                  Security Policy <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </PageTemplate>
  );
};

export default Terms;
