-- Create a function to get a client by ID without authentication
-- This function bypasses RLS for public access to clients
CREATE OR REPLACE FUNCTION get_public_client(client_id UUID)
RETURNS SETOF clients
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM clients WHERE id = client_id;
$$;

-- Create a function to get a business profile by user ID without authentication
-- This function bypasses RLS for public access to profiles
CREATE OR REPLACE FUNCTION get_public_business_profile(user_id UUID)
RETURNS SETOF profiles
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM profiles WHERE id = user_id;
$$;

-- Create a function to update an invoice status without authentication
-- This function bypasses <PERSON><PERSON> for public access to update invoice status
CREATE OR REPLACE FUNCTION update_public_invoice_status(invoice_id UUID, new_status TEXT)
RETURNS SETOF invoices
LANGUAGE sql
SECURITY DEFINER
AS $$
  UPDATE invoices
  SET status = new_status,
      updated_at = NOW(),
      paid_at = CASE WHEN new_status = 'paid' THEN NOW() ELSE paid_at END
  WHERE id = invoice_id
  RETURNING *;
$$;

-- Create a function to create a payment record without authentication
-- This function bypasses RLS for public access to create payment records
CREATE OR REPLACE FUNCTION create_public_payment_record(
  p_invoice_id UUID,
  p_amount NUMERIC,
  p_payment_method TEXT,
  p_transaction_reference TEXT,
  p_status TEXT
)
RETURNS SETOF payments
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_payment_id UUID;
BEGIN
  -- Insert the payment record
  INSERT INTO payments (
    invoice_id,
    amount,
    payment_method,
    transaction_reference,
    status,
    payment_date
  )
  VALUES (
    p_invoice_id,
    p_amount,
    p_payment_method,
    p_transaction_reference,
    p_status,
    NOW()
  )
  RETURNING id INTO v_payment_id;
  
  -- Return the created payment record
  RETURN QUERY SELECT * FROM payments WHERE id = v_payment_id;
END;
$$;
