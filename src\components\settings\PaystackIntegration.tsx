import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from '@/hooks/use-toast';
import { useSession } from '@/contexts/SessionContext';
import { supabase } from '@/integrations/supabase/client';

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Loader2, CheckCircle, AlertCircle, ExternalLink } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Define the form schema
const paystackFormSchema = z.object({
  public_key: z.string().min(1, 'Public key is required'),
  secret_key: z.string().min(1, 'Secret key is required'),
  webhook_secret: z.string().optional(),
  is_test: z.boolean().default(true),
});

type PaystackFormValues = z.infer<typeof paystackFormSchema>;

// Define the PaystackIntegration component
const PaystackIntegration: React.FC = () => {
  const { user } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasIntegration, setHasIntegration] = useState(false);

  // Initialize form
  const form = useForm<PaystackFormValues>({
    resolver: zodResolver(paystackFormSchema),
    defaultValues: {
      public_key: '',
      secret_key: '',
      webhook_secret: '',
      is_test: true,
    },
  });

  // Load existing Paystack integration
  useEffect(() => {
    const loadPaystackIntegration = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('paystack_integrations')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (error) {
          if (error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
            console.error('Error loading Paystack integration:', error);
            toast({
              title: 'Error',
              description: 'Failed to load Paystack integration',
              variant: 'destructive',
            });
          }
          setHasIntegration(false);
        } else if (data) {
          form.reset({
            public_key: data.public_key,
            secret_key: data.secret_key,
            webhook_secret: data.webhook_secret || '',
            is_test: data.is_test,
          });
          setHasIntegration(true);
        }
      } catch (error) {
        console.error('Error in loadPaystackIntegration:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPaystackIntegration();
  }, [user, form]);

  // Handle form submission
  const onSubmit = async (values: PaystackFormValues) => {
    if (!user) return;

    setIsSaving(true);
    try {
      const integrationData = {
        user_id: user.id,
        public_key: values.public_key,
        secret_key: values.secret_key,
        webhook_secret: values.webhook_secret || null,
        is_test: values.is_test,
      };

      let result;
      if (hasIntegration) {
        // Update existing integration
        result = await supabase
          .from('paystack_integrations')
          .update(integrationData)
          .eq('user_id', user.id);
      } else {
        // Create new integration
        result = await supabase
          .from('paystack_integrations')
          .insert(integrationData);
      }

      if (result.error) {
        throw result.error;
      }

      setHasIntegration(true);
      toast({
        title: 'Success',
        description: 'Paystack integration saved successfully',
      });
    } catch (error) {
      console.error('Error saving Paystack integration:', error);
      toast({
        title: 'Error',
        description: 'Failed to save Paystack integration',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <img 
            src="/paystack-logo.png" 
            alt="Paystack" 
            className="h-6 mr-2" 
            onError={(e) => {
              e.currentTarget.src = 'https://paystack.com/favicon.ico';
            }}
          />
          Paystack Integration
        </CardTitle>
        <CardDescription>
          Connect your Paystack account to process payments for your invoices
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-6">
            <Loader2 className="h-8 w-8 animate-spin text-ghana-green" />
          </div>
        ) : (
          <>
            <Alert className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                Your Paystack API keys are stored securely and used only to process payments for your invoices.
                Payments will flow directly into your Paystack account.
              </AlertDescription>
            </Alert>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="is_test"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Test Mode</FormLabel>
                        <FormDescription>
                          Use Paystack test keys for development and testing
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <Separator />

                <FormField
                  control={form.control}
                  name="public_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Public Key</FormLabel>
                      <FormControl>
                        <Input placeholder="pk_..." {...field} />
                      </FormControl>
                      <FormDescription>
                        Your Paystack public key (starts with pk_)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="secret_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Secret Key</FormLabel>
                      <FormControl>
                        <Input 
                          type="password" 
                          placeholder="sk_..." 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Your Paystack secret key (starts with sk_)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="webhook_secret"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Webhook Secret (Optional)</FormLabel>
                      <FormControl>
                        <Input 
                          type="password" 
                          placeholder="whsec_..." 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Your Paystack webhook signing secret for verifying webhook events
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button 
                  type="submit" 
                  className="w-full"
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Integration'
                  )}
                </Button>
              </form>
            </Form>
          </>
        )}
      </CardContent>
      <CardFooter className="flex flex-col items-start">
        <div className="text-sm text-muted-foreground">
          <a 
            href="https://dashboard.paystack.com/#/settings/developer" 
            target="_blank" 
            rel="noopener noreferrer"
            className="flex items-center text-blue-600 hover:underline"
          >
            Get your Paystack API keys
            <ExternalLink className="ml-1 h-3 w-3" />
          </a>
        </div>
      </CardFooter>
    </Card>
  );
};

export default PaystackIntegration;
