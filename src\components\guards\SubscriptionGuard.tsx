import React, { useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { useSubscription, SubscriptionTier } from '@/contexts/SubscriptionContext';
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Info,
  LifeBuoy,
  Loader2,
  Lock,
  Settings,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  requiredTier?: SubscriptionTier;
  requiredFeature?: string;
  fallbackUrl?: string;
}

const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  children,
  requiredTier = 'free',
  requiredFeature,
  fallbackUrl = '/settings?tab=billing'
}) => {
  const { isLoading, canAccessTier, canAccessFeature, tier } = useSubscription();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const navigate = useNavigate();

  // Check access when subscription data is loaded
  useEffect(() => {
    if (!isLoading) {
      try {
        // Check if user has access to the required tier
        const hasTierAccess = canAccessTier(requiredTier);

        // Check if user has access to the required feature (if specified)
        const hasFeatureAccess = requiredFeature ? canAccessFeature(requiredFeature) : true;

        // Set access state
        setHasAccess(hasTierAccess && hasFeatureAccess);

        // Show toast if access is denied
        if (!(hasTierAccess && hasFeatureAccess)) {
          // Check if the user's tier is actually higher than the required tier
          const tierHierarchy = { 'free': 0, 'freelancer': 1, 'business': 2, 'enterprise': 3 };
          const userTierLevel = tierHierarchy[tier] || 0;
          const requiredTierLevel = tierHierarchy[requiredTier] || 0;

          if (userTierLevel > requiredTierLevel) {
            // This is an error in the access control logic - the user should have access
            toast({
              title: 'Access Control Error',
              description: `You should have access to this feature with your ${tier} plan. Please contact support.`,
              variant: 'destructive',
            });
          } else {
            toast({
              title: 'Subscription Upgrade Required',
              description: `This feature requires the ${requiredTier} plan or higher.`,
              variant: 'default',
            });
          }
        }
      } catch (error) {
        console.error('Error checking subscription access:', error);
        setHasAccess(false);
      }
    }
  }, [isLoading, canAccessTier, canAccessFeature, requiredTier, requiredFeature]);

  // Show loading state while checking subscription
  if (isLoading || hasAccess === null) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 text-ghana-green animate-spin mb-4" />
        <p className="text-lg text-gray-600">Checking subscription...</p>
      </div>
    );
  }

  // If user doesn't have access, show upgrade message
  if (!hasAccess) {
    // Check if the user's tier is actually higher than the required tier
    const tierHierarchy = { 'free': 0, 'freelancer': 1, 'business': 2, 'enterprise': 3 };
    const userTierLevel = tierHierarchy[tier] || 0;
    const requiredTierLevel = tierHierarchy[requiredTier] || 0;
    const isUserTierHigher = userTierLevel > requiredTierLevel;

    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] max-w-md mx-auto text-center p-6 animate-fadeIn">
        <div className={`border-2 ${isUserTierHigher ? 'bg-red-50 border-red-200' : canAccessTier(requiredTier) ? 'bg-green-50 border-green-200' : 'bg-amber-50 border-amber-200'} rounded-xl p-8 mb-6 w-full shadow-lg transition-all duration-300 hover:shadow-xl`}>
          <div className="flex items-center justify-center mb-6">
            <div className={`rounded-full p-3 ${isUserTierHigher ? 'bg-red-100' : canAccessTier(requiredTier) ? 'bg-green-100' : 'bg-amber-100'}`}>
              {isUserTierHigher ? (
                <AlertCircle className="h-8 w-8 text-red-600" />
              ) : canAccessTier(requiredTier) ? (
                <CheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <Lock className="h-8 w-8 text-amber-600" />
              )}
            </div>
          </div>

          <h2 className={`text-2xl font-bold mb-4 ${isUserTierHigher ? 'text-red-800' : canAccessTier(requiredTier) ? 'text-green-800' : 'text-amber-800'}`}>
            {isUserTierHigher ? 'Access Control Error' : canAccessTier(requiredTier) ? 'Feature Access Available' : 'Subscription Upgrade Required'}
          </h2>

          <p className={`${isUserTierHigher ? 'text-red-600' : canAccessTier(requiredTier) ? 'text-green-600' : 'text-amber-600'} mb-6`}>
            {isUserTierHigher ?
              `You should have access to this feature with your ${tier.charAt(0).toUpperCase() + tier.slice(1)} plan. Please contact support.` :
              canAccessTier(requiredTier) ?
                `You already have access to this feature with your ${tier.charAt(0).toUpperCase() + tier.slice(1)} plan.` :
                `This feature requires the ${requiredTier.charAt(0).toUpperCase() + requiredTier.slice(1)} plan or higher.
                ${tier ? ' Your current plan is ' + tier.charAt(0).toUpperCase() + tier.slice(1) + '.' : ''}
                Please upgrade your subscription to access this feature.`
            }
          </p>

          <div className={`p-4 rounded-lg mb-6 ${isUserTierHigher ? 'bg-red-100' : canAccessTier(requiredTier) ? 'bg-green-100' : 'bg-amber-100'}`}>
            <div className="flex items-start gap-2">
              <Info className={`h-5 w-5 mt-0.5 ${isUserTierHigher ? 'text-red-600' : canAccessTier(requiredTier) ? 'text-green-600' : 'text-amber-600'}`} />
              <p className={`text-sm ${isUserTierHigher ? 'text-red-700' : canAccessTier(requiredTier) ? 'text-green-700' : 'text-amber-700'}`}>
                {isUserTierHigher ?
                  'This appears to be an error in our access control system. Your subscription tier should grant you access to this feature.' :
                  canAccessTier(requiredTier) ?
                    'You can proceed to use this feature with your current subscription plan.' :
                    'Upgrading your subscription will give you access to this and many other premium features.'
                }
              </p>
            </div>
          </div>

          <Link to={fallbackUrl}>
            <Button className={`w-full shadow-md hover:shadow-lg transition-all duration-300 ${isUserTierHigher ? 'bg-red-600 hover:bg-red-700' : canAccessTier(requiredTier) ? 'bg-green-600 hover:bg-green-700' : 'bg-ghana-green hover:bg-ghana-green/90'}`}>
              <div className="flex items-center justify-center gap-2">
                {isUserTierHigher ? (
                  <>
                    <LifeBuoy className="h-5 w-5" />
                    <span>Contact Support</span>
                  </>
                ) : canAccessTier(requiredTier) ? (
                  <>
                    <Settings className="h-5 w-5" />
                    <span>Manage Subscription</span>
                  </>
                ) : (
                  <>
                    <Zap className="h-5 w-5" />
                    <span>Upgrade Subscription</span>
                  </>
                )}
              </div>
            </Button>
          </Link>
        </div>
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard')}
          className="mt-4 border-2 border-gray-200 hover:border-gray-300 transition-all duration-300"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Return to Dashboard
        </Button>
      </div>
    );
  }

  // If user has access, render the children
  return <>{children}</>;
};

export default SubscriptionGuard;
