
import React from 'react';
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin, ArrowUpRight, Heart } from 'lucide-react';
import ScrollLink from './ScrollLink';

// Use ScrollLink for all navigation to ensure smooth scrolling to top
const Link = ScrollLink;

const FooterLink = ({ to, children }: { to: string, children: React.ReactNode }) => (
  <li>
    <Link
      to={to}
      className="text-gray-500 hover:text-ghana-green dark:text-gray-400 dark:hover:text-ghana-gold transition-all duration-300 flex items-center group"
    >
      <span className="group-hover:translate-x-1 transition-transform duration-300">{children}</span>
      <ArrowUpRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </Link>
  </li>
);

const SocialLink = ({ href, icon }: { href: string, icon: React.ReactNode }) => (
  <a
    href={href}
    className="text-gray-400 hover:text-ghana-green dark:text-gray-500 dark:hover:text-ghana-gold transition-all duration-300 hover:scale-110 transform bg-white dark:bg-gray-800 p-2 rounded-full shadow-sm hover:shadow-md"
    target="_blank"
    rel="noopener noreferrer"
  >
    {icon}
  </a>
);

const Footer: React.FC = () => {
  return (
    <footer className="bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-950 border-t border-gray-200 dark:border-gray-700 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-ghana-green/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-ghana-gold/5 rounded-full blur-3xl"></div>

      <div className="container py-16 md:py-20 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-8 md:gap-12">
          <div className="md:col-span-4">
            <Link to="/" className="flex items-center gap-2 mb-6 group">
              <div className="h-10 w-auto shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-110">
                <img src="/logo.png" alt="Receipta Logo" className="h-full w-auto dark:invert" />
              </div>
              <span className="font-display font-bold text-2xl group-hover:text-ghana-green dark:text-white dark:group-hover:text-ghana-gold transition-colors duration-300">Payvoicer</span>
            </Link>

            <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
              The easiest way for Ghanaian businesses to create GRA VAT-compliant invoices and get paid faster. Perfect for freelancers and businesses of all sizes.
            </p>

            <div className="flex gap-3 mb-8">
              <SocialLink href="https://facebook.com" icon={<Facebook size={18} />} />
              <SocialLink href="https://twitter.com" icon={<Twitter size={18} />} />
              <SocialLink href="https://instagram.com" icon={<Instagram size={18} />} />
              <SocialLink href="https://linkedin.com" icon={<Linkedin size={18} />} />
            </div>

            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-ghana-green dark:text-ghana-gold mt-0.5" />
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Email Us</h4>
                  <a href="mailto:<EMAIL>" className="text-gray-500 hover:text-ghana-green dark:text-gray-400 dark:hover:text-ghana-gold transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Phone className="h-5 w-5 text-ghana-green dark:text-ghana-gold mt-0.5" />
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Call Us</h4>
                  <a href="tel:+233500000000" className="text-gray-500 hover:text-ghana-green dark:text-gray-400 dark:hover:text-ghana-gold transition-colors">
                    +233 50 000 0000
                  </a>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-ghana-green dark:text-ghana-gold mt-0.5" />
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Visit Us</h4>
                  <p className="text-gray-500 dark:text-gray-400">
                    Accra, Ghana
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="md:col-span-2">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-6 text-lg">Product</h4>
            <ul className="space-y-3">
              <FooterLink to="/features">Features</FooterLink>
              <FooterLink to="/pricing">Pricing</FooterLink>
              <FooterLink to="/demo">Request Demo</FooterLink>
              <FooterLink to="/security-policy">Security</FooterLink>
              <FooterLink to="/roadmap">Roadmap</FooterLink>
            </ul>
          </div>

          <div className="md:col-span-2">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-6 text-lg">Company</h4>
            <ul className="space-y-3">
              <FooterLink to="/about">About Us</FooterLink>
              <FooterLink to="/contact">Contact</FooterLink>
              <FooterLink to="/careers">Careers</FooterLink>
              <FooterLink to="/blog">Blog</FooterLink>
              <FooterLink to="/press">Press</FooterLink>
            </ul>
          </div>

          <div className="md:col-span-2">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-6 text-lg">Legal</h4>
            <ul className="space-y-3">
              <FooterLink to="/terms">Terms of Service</FooterLink>
              <FooterLink to="/privacy">Privacy Policy</FooterLink>
              <FooterLink to="/compliance">GRA Compliance</FooterLink>
              <FooterLink to="/security-policy">Security Policy</FooterLink>
            </ul>
          </div>

          <div className="md:col-span-2">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-6 text-lg">Resources</h4>
            <ul className="space-y-3">
              <FooterLink to="/help-center">Help Center</FooterLink>
              <FooterLink to="/guides">Guides</FooterLink>
              <FooterLink to="/api-docs">API Documentation</FooterLink>
              <FooterLink to="/community">Community</FooterLink>
              <FooterLink to="/faqs">FAQs</FooterLink>
            </ul>
          </div>
        </div>

        <div className="justify-center border-t border-gray-200 dark:border-gray-700 mt-16 pt-8 flex flex-col md:flex-row items-center">
          <p className="text-gray-500 dark:text-gray-400 text-sm mb-4 md:mb-0">
            &copy; 2025 Payvoicer. All rights reserved.
          </p>

          {/* <div className="flex gap-4 items-center bg-white/80 backdrop-blur-sm py-2 px-4 rounded-full shadow-sm">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/thumb/1/19/Flag_of_Ghana.svg/1200px-Flag_of_Ghana.svg.png"
              alt="Ghana Flag"
              className="h-5 shadow-sm rounded-sm"
            />
            {/* <span className="text-sm text-gray-600 flex items-center">
              Made with <Heart className="h-3 w-3 mx-1 text-red-500 fill-red-500 animate-pulse" /> in Tamale, Ghana
            </span> }
          </div> */}
        </div>
      </div>
    </footer>
  );
};

export default Footer;
