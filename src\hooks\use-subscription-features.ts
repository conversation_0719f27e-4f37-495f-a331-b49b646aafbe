import { useSubscription, SubscriptionTier } from '@/contexts/SubscriptionContext';

// Define feature keys
export type FeatureKey =
  | 'unlimited_invoices'
  | 'advanced_client_management'
  | 'team_members'
  | 'analytics'
  | 'basic_reports'
  | 'advanced_analytics'
  | 'custom_branding'
  | 'priority_support';

// Define feature limits
export interface FeatureLimits {
  maxInvoicesPerMonth: number;
  maxTeamMembers: number;
  maxClients: number;
  canAccessAnalytics: boolean;
  canAccessBasicReports: boolean;
  canAccessAdvancedAnalytics: boolean;
  canAccessCustomBranding: boolean;
  canAccessPrioritySupport: boolean;
}

/**
 * Custom hook to check subscription features and limits
 */
export const useSubscriptionFeatures = () => {
  const { tier, isActive, canAccessFeature, canAccessTier } = useSubscription();

  // Get feature limits based on subscription tier
  const getFeatureLimits = (): FeatureLimits => {
    // Default limits for free tier
    const limits: FeatureLimits = {
      maxInvoicesPerMonth: 5,
      maxTeamMembers: 1,
      maxClients: 10,
      canAccessAnalytics: false,
      canAccessBasicReports: false,
      canAccessAdvancedAnalytics: false,
      canAccessCustomBranding: false,
      canAccessPrioritySupport: false
    };

    if (!isActive) return limits;

    // Update limits based on tier
    switch (tier) {
      case 'business':
        return {
          maxInvoicesPerMonth: Infinity,
          maxTeamMembers: Infinity,
          maxClients: Infinity,
          canAccessAnalytics: true,
          canAccessBasicReports: true,
          canAccessAdvancedAnalytics: true,
          canAccessCustomBranding: true,
          canAccessPrioritySupport: true
        };
      case 'freelancer':
        return {
          maxInvoicesPerMonth: Infinity,
          maxTeamMembers: 2,
          maxClients: 50,
          canAccessAnalytics: true,
          canAccessBasicReports: true,
          canAccessAdvancedAnalytics: false,
          canAccessCustomBranding: false,
          canAccessPrioritySupport: false
        };
      default:
        return limits;
    }
  };

  // Check if user can access a specific feature
  const canAccess = (feature: FeatureKey): boolean => {
    switch (feature) {
      case 'unlimited_invoices':
        return tier !== 'free';
      case 'advanced_client_management':
        return tier !== 'free';
      case 'team_members':
        return tier !== 'free';
      case 'analytics':
        return tier !== 'free';
      case 'basic_reports':
        return tier === 'freelancer' || tier === 'business' || tier === 'enterprise';
      case 'advanced_analytics':
        return tier === 'business' || tier === 'enterprise';
      case 'custom_branding':
        return tier === 'business' || tier === 'enterprise';
      case 'priority_support':
        return tier === 'business' || tier === 'enterprise';
      default:
        return false;
    }
  };

  // Get the required tier for a feature
  const getRequiredTierForFeature = (feature: FeatureKey): SubscriptionTier => {
    switch (feature) {
      case 'unlimited_invoices':
      case 'advanced_client_management':
      case 'team_members':
      case 'analytics':
      case 'basic_reports':
        return 'freelancer';
      case 'advanced_analytics':
      case 'custom_branding':
      case 'priority_support':
        return 'business';
      default:
        return 'free';
    }
  };

  return {
    tier,
    isActive,
    canAccessFeature,
    canAccessTier,
    canAccess,
    getFeatureLimits,
    getRequiredTierForFeature
  };
};
