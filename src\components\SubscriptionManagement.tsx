import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { useSession } from '@/contexts/SessionContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Loader2, AlertCircle, CheckCircle, Calendar, CreditCard, Clock, ArrowUpCircle, ArrowRight, Lock } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { updateUserSubscription } from '@/services/databaseService';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import { AnimatedCard } from '@/components/ui/animated-card';
import { AnimatedButton } from '@/components/ui/animated-button';
import AnimatedContainer from '@/components/ui/animated-container';

// Subscription plan details
const SUBSCRIPTION_PLANS = {
  free: {
    name: 'Free',
    price: 'Free',
    color: 'bg-gray-500',
    features: ['5 invoices per month', '3 clients', 'Basic invoicing']
  },
  freelancer: {
    name: 'Freelancer',
    price: '₵49/month',
    yearlyPrice: '₵490/year',
    color: 'bg-blue-500',
    features: ['20 invoices per month', '10 clients', 'Invoice customization']
  },
  business: {
    name: 'Business',
    price: '₵99/month',
    yearlyPrice: '₵990/year',
    color: 'bg-purple-500',
    features: ['50 invoices per month', '50 clients', 'Team members', 'Reports']
  },
  enterprise: {
    name: 'Enterprise',
    price: '₵199/month',
    yearlyPrice: '₵1990/year',
    color: 'bg-ghana-green',
    features: ['Unlimited invoices', 'Unlimited clients', 'Advanced reports', 'API access']
  }
};

const SubscriptionManagement: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useSession();
  const {
    subscription,
    isLoading,
    tier,
    isActive,
    daysUntilExpiration,
    nextBillingDate,
    billingCycle,
    refreshSubscription,
    invoiceUsage,
    clientUsage,
    updateUsageCounts
  } = useSubscription();

  const [isUpdating, setIsUpdating] = useState(false);

  // Format date for display
  const formatDate = (date: Date | null): string => {
    if (!date) return 'N/A';
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Update usage counts when component mounts
  useEffect(() => {
    if (user) {
      updateUsageCounts();
    }
  }, [user, updateUsageCounts]);

  // Toggle billing cycle - redirects to secure subscription management
  const toggleBillingCycle = () => {
    // Show toast notification
    toast({
      title: 'Secure Billing Cycle Change',
      description: 'For security reasons, billing cycle changes must be done through the secure subscription management page.',
      variant: 'default'
    });

    // Redirect to secure subscription management page
    navigate('/subscription/manage');
  };

  // Render loading state
  if (isLoading) {
    return (
      <AnimatedCard
        className="w-full"
        title="Subscription"
        description="Loading your subscription details..."
        animationType="static"
      >
        <div className="flex flex-col items-center justify-center py-8 space-y-4">
          <motion.div
            animate={{
              rotate: 360,
              transition: { repeat: Infinity, duration: 1, ease: "linear" }
            }}
          >
            <Loader2 className="h-8 w-8 text-ghana-green" />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.3 }}
            className="text-sm text-muted-foreground"
          >
            Retrieving your subscription information...
          </motion.div>
        </div>
      </AnimatedCard>
    );
  }

  // Get plan details
  const planDetails = SUBSCRIPTION_PLANS[tier as keyof typeof SUBSCRIPTION_PLANS] || SUBSCRIPTION_PLANS.free;

  return (
    <AnimatedContainer type="scale" duration={0.5}>
      <Card className="w-full">
        <CardHeader>
          <motion.div
            className="flex justify-between items-center"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div>
              <CardTitle>Subscription</CardTitle>
              <CardDescription>Manage your subscription plan</CardDescription>
            </div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Badge className={`${planDetails.color} text-white`}>
                {planDetails.name}
              </Badge>
            </motion.div>
          </motion.div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Subscription Status */}
          <motion.div
            className="flex items-center justify-between"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="flex items-center gap-2">
              {isActive ? (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 15, delay: 0.5 }}
                >
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </motion.div>
              ) : (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 15, delay: 0.5 }}
                >
                  <AlertCircle className="h-5 w-5 text-red-500" />
                </motion.div>
              )}
              <span className="font-medium">Status:</span>
            </div>
            <span>{isActive ? 'Active' : 'Inactive'}</span>
          </motion.div>

        {/* Billing Cycle */}
        <motion.div
          className="flex items-center justify-between"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="flex items-center gap-2">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 500, damping: 15, delay: 0.6 }}
            >
              <CreditCard className="h-5 w-5 text-gray-500" />
            </motion.div>
            <span className="font-medium">Billing Cycle:</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="capitalize">{billingCycle}</span>
            {tier !== 'free' && (
              <AnimatedButton
                variant="outline"
                size="sm"
                onClick={toggleBillingCycle}
                disabled={isUpdating}
                animationType="scale"
              >
                {isUpdating ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Switch to {billingCycle === 'monthly' ? 'Yearly' : 'Monthly'}
              </AnimatedButton>
            )}
          </div>
        </motion.div>

        {/* Next Billing Date */}
        {tier !== 'free' && nextBillingDate && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-gray-500" />
              <span className="font-medium">Next Billing Date:</span>
            </div>
            <span>{formatDate(nextBillingDate)}</span>
          </div>
        )}

        {/* Days Until Expiration */}
        {tier !== 'free' && daysUntilExpiration !== null && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-gray-500" />
                <span className="font-medium">Days Until Expiration:</span>
              </div>
              <span className={daysUntilExpiration <= 7 ? 'text-red-500 font-bold' : ''}>
                {daysUntilExpiration} days
              </span>
            </div>
            {daysUntilExpiration <= 7 && (
              <div className="bg-red-50 p-3 rounded-md text-sm text-red-600 flex items-start gap-2">
                <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Your subscription will expire soon!</p>
                  <p>Renew now to avoid being downgraded to the free plan.</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Usage Limits */}
        <div className="space-y-4 pt-2">
          <h3 className="font-medium text-sm text-gray-500">Usage This Month</h3>

          {tier === 'enterprise' ? (
            <div className="space-y-4">
              {/* Enterprise Unlimited Usage */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Invoices (Including Drafts)</span>
                  <span className="text-ghana-green font-medium">
                    {invoiceUsage.used} / Unlimited
                  </span>
                </div>
                <Progress
                  value={100}
                  className="h-2 bg-ghana-green/30"
                />
                <p className="text-xs text-gray-500">
                  Your Enterprise plan includes unlimited invoices.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Clients</span>
                  <span className="text-ghana-green font-medium">
                    {clientUsage.used} / Unlimited
                  </span>
                </div>
                <Progress
                  value={100}
                  className="h-2 bg-ghana-green/30"
                />
                <p className="text-xs text-gray-500">
                  Your Enterprise plan includes unlimited clients.
                </p>
              </div>
            </div>
          ) : (
            <>
              {/* Invoice Usage for non-Enterprise tiers */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Invoices (Including Drafts)</span>
                  <span className={invoiceUsage.limit && invoiceUsage.used > invoiceUsage.limit ? "text-red-600 font-bold" : ""}>
                    {invoiceUsage.used} / {invoiceUsage.limit === null ? '∞' : invoiceUsage.limit}
                  </span>
                </div>
                <Progress
                  value={invoiceUsage.limit ? Math.min((invoiceUsage.used / invoiceUsage.limit) * 100, 100) : 0}
                  className={`h-2 ${invoiceUsage.limit && invoiceUsage.used >= invoiceUsage.limit ? "bg-red-600" : ""}`}
                />
                {invoiceUsage.limit && invoiceUsage.used > invoiceUsage.limit ? (
                  <div className="text-xs text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    <span>You've exceeded your monthly invoice limit. No more invoices can be created until next month or until you upgrade.</span>
                  </div>
                ) : invoiceUsage.used > 0 && invoiceUsage.limit && invoiceUsage.used >= invoiceUsage.limit * 0.8 && (
                  <div className="text-xs text-amber-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    <span>You're approaching your monthly invoice limit.</span>
                  </div>
                )}
                <p className="text-xs text-gray-500">
                  Note: All invoices (including drafts) count toward your monthly limit.
                </p>
              </div>

              {/* Client Usage for non-Enterprise tiers */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Clients</span>
                  <span className={clientUsage.limit && clientUsage.used > clientUsage.limit ? "text-red-600 font-bold" : ""}>
                    {clientUsage.used} / {clientUsage.limit === null ? '∞' : clientUsage.limit}
                  </span>
                </div>
                <Progress
                  value={clientUsage.limit ? Math.min((clientUsage.used / clientUsage.limit) * 100, 100) : 0}
                  className={`h-2 ${clientUsage.limit && clientUsage.used >= clientUsage.limit ? "bg-red-600" : ""}`}
                />
                {clientUsage.limit && clientUsage.used > clientUsage.limit ? (
                  <div className="text-xs text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    <span>You've exceeded your client limit. No more clients can be added until you upgrade your plan.</span>
                  </div>
                ) : clientUsage.used > 0 && clientUsage.limit && clientUsage.used >= clientUsage.limit * 0.8 && (
                  <div className="text-xs text-amber-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    <span>You're approaching your client limit.</span>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Plan Features */}
        <div className="space-y-3 pt-2">
          <h3 className="font-medium text-sm text-gray-500">Plan Features</h3>
          <ul className="space-y-2">
            {planDetails.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-ghana-green" />
                {feature}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        {tier === 'free' ? (
          <motion.div
            className="w-full"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <AnimatedButton
              className="w-full bg-ghana-green hover:bg-ghana-green/90"
              onClick={() => navigate('/subscription/manage')}
              animationType="lift"
            >
              <ArrowUpCircle className="mr-2 h-4 w-4" />
              Upgrade Plan
            </AnimatedButton>
          </motion.div>
        ) : (
          <motion.div
            className="w-full flex flex-col sm:flex-row gap-3 sm:gap-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <AnimatedButton
              variant="outline"
              className="w-full sm:w-auto order-2 sm:order-1"
              onClick={() => navigate('/subscription/manage')}
              animationType="scale"
            >
              Manage Subscription
            </AnimatedButton>
            <AnimatedButton
              className="w-full sm:w-auto bg-ghana-green hover:bg-ghana-green/90 order-1 sm:order-2 sm:ml-auto"
              onClick={() => navigate('/subscription/manage')}
              animationType="lift"
            >
              <Lock className="mr-2 h-4 w-4" />
              <span className="whitespace-nowrap">Secure Subscription Portal</span>
            </AnimatedButton>
          </motion.div>
        )}
      </CardFooter>
    </Card>
    </AnimatedContainer>
  );
};

export default SubscriptionManagement;
