import React, { ButtonHTMLAttributes } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface AnimatedButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  isLoading?: boolean;
  loadingText?: string;
  animationType?: 'scale' | 'lift' | 'pulse' | 'none';
}

/**
 * AnimatedButton - An enhanced button component with animations
 */
export function AnimatedButton({
  children,
  variant = 'default',
  size = 'default',
  className,
  isLoading = false,
  loadingText,
  animationType = 'scale',
  ...props
}: AnimatedButtonProps) {
  // Animation variants
  const getAnimationProps = () => {
    switch (animationType) {
      case 'scale':
        return {
          whileHover: { scale: 1.03 },
          whileTap: { scale: 0.97 },
          transition: { duration: 0.2 }
        };
      case 'lift':
        return {
          whileHover: { y: -3, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' },
          whileTap: { y: 0, boxShadow: 'none' },
          transition: { duration: 0.2 }
        };
      case 'pulse':
        return {
          whileHover: { 
            scale: [1, 1.03, 1.01],
            transition: { 
              duration: 0.5,
              times: [0, 0.5, 1],
              repeat: Infinity,
              repeatType: 'reverse'
            }
          },
          whileTap: { scale: 0.97 }
        };
      case 'none':
      default:
        return {};
    }
  };

  return (
    <motion.div {...getAnimationProps()}>
      <Button
        variant={variant}
        size={size}
        className={cn(
          'relative overflow-hidden transition-all duration-300',
          isLoading && 'cursor-not-allowed',
          className
        )}
        disabled={isLoading || props.disabled}
        {...props}
      >
        {isLoading ? (
          <>
            <span className="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></span>
            {loadingText || children}
          </>
        ) : (
          <>
            {children}
            <span className="absolute inset-0 h-full w-full bg-white/10 opacity-0 transition-opacity duration-300 hover:opacity-100"></span>
          </>
        )}
      </Button>
    </motion.div>
  );
}

export default AnimatedButton;
