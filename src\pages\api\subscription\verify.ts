import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { verifyPaystackTransaction } from '@/services/serverPaystackService';
import { getUserSubscription, updateUserSubscription } from '@/services/databaseService';
import { logSubscriptionTransaction } from '@/services/secureSubscriptionService';

/**
 * API endpoint to securely verify subscription payments
 * 
 * This endpoint handles the verification of subscription payments
 * and updates the user's subscription accordingly.
 * 
 * It includes security measures to prevent payment gateway bypass:
 * - Server-side verification of payment
 * - Token-based verification
 * - IP logging
 * - Transaction logging
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // Create authenticated Supabase client
  const supabase = createServerSupabaseClient({ req, res });
  
  // Check if user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();
  
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    const { reference, upgradeToken } = req.body;
    
    if (!reference) {
      return res.status(400).json({ error: 'Payment reference is required' });
    }
    
    // Get the user's subscription
    const subscription = await getUserSubscription(session.user.id);
    
    if (!subscription) {
      return res.status(404).json({ error: 'Subscription not found' });
    }
    
    // If upgradeToken is provided, verify it
    if (upgradeToken) {
      if (subscription.upgrade_token !== upgradeToken) {
        return res.status(400).json({ error: 'Invalid upgrade token' });
      }
      
      // Check if token has expired
      if (subscription.upgrade_token_expires_at && new Date(subscription.upgrade_token_expires_at) < new Date()) {
        return res.status(400).json({ error: 'Upgrade token has expired' });
      }
    }
    
    // Get client IP for security logging
    const clientIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress || null;
    
    // Verify the payment with Paystack
    const verificationResult = await verifyPaystackTransaction(reference, session.user.id, subscription.id);
    
    if (!verificationResult || verificationResult.status !== 'success') {
      // Log failed verification
      await logSubscriptionTransaction({
        user_id: session.user.id,
        subscription_id: subscription.id,
        transaction_type: 'payment_failure',
        payment_gateway: 'paystack',
        payment_gateway_reference: reference,
        verification_status: 'failed',
        client_ip: clientIp as string | null,
        user_agent: req.headers['user-agent'] || null
      });
      
      return res.status(400).json({ error: 'Payment verification failed' });
    }
    
    // Extract plan details from metadata
    const { data } = verificationResult;
    const metadata = data.metadata || {};
    const planType = metadata.planType || subscription.plan_type;
    const billingCycle = metadata.period || subscription.billing_cycle;
    
    // Calculate next billing date
    const now = new Date();
    const nextBillingDate = billingCycle === 'yearly'
      ? new Date(now.setFullYear(now.getFullYear() + 1))
      : new Date(now.setMonth(now.getMonth() + 1));
    
    // Update subscription
    await updateUserSubscription(session.user.id, {
      plan_type: planType,
      billing_cycle: billingCycle,
      status: 'active',
      payment_status: 'active',
      last_payment_date: new Date().toISOString(),
      next_payment_date: nextBillingDate.toISOString(),
      upgrade_token: null,
      upgrade_token_expires_at: null,
      last_verification_ip: clientIp as string | null
    });
    
    // Log successful transaction
    await logSubscriptionTransaction({
      user_id: session.user.id,
      subscription_id: subscription.id,
      transaction_type: metadata.previous_plan ? 'upgrade' : 'renew',
      previous_plan: metadata.previous_plan || subscription.plan_type,
      new_plan: planType,
      previous_billing_cycle: metadata.previous_billing_cycle || subscription.billing_cycle,
      new_billing_cycle: billingCycle,
      amount: data.amount / 100, // Convert from kobo to GHS
      payment_gateway: 'paystack',
      payment_gateway_reference: reference,
      payment_gateway_response: verificationResult,
      verification_status: 'verified',
      client_ip: clientIp as string | null,
      user_agent: req.headers['user-agent'] || null
    });
    
    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      plan: planType,
      billingCycle
    });
  } catch (error) {
    console.error('Error verifying subscription payment:', error);
    return res.status(500).json({ error: 'An error occurred while verifying payment' });
  }
}
