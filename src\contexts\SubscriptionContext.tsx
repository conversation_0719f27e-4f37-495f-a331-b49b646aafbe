import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSession } from './SessionContext';
import { Tables } from '@/integrations/supabase/types';
import { supabase } from '@/integrations/supabase/client';
import {
  getUserSubscription,
  hasFeatureAccess,
  getDaysUntilExpiration,
  isSubscriptionExpired,
  getInvoiceLimit,
  getClientLimit,
  hasReachedInvoiceLimit,
  hasReachedClientLimit
} from '@/services/databaseService';

// Define subscription tiers
export type SubscriptionTier = 'free' | 'freelancer' | 'business' | 'enterprise';

// Define subscription context type
interface SubscriptionContextType {
  subscription: Tables<"subscriptions"> | null;
  isLoading: boolean;
  tier: SubscriptionTier;
  isActive: boolean;
  daysUntilExpiration: number | null;
  nextBillingDate: Date | null;
  billingCycle: string;
  invoiceUsage: { used: number; limit: number | null };
  clientUsage: { used: number; limit: number | null };
  refreshSubscription: () => Promise<void>;
  canAccessFeature: (feature: string) => Promise<boolean>;
  canAccessTier: (requiredTier: SubscriptionTier) => boolean;
  getInvoiceLimit: () => Promise<number | null>;
  getClientLimit: () => Promise<number | null>;
  hasReachedInvoiceLimit: () => Promise<boolean>;
  hasReachedClientLimit: () => Promise<boolean>;
  updateUsageCounts: () => Promise<void>;
}

// Default context values
const defaultContextValue: SubscriptionContextType = {
  subscription: null,
  isLoading: true,
  tier: 'free',
  isActive: false,
  daysUntilExpiration: null,
  nextBillingDate: null,
  billingCycle: 'monthly',
  invoiceUsage: { used: 0, limit: 5 },
  clientUsage: { used: 0, limit: 3 },
  refreshSubscription: async () => {},
  canAccessFeature: async () => false,
  canAccessTier: () => false,
  getInvoiceLimit: async () => 5,
  getClientLimit: async () => 3,
  hasReachedInvoiceLimit: async () => false,
  hasReachedClientLimit: async () => false,
  updateUsageCounts: async () => {}
};

// Create the context
const SubscriptionContext = createContext<SubscriptionContextType>(defaultContextValue);

// Create the provider component
export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useSession();
  const [subscription, setSubscription] = useState<Tables<"subscriptions"> | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [daysUntilExpiration, setDaysUntilExpiration] = useState<number | null>(null);
  const [nextBillingDate, setNextBillingDate] = useState<Date | null>(null);
  const [invoiceUsage, setInvoiceUsage] = useState<{ used: number; limit: number | null }>({ used: 0, limit: 5 });
  const [clientUsage, setClientUsage] = useState<{ used: number; limit: number | null }>({ used: 0, limit: 3 });

  // Determine the current tier based on subscription
  const tier: SubscriptionTier = (subscription?.plan_type as SubscriptionTier) || 'free';

  // Check if subscription is active
  const isActive = subscription?.status === 'active';

  // Get billing cycle
  const billingCycle = subscription?.billing_cycle || 'monthly';

  // Function to load subscription data
  const loadSubscription = async () => {
    if (!user) {
      setSubscription(null);
      setDaysUntilExpiration(null);
      setNextBillingDate(null);
      setInvoiceUsage({ used: 0, limit: 5 });
      setClientUsage({ used: 0, limit: 3 });
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      // Get the user's subscription
      const userSubscription = await getUserSubscription(user.id);
      setSubscription(userSubscription);

      // Calculate days until expiration
      if (userSubscription) {
        // Log the subscription details for debugging
        console.log('Loaded subscription:', userSubscription);

        // Calculate days until expiration directly instead of using the service
        // This ensures it's always up-to-date with the current date
        if (userSubscription.current_period_end) {
          const endDate = new Date(userSubscription.current_period_end);
          const now = new Date();
          const diffMs = endDate.getTime() - now.getTime();
          const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
          setDaysUntilExpiration(diffDays > 0 ? diffDays : 0);
        } else {
          setDaysUntilExpiration(null);
        }

        // Set next billing date
        if (userSubscription.next_payment_date) {
          setNextBillingDate(new Date(userSubscription.next_payment_date));
        } else if (userSubscription.current_period_end) {
          setNextBillingDate(new Date(userSubscription.current_period_end));
        } else {
          setNextBillingDate(null);
        }

        // Update usage counts - this will now handle enterprise tier correctly
        await updateUsageCounts();
      } else {
        // If no subscription found, set to free tier defaults
        setInvoiceUsage({ used: 0, limit: 5 });
        setClientUsage({ used: 0, limit: 3 });
      }
    } catch (error) {
      console.error('Error loading subscription:', error);
      // Set default values in case of error
      setInvoiceUsage({ used: 0, limit: 5 });
      setClientUsage({ used: 0, limit: 3 });
    } finally {
      setIsLoading(false);
    }
  };

  // Load subscription when user changes
  useEffect(() => {
    if (user) {
      loadSubscription();
    } else {
      setSubscription(null);
      setDaysUntilExpiration(null);
      setNextBillingDate(null);
    }
  }, [user]);

  // Function to refresh subscription data
  const refreshSubscription = async () => {
    try {
      await loadSubscription();
    } catch (error) {
      console.error('Error refreshing subscription:', error);
    }
  };

  // Function to check if user can access a specific feature
  const canAccessFeature = async (feature: string): Promise<boolean> => {
    if (!user) return false;
    return hasFeatureAccess(user.id, feature);
  };

  // Function to check if user can access a specific tier
  const canAccessTier = (requiredTier: SubscriptionTier): boolean => {
    if (!isActive) return requiredTier === 'free';

    switch (tier) {
      case 'enterprise':
        return true; // Enterprise tier can access all tiers
      case 'business':
        return requiredTier === 'free' || requiredTier === 'freelancer' || requiredTier === 'business';
      case 'freelancer':
        return requiredTier === 'free' || requiredTier === 'freelancer';
      case 'free':
      default:
        return requiredTier === 'free';
    }
  };

  // Function to get invoice limit
  const getInvoiceLimitFn = async (): Promise<number | null> => {
    if (!user) return 5; // Default to free tier limit
    return getInvoiceLimit(user.id);
  };

  // Function to get client limit
  const getClientLimitFn = async (): Promise<number | null> => {
    if (!user) return 3; // Default to free tier limit
    return getClientLimit(user.id);
  };

  // Function to check if user has reached invoice limit
  const hasReachedInvoiceLimitFn = async (): Promise<boolean> => {
    if (!user) return false;
    return hasReachedInvoiceLimit(user.id);
  };

  // Function to check if user has reached client limit
  const hasReachedClientLimitFn = async (): Promise<boolean> => {
    if (!user) return false;
    return hasReachedClientLimit(user.id);
  };

  // Function to update usage counts (invoices and clients)
  const updateUsageCounts = async (): Promise<void> => {
    if (!user) return;

    try {
      // Special handling for enterprise tier - always set to unlimited
      if (tier === 'enterprise') {
        // Still count usage for display purposes, but set limit to null (unlimited)
        // Get the current month's start and end dates
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();

        // Count all invoices created this month (including drafts)
        const { count: invoiceCount, error: invoiceError } = await supabase
          .from("invoices")
          .select("id", { count: "exact", head: true })
          .eq("user_id", user.id)
          .gte("created_at", startOfMonth)
          .lte("created_at", endOfMonth);

        if (invoiceError) {
          console.error("Error counting invoices:", invoiceError);
        } else {
          setInvoiceUsage({ used: invoiceCount || 0, limit: null }); // null means unlimited
        }

        // Count all clients
        const { count: clientCount, error: clientError } = await supabase
          .from("clients")
          .select("id", { count: "exact", head: true })
          .eq("user_id", user.id);

        if (clientError) {
          console.error("Error counting clients:", clientError);
        } else {
          setClientUsage({ used: clientCount || 0, limit: null }); // null means unlimited
        }

        return; // Exit early for enterprise tier
      }

      // For non-enterprise tiers, get limits from the database
      // Get invoice limit for the current tier
      const invoiceLimit = await getInvoiceLimitFn();

      // Get client limit for the current tier
      const clientLimit = await getClientLimitFn();

      // Get the current month's start and end dates
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();

      // Count all invoices created this month (including drafts)
      const { count: invoiceCount, error: invoiceError } = await supabase
        .from("invoices")
        .select("id", { count: "exact", head: true })
        .eq("user_id", user.id)
        .gte("created_at", startOfMonth)
        .lte("created_at", endOfMonth);

      if (invoiceError) {
        console.error("Error counting invoices:", invoiceError);
      } else {
        setInvoiceUsage({ used: invoiceCount || 0, limit: invoiceLimit });
      }

      // Count all clients
      const { count: clientCount, error: clientError } = await supabase
        .from("clients")
        .select("id", { count: "exact", head: true })
        .eq("user_id", user.id);

      if (clientError) {
        console.error("Error counting clients:", clientError);
      } else {
        setClientUsage({ used: clientCount || 0, limit: clientLimit });
      }
    } catch (error) {
      console.error("Error updating usage counts:", error);
    }
  };

  // Load subscription when user changes
  useEffect(() => {
    if (user) {
      loadSubscription();
    } else {
      setSubscription(null);
      setDaysUntilExpiration(null);
      setNextBillingDate(null);
      setInvoiceUsage({ used: 0, limit: 5 });
      setClientUsage({ used: 0, limit: 3 });
    }
  }, [user]);

  // Update days until expiration daily
  useEffect(() => {
    // Skip if no subscription or no expiration date
    if (!subscription || !subscription.current_period_end) return;

    // Function to update days until expiration
    const updateDaysUntilExpiration = () => {
      const endDate = new Date(subscription.current_period_end);
      const now = new Date();
      const diffMs = endDate.getTime() - now.getTime();
      const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
      setDaysUntilExpiration(diffDays > 0 ? diffDays : 0);
    };

    // Update immediately
    updateDaysUntilExpiration();

    // Set up interval to update daily at midnight
    const now = new Date();
    const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    const timeUntilMidnight = tomorrow.getTime() - now.getTime();

    // Set timeout to run at midnight
    const midnightTimeout = setTimeout(() => {
      updateDaysUntilExpiration();

      // Then set up daily interval
      const dailyInterval = setInterval(updateDaysUntilExpiration, 24 * 60 * 60 * 1000);

      // Clean up interval on unmount
      return () => clearInterval(dailyInterval);
    }, timeUntilMidnight);

    // Clean up timeout on unmount
    return () => clearTimeout(midnightTimeout);
  }, [subscription]);

  // Context value
  const value: SubscriptionContextType = {
    subscription,
    isLoading,
    tier,
    isActive,
    daysUntilExpiration,
    nextBillingDate,
    billingCycle,
    invoiceUsage,
    clientUsage,
    refreshSubscription,
    canAccessFeature,
    canAccessTier,
    getInvoiceLimit: getInvoiceLimitFn,
    getClientLimit: getClientLimitFn,
    hasReachedInvoiceLimit: hasReachedInvoiceLimitFn,
    hasReachedClientLimit: hasReachedClientLimitFn,
    updateUsageCounts
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};

// Custom hook to use the subscription context
export const useSubscription = (): SubscriptionContextType => {
  try {
    const context = useContext(SubscriptionContext);
    return context;
  } catch (error) {
    console.error('Error using subscription context:', error);
    return defaultContextValue;
  }
};
