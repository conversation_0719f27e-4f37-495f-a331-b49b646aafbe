import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, Calendar, CreditCard, Loader2, CheckCircle, AlertTriangle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useSession } from '@/contexts/SessionContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { SUBSCRIPTION_PLANS } from '@/services/subscriptionService';
import SubscriptionPlans from '@/components/subscription/SubscriptionPlans';
import { motion } from 'framer-motion';
import { AnimatedButton } from '@/components/ui/animated-button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

const SubscriptionManagement: React.FC = () => {
  const { user } = useSession();
  const { subscription, tier, isActive, refreshSubscription } = useSubscription();
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Use a ref to track if we've already loaded the data
  const dataLoaded = React.useRef(false);

  useEffect(() => {
    const loadData = async () => {
      if (dataLoaded.current) return;

      setIsLoading(true);
      try {
        await refreshSubscription();
        dataLoaded.current = true;
      } catch (error) {
        console.error('Error refreshing subscription data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load subscription data',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      loadData();
    }

    // Only depend on user, not on refreshSubscription
  }, [user]);

  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-GH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get plan details based on tier
  const getPlanDetails = () => {
    switch (tier) {
      case 'freelancer':
        return SUBSCRIPTION_PLANS.FREELANCER;
      case 'business':
        return SUBSCRIPTION_PLANS.BUSINESS;
      default:
        return SUBSCRIPTION_PLANS.FREE;
    }
  };

  // Get subscription status badge
  const getStatusBadge = () => {
    if (!subscription) {
      return (
        <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          Free Plan
        </div>
      );
    }

    switch (subscription.status) {
      case 'active':
        return (
          <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </div>
        );
      case 'canceled':
        return (
          <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Canceled
          </div>
        );
      case 'expired':
        return (
          <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertCircle className="w-3 h-3 mr-1" />
            Expired
          </div>
        );
      default:
        return (
          <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {subscription.status}
          </div>
        );
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Subscription Management</h1>
        </div>

        <Tabs defaultValue="current" className="w-full">
          <TabsList className="mb-8">
            <TabsTrigger value="current">Current Plan</TabsTrigger>
            <TabsTrigger value="upgrade">Upgrade Plan</TabsTrigger>
            <TabsTrigger value="billing">Billing History</TabsTrigger>
          </TabsList>

          <TabsContent value="current">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-ghana-green" />
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="md:col-span-2">
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>Your Subscription</CardTitle>
                          <CardDescription>
                            Details about your current subscription plan
                          </CardDescription>
                        </div>
                        {getStatusBadge()}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Plan Details</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-muted-foreground">Plan</p>
                              <p className="font-medium">{tier.charAt(0).toUpperCase() + tier.slice(1)}</p>
                            </div>

                            <div>
                              <p className="text-sm text-muted-foreground">Billing Cycle</p>
                              <p className="font-medium">
                                {subscription?.billing_cycle === 'yearly' ? 'Yearly' :
                                 subscription?.billing_cycle === 'monthly' ? 'Monthly' : 'N/A'}
                              </p>
                            </div>

                            {subscription && (
                              <>
                                <div>
                                  <p className="text-sm text-muted-foreground">Start Date</p>
                                  <p className="font-medium">{formatDate(subscription.start_date)}</p>
                                </div>

                                <div>
                                  <p className="text-sm text-muted-foreground">Next Billing Date</p>
                                  <p className="font-medium">{formatDate(subscription.next_billing_date)}</p>
                                </div>
                              </>
                            )}
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold mb-2">Features Included</h3>
                          <ul className="space-y-2">
                            {getPlanDetails().features.map((feature, index) => (
                              <li key={index} className="flex items-start">
                                <CheckCircle className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                                <span>{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {subscription && subscription.status === 'active' && (
                          <div className="pt-4 border-t border-gray-200">
                            <Button
                              variant="outline"
                              className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
                              onClick={() => {
                                // TODO: Implement cancel subscription logic
                                toast({
                                  title: 'Subscription Cancellation',
                                  description: 'Your subscription will be canceled at the end of the current billing period.',
                                });
                              }}
                            >
                              Cancel Subscription
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div>
                  <Card>
                    <CardHeader>
                      <CardTitle>Payment Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {subscription ? (
                        <div className="space-y-4">
                          <div className="flex items-center">
                            <CreditCard className="h-5 w-5 text-muted-foreground mr-2" />
                            <div>
                              <p className="text-sm text-muted-foreground">Payment Method</p>
                              <p className="font-medium">Paystack</p>
                            </div>
                          </div>

                          <div className="flex items-center">
                            <Calendar className="h-5 w-5 text-muted-foreground mr-2" />
                            <div>
                              <p className="text-sm text-muted-foreground">Billing Cycle</p>
                              <p className="font-medium">
                                {subscription.billing_cycle === 'yearly' ? 'Yearly' : 'Monthly'}
                              </p>
                            </div>
                          </div>

                          <Button
                            className="w-full mt-4"
                            onClick={() => navigate('/subscription-management?tab=upgrade')}
                          >
                            Change Plan
                          </Button>
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <p className="text-gray-500 mb-4">You are currently on the free plan</p>
                          <Button
                            className="w-full"
                            onClick={() => navigate('/subscription-management?tab=upgrade')}
                          >
                            Upgrade Now
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="upgrade">
            <SubscriptionPlans />
          </TabsContent>

          <TabsContent value="billing">
            <Card>
              <CardHeader>
                <CardTitle>Billing History</CardTitle>
                <CardDescription>
                  View your past invoices and payment history
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-ghana-green" />
                  </div>
                ) : subscription ? (
                  <div className="space-y-4">
                    {/* This would be populated with actual billing history */}
                    <div className="bg-gray-50 p-4 rounded-lg flex justify-between items-center">
                      <div>
                        <p className="font-medium">{formatDate(subscription.start_date)}</p>
                        <p className="text-sm text-gray-500">
                          {tier.charAt(0).toUpperCase() + tier.slice(1)} Plan - {subscription.billing_cycle === 'yearly' ? 'Yearly' : 'Monthly'}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          GHS {subscription.billing_cycle === 'yearly'
                            ? getPlanDetails().yearlyPrice
                            : getPlanDetails().price}
                        </p>
                        <div className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                          Paid
                        </div>
                      </div>
                    </div>

                    {/* Add more billing history items here */}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-gray-500">No billing history available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default SubscriptionManagement;
