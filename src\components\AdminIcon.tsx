import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ShieldCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { useSession } from '@/contexts/SessionContext';
import { isAdmin, isAdminEmail } from '@/utils/adminUtils';

interface AdminIconProps {
  inDropdown?: boolean;
}

const AdminIcon = ({ inDropdown = false }: AdminIconProps) => {
  const { session, user } = useSession();
  const [isAdminUser, setIsAdminUser] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  useEffect(() => {
    const checkAdminStatus = async () => {
      setIsLoading(true);

      try {
        if (!session || !user) {
          setIsAdminUser(false);
          return;
        }

        const userId = session.user.id;
        const userEmail = user.email || '';

        try {
          const adminStatus = await isAdmin(userId, userEmail);
          setIsAdminUser(adminStatus);
        } catch (error) {
          console.error('Error checking admin status:', error);
          // Fallback to just checking email if there's an error with the full check
          const isEmailAdmin = isAdminEmail(userEmail);
          setIsAdminUser(isEmailAdmin);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdminUser(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminStatus();
  }, [session, user]);

  if (isLoading || !isAdminUser) {
    return null;
  }

  const handleNavigate = (e: React.MouseEvent) => {
    e.preventDefault();
    // Use a timeout to avoid immediate navigation which can cause issues
    setTimeout(() => {
      navigate('/admin');
    }, 10);
  };

  // If this is being rendered inside a dropdown menu
  if (inDropdown) {
    return (
      <div className="w-full" onClick={handleNavigate}>
        <DropdownMenuItem className="dark:focus:bg-gray-800/50 dark:text-foreground">
          <ShieldCheck className="mr-2 h-4 w-4" />
          <span>Admin Dashboard</span>
        </DropdownMenuItem>
      </div>
    );
  }

  // Default standalone icon with tooltip
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="relative"
            onClick={handleNavigate}
          >
            <ShieldCheck className="h-5 w-5 text-primary" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Admin Dashboard</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default AdminIcon;
