-- Direct fix for RLS policies
-- This script directly addresses the "record new has no field amount" error

-- Drop all existing policies on the invoices table
DROP POLICY IF EXISTS "Allow updating public token fields" ON invoices;
DROP POLICY IF EXISTS "Allow updating status for public invoices" ON invoices;
DROP POLICY IF EXISTS "Public can update invoice status if they have the token" ON invoices;
DROP POLICY IF EXISTS "Users can update their own invoices" ON invoices;
DROP POLICY IF EXISTS "Organization members can update organization invoices" ON invoices;
DROP POLICY IF EXISTS "Allow updates with validation" ON invoices;
DROP POLICY IF EXISTS "Allow extending public token expiration" ON invoices;
DROP POLICY IF EXISTS "Allow public access to invoices with valid token" ON invoices;
DROP POLICY IF EXISTS "Users can select their own invoices" ON invoices;
DROP POLICY IF EXISTS "Users can insert their own invoices" ON invoices;
DROP POLICY IF EXISTS "Organization members can select organization invoices" ON invoices;
DROP POLICY IF EXISTS "Allow public status updates with valid token" ON invoices;
DROP POLICY IF EXISTS "Public access with valid token" ON invoices;

-- Create minimal, secure policies without any WITH CHECK clauses that could cause issues

-- Policy for users to select their own invoices
CREATE POLICY "Users can select their own invoices" ON invoices
FOR SELECT
USING (auth.uid() = user_id);

-- Policy for users to update their own invoices
CREATE POLICY "Users can update their own invoices" ON invoices
FOR UPDATE
USING (auth.uid() = user_id);

-- Policy for users to insert their own invoices
CREATE POLICY "Users can insert their own invoices" ON invoices
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Policy for organization members to select organization invoices
CREATE POLICY "Organization members can select organization invoices" ON invoices
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM organization_members
        WHERE organization_id = invoices.organization_id
        AND user_id = auth.uid()
    )
);

-- Policy for organization members to update organization invoices
CREATE POLICY "Organization members can update organization invoices" ON invoices
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM organization_members
        WHERE organization_id = invoices.organization_id
        AND user_id = auth.uid()
    )
);

-- Policy for public access to invoices with valid tokens
CREATE POLICY "Public access with valid token" ON invoices
FOR SELECT
USING (
    public_access_token IS NOT NULL
    AND (
        public_access_expires_at IS NULL
        OR public_access_expires_at > NOW()
    )
);

-- Policy for public updates to invoices with valid tokens (limited to status field)
-- Note: No WITH CHECK clause to avoid the "record new has no field amount" error
CREATE POLICY "Allow public status updates with valid token" ON invoices
FOR UPDATE
USING (
    public_access_token IS NOT NULL
    AND (
        public_access_expires_at IS NULL
        OR public_access_expires_at > NOW()
    )
);
