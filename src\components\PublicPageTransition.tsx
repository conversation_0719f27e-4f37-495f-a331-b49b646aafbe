import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface PublicPageTransitionProps {
  children: ReactNode;
  className?: string;
}

/**
 * PublicPageTransition - A component that adds smooth transitions for public pages
 * Designed specifically for landing, features, pricing, about and contact pages
 */
const PublicPageTransition: React.FC<PublicPageTransitionProps> = ({ children, className = '' }) => {
  // Page transition variants
  const pageVariants = {
    initial: {
      opacity: 0,
      y: 20
    },
    in: {
      opacity: 1,
      y: 0
    },
    exit: {
      opacity: 0,
      y: -20
    }
  };

  // Page transition options
  const pageTransition = {
    type: 'tween',
    ease: [0.25, 0.1, 0.25, 1], // cubic-bezier easing for smoother motion
    duration: 0.6
  };

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="in"
      exit="exit"
      variants={pageVariants}
      transition={pageTransition}
    >
      {children}
    </motion.div>
  );
};

export default PublicPageTransition;
