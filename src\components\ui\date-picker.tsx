import * as React from "react";
import { format, isSameDay } from "date-fns";
import { Calendar as CalendarIcon, Check } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DatePickerProps {
  date?: Date;
  setDate: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

export function DatePicker({
  date,
  setDate,
  className,
  placeholder = "Select date",
  disabled = false,
}: DatePickerProps) {
  // Use internal state to track the selected date and popover open state
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(date);
  const [open, setOpen] = React.useState(false);

  // Add custom CSS to completely override today styling when any date is selected
  React.useEffect(() => {
    // Add a style tag to handle date highlighting
    const styleTag = document.createElement('style');
    styleTag.innerHTML = `
      /* When a date is selected, override today styling */
      .has-selected .rdp-day_today:not(.rdp-day_selected) {
        background-color: transparent !important;
        color: inherit !important;
        border: none !important;
        font-weight: normal !important;
      }

      /* Ensure selected date has the correct styling */
      .rdp-day_selected {
        background-color: var(--ghana-green) !important;
        color: white !important;
        border: none !important;
        font-weight: bold !important;
      }

      /* Add a subtle indicator for today when it's not selected */
      .no-selected .rdp-day_today {
        border: 1px solid var(--ghana-green) !important;
        font-weight: bold !important;
      }
    `;
    document.head.appendChild(styleTag);

    return () => {
      document.head.removeChild(styleTag);
    };
  }, []);

  // Update internal state when the external date prop changes
  React.useEffect(() => {
    setSelectedDate(date);
  }, [date]);

  // Update calendar classes when the component mounts or the selected date changes
  React.useEffect(() => {
    // When the popover opens, update the calendar classes
    if (open) {
      setTimeout(() => {
        const calendarContainer = document.querySelector('.rdp');
        if (calendarContainer) {
          if (selectedDate) {
            calendarContainer.classList.add('has-selected');
            calendarContainer.classList.remove('no-selected');
          } else {
            calendarContainer.classList.add('no-selected');
            calendarContainer.classList.remove('has-selected');
          }
        }
      }, 50);
    }
  }, [open, selectedDate]);

  // Handle date selection
  const handleSelect = (newDate: Date | undefined) => {
    // Update both internal and external state
    setSelectedDate(newDate);
    setDate(newDate);

    // Force a re-render to ensure the calendar updates correctly
    setTimeout(() => {
      // Add a class to the calendar container to indicate a date is selected
      const calendarContainer = document.querySelector('.rdp');
      if (calendarContainer) {
        if (newDate) {
          calendarContainer.classList.add('has-selected');
          calendarContainer.classList.remove('no-selected');
        } else {
          calendarContainer.classList.add('no-selected');
          calendarContainer.classList.remove('has-selected');
        }
      }

      // Close the popover after selection for better UX
      setTimeout(() => setOpen(false), 100);
    }, 10);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal",
            !selectedDate && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedDate ? format(selectedDate, "PPP") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0 max-w-[95vw] md:max-w-none" align="start">
        <div className="p-2 border-b border-gray-100 flex justify-between items-center">
          <span className="text-sm font-medium">Select a date</span>
          {selectedDate && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 px-2 text-xs"
              onClick={() => handleSelect(undefined)}
            >
              Clear
            </Button>
          )}
        </div>
        <div className={`overflow-x-auto ${selectedDate ? 'has-selected' : 'no-selected'}`}>
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleSelect}
            initialFocus
            disabled={disabled}
            modifiers={{
              // This ensures that the selected date styling takes precedence over today's styling
              selected: selectedDate ? [selectedDate] : undefined,
              today: new Date()
            }}
            modifiersClassNames={{
              selected: "bg-ghana-green text-white hover:bg-ghana-green/90 focus:bg-ghana-green/90",
              today: selectedDate ? "" : "border border-ghana-green/40"
            }}
            classNames={{
              day_selected: "bg-ghana-green text-white hover:bg-ghana-green/90 focus:bg-ghana-green/90",
              day_today: "text-ghana-green font-medium",
              day: "h-8 w-8 p-0 font-normal text-sm aria-selected:opacity-100",
              table: "w-full border-collapse space-y-1",
              head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
              cell: "h-8 w-8 text-center text-sm p-0 relative",
              caption: "flex justify-center pt-1 relative items-center",
              caption_label: "text-sm font-medium",
              nav: "space-x-1 flex items-center",
              nav_button: "h-7 w-7 bg-transparent p-0 opacity-70 hover:opacity-100",
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              month: "space-y-4"
            }}
          />
        </div>
        {selectedDate && (
          <div className="p-2 border-t border-gray-100 bg-gray-50">
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-center text-ghana-green hover:text-ghana-green/90 hover:bg-ghana-green/10"
              onClick={() => setOpen(false)}
            >
              <Check className="mr-2 h-4 w-4" />
              <span className="whitespace-nowrap text-sm">Confirm {format(selectedDate, "MMM d, yyyy")}</span>
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
