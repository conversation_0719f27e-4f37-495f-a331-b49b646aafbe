import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Check, Loader2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useSession } from '@/contexts/SessionContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { SUBSCRIPTION_PLANS, initializeSubscriptionUpgrade } from '@/services/subscriptionService';

interface SubscriptionPlansProps {
  showCurrentPlan?: boolean;
  compact?: boolean;
  onSuccess?: () => void;
}

const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({
  showCurrentPlan = true,
  compact = false,
  onSuccess
}) => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [isUpgrading, setIsUpgrading] = useState<string | null>(null);
  const { user } = useSession();
  const { tier, isActive, refreshSubscription } = useSubscription();
  const navigate = useNavigate();

  // Format price based on billing cycle
  const formatPrice = (price: number, yearlyPrice: number) => {
    if (billingCycle === 'yearly') {
      return `GHS ${yearlyPrice.toFixed(2)}/year`;
    }
    return `GHS ${price.toFixed(2)}/month`;
  };

  // Calculate savings percentage for yearly plans
  const calculateSavings = (monthlyPrice: number, yearlyPrice: number) => {
    const monthlyCost = monthlyPrice * 12;
    const yearlyCost = yearlyPrice;
    const savings = ((monthlyCost - yearlyCost) / monthlyCost) * 100;
    return Math.round(savings);
  };

  // Handle subscription upgrade
  const handleSubscriptionUpgrade = async (planType: string) => {
    if (!user) {
      // If user is not logged in, redirect to auth page
      localStorage.setItem('authMode', 'signup');
      localStorage.setItem('redirectAfterAuth', '/settings?tab=billing');
      localStorage.setItem('selectedPlan', planType);
      localStorage.setItem('selectedBillingCycle', billingCycle);
      navigate('/auth');
      return;
    }

    // Don't allow upgrading to the same plan
    if (isActive && tier === planType) {
      toast({
        title: 'Already subscribed',
        description: `You are already subscribed to the ${planType} plan.`,
        variant: 'default',
      });
      return;
    }

    // Free plan doesn't need payment
    if (planType === 'free') {
      // TODO: Implement downgrade to free plan logic
      toast({
        title: 'Plan change',
        description: 'Your plan will be downgraded to Free at the end of your current billing period.',
        variant: 'default',
      });
      return;
    }

    // Make sure planType is one of the valid tiers
    if (!['free', 'business', 'enterprise'].includes(planType)) {
      toast({
        title: 'Invalid plan',
        description: `The selected plan "${planType}" is not valid.`,
        variant: 'destructive',
      });
      return;
    }

    setIsUpgrading(planType);
    try {
      console.log(`Initializing subscription upgrade: ${planType} (${billingCycle})`);

      const result = await initializeSubscriptionUpgrade(
        user.id,
        null, // No organization for now
        planType,
        billingCycle,
        user.email || '',
        `${window.location.origin}/subscription/callback`
      );

      if (result) {
        console.log('Subscription upgrade initialized successfully:', result);

        // Show success message
        toast({
          title: 'Redirecting to payment',
          description: 'You will be redirected to Paystack to complete your payment',
        });

        // Redirect to Paystack checkout
        setTimeout(() => {
          window.location.href = result.authorizationUrl;
        }, 1500);
      } else {
        throw new Error('Failed to initialize subscription upgrade');
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to upgrade subscription. Please try again later.',
        variant: 'destructive'
      });
      setIsUpgrading(null);
    }
  };

  return (
    <div className={compact ? "w-full" : "container mx-auto py-8"}>
      {!compact && (
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4">Choose Your Plan</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Select the plan that best fits your business needs. All plans include GRA-compliant invoicing.
          </p>
        </div>
      )}

      <Tabs defaultValue="monthly" className="w-full">
        <div className="flex justify-center mb-8">
          <TabsList>
            <TabsTrigger
              value="monthly"
              onClick={() => setBillingCycle('monthly')}
            >
              Monthly Billing
            </TabsTrigger>
            <TabsTrigger
              value="yearly"
              onClick={() => setBillingCycle('yearly')}
            >
              Yearly Billing <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Save 20%</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="monthly" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Free Plan */}
            <Card className={`border ${tier === 'free' && showCurrentPlan ? 'border-ghana-green' : 'border-gray-200'} h-full flex flex-col`}>
              {tier === 'free' && showCurrentPlan && (
                <div className="bg-ghana-green text-white text-center py-1 text-sm font-medium">
                  Current Plan
                </div>
              )}
              <CardHeader>
                <CardTitle>Free</CardTitle>
                <CardDescription>For individuals just getting started</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">GHS 0</span>
                  <span className="text-gray-500">/month</span>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  {SUBSCRIPTION_PLANS.FREE.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full bg-gray-900 hover:bg-gray-800"
                  onClick={() => handleSubscriptionUpgrade('free')}
                  disabled={isUpgrading !== null || (isActive && tier === 'free')}
                >
                  {isUpgrading === 'free' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isActive && tier === 'free' ? (
                    'Current Plan'
                  ) : (
                    'Get Started'
                  )}
                </Button>
              </CardFooter>
            </Card>

            {/* Freelancer Plan */}
            <Card className={`border ${tier === 'freelancer' && showCurrentPlan ? 'border-ghana-green' : 'border-gray-200'} h-full flex flex-col`}>
              {tier === 'freelancer' && showCurrentPlan && (
                <div className="bg-ghana-green text-white text-center py-1 text-sm font-medium">
                  Current Plan
                </div>
              )}
              <CardHeader>
                <CardTitle>Freelancer</CardTitle>
                <CardDescription>Perfect for independent professionals</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">
                    {billingCycle === 'monthly'
                      ? `GHS ${SUBSCRIPTION_PLANS.FREELANCER.price}`
                      : `GHS ${SUBSCRIPTION_PLANS.FREELANCER.yearlyPrice}`}
                  </span>
                  <span className="text-gray-500">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>

                  {billingCycle === 'yearly' && (
                    <div className="text-green-600 text-sm mt-1">
                      Save {calculateSavings(SUBSCRIPTION_PLANS.FREELANCER.price, SUBSCRIPTION_PLANS.FREELANCER.yearlyPrice)}%
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  {SUBSCRIPTION_PLANS.FREELANCER.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full bg-ghana-green hover:bg-ghana-green/90"
                  onClick={() => handleSubscriptionUpgrade('freelancer')}
                  disabled={isUpgrading !== null || (isActive && tier === 'freelancer')}
                >
                  {isUpgrading === 'freelancer' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isActive && tier === 'freelancer' ? (
                    'Current Plan'
                  ) : (
                    'Upgrade to Freelancer'
                  )}
                </Button>
              </CardFooter>
            </Card>

            {/* Business Plan */}
            <Card className={`border ${tier === 'business' && showCurrentPlan ? 'border-ghana-green' : 'border-gray-200'} h-full flex flex-col`}>
              {tier === 'business' && showCurrentPlan && (
                <div className="bg-ghana-green text-white text-center py-1 text-sm font-medium">
                  Current Plan
                </div>
              )}
              <CardHeader>
                <CardTitle>Business</CardTitle>
                <CardDescription>For growing businesses with regular invoicing needs</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">
                    {billingCycle === 'monthly'
                      ? `GHS ${SUBSCRIPTION_PLANS.BUSINESS.price}`
                      : `GHS ${SUBSCRIPTION_PLANS.BUSINESS.yearlyPrice}`}
                  </span>
                  <span className="text-gray-500">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>

                  {billingCycle === 'yearly' && (
                    <div className="text-green-600 text-sm mt-1">
                      Save {calculateSavings(SUBSCRIPTION_PLANS.BUSINESS.price, SUBSCRIPTION_PLANS.BUSINESS.yearlyPrice)}%
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  {SUBSCRIPTION_PLANS.BUSINESS.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full bg-ghana-green hover:bg-ghana-green/90"
                  onClick={() => handleSubscriptionUpgrade('business')}
                  disabled={isUpgrading !== null || (isActive && tier === 'business')}
                >
                  {isUpgrading === 'business' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isActive && tier === 'business' ? (
                    'Current Plan'
                  ) : (
                    'Upgrade to Business'
                  )}
                </Button>
              </CardFooter>
            </Card>

            {/* Enterprise Plan */}
            <Card className={`border ${tier === 'enterprise' && showCurrentPlan ? 'border-ghana-green' : 'border-gray-200'} h-full flex flex-col`}>
              {tier === 'enterprise' && showCurrentPlan && (
                <div className="bg-ghana-green text-white text-center py-1 text-sm font-medium">
                  Current Plan
                </div>
              )}
              <CardHeader>
                <CardTitle>Enterprise</CardTitle>
                <CardDescription>For established businesses with multiple team members</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">
                    {billingCycle === 'monthly'
                      ? `GHS ${SUBSCRIPTION_PLANS.ENTERPRISE.price}`
                      : `GHS ${SUBSCRIPTION_PLANS.ENTERPRISE.yearlyPrice}`}
                  </span>
                  <span className="text-gray-500">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>

                  {billingCycle === 'yearly' && (
                    <div className="text-green-600 text-sm mt-1">
                      Save {calculateSavings(SUBSCRIPTION_PLANS.ENTERPRISE.price, SUBSCRIPTION_PLANS.ENTERPRISE.yearlyPrice)}%
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  {SUBSCRIPTION_PLANS.ENTERPRISE.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full bg-ghana-green hover:bg-ghana-green/90"
                  onClick={() => handleSubscriptionUpgrade('enterprise')}
                  disabled={isUpgrading !== null || (isActive && tier === 'enterprise')}
                >
                  {isUpgrading === 'enterprise' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isActive && tier === 'enterprise' ? (
                    'Current Plan'
                  ) : (
                    'Upgrade to Enterprise'
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="yearly" className="mt-0">
          {/* Same content as monthly but with yearly prices */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Free Plan */}
            <Card className={`border ${tier === 'free' && showCurrentPlan ? 'border-ghana-green' : 'border-gray-200'} h-full flex flex-col`}>
              {tier === 'free' && showCurrentPlan && (
                <div className="bg-ghana-green text-white text-center py-1 text-sm font-medium">
                  Current Plan
                </div>
              )}
              <CardHeader>
                <CardTitle>Free</CardTitle>
                <CardDescription>For individuals just getting started</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">GHS 0</span>
                  <span className="text-gray-500">/year</span>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  {SUBSCRIPTION_PLANS.FREE.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full bg-gray-900 hover:bg-gray-800"
                  onClick={() => handleSubscriptionUpgrade('free')}
                  disabled={isUpgrading !== null || (isActive && tier === 'free')}
                >
                  {isUpgrading === 'free' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isActive && tier === 'free' ? (
                    'Current Plan'
                  ) : (
                    'Get Started'
                  )}
                </Button>
              </CardFooter>
            </Card>

            {/* Freelancer Plan - Yearly */}
            <Card className={`border ${tier === 'freelancer' && showCurrentPlan ? 'border-ghana-green' : 'border-gray-200'} h-full flex flex-col`}>
              {tier === 'freelancer' && showCurrentPlan && (
                <div className="bg-ghana-green text-white text-center py-1 text-sm font-medium">
                  Current Plan
                </div>
              )}
              <CardHeader>
                <CardTitle>Freelancer</CardTitle>
                <CardDescription>Perfect for independent professionals</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">
                    {billingCycle === 'monthly'
                      ? `GHS ${SUBSCRIPTION_PLANS.FREELANCER.price}`
                      : `GHS ${SUBSCRIPTION_PLANS.FREELANCER.yearlyPrice}`}
                  </span>
                  <span className="text-gray-500">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>

                  {billingCycle === 'yearly' && (
                    <div className="text-green-600 text-sm mt-1">
                      Save {calculateSavings(SUBSCRIPTION_PLANS.FREELANCER.price, SUBSCRIPTION_PLANS.FREELANCER.yearlyPrice)}%
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  {SUBSCRIPTION_PLANS.FREELANCER.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full bg-ghana-green hover:bg-ghana-green/90"
                  onClick={() => handleSubscriptionUpgrade('freelancer')}
                  disabled={isUpgrading !== null || (isActive && tier === 'freelancer')}
                >
                  {isUpgrading === 'freelancer' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isActive && tier === 'freelancer' ? (
                    'Current Plan'
                  ) : (
                    'Upgrade to Freelancer'
                  )}
                </Button>
              </CardFooter>
            </Card>

            {/* Business Plan - Yearly */}
            <Card className={`border ${tier === 'business' && showCurrentPlan ? 'border-ghana-green' : 'border-gray-200'} h-full flex flex-col`}>
              {tier === 'business' && showCurrentPlan && (
                <div className="bg-ghana-green text-white text-center py-1 text-sm font-medium">
                  Current Plan
                </div>
              )}
              <CardHeader>
                <CardTitle>Business</CardTitle>
                <CardDescription>For growing businesses with regular invoicing needs</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">
                    {billingCycle === 'monthly'
                      ? `GHS ${SUBSCRIPTION_PLANS.BUSINESS.price}`
                      : `GHS ${SUBSCRIPTION_PLANS.BUSINESS.yearlyPrice}`}
                  </span>
                  <span className="text-gray-500">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>

                  {billingCycle === 'yearly' && (
                    <div className="text-green-600 text-sm mt-1">
                      Save {calculateSavings(SUBSCRIPTION_PLANS.BUSINESS.price, SUBSCRIPTION_PLANS.BUSINESS.yearlyPrice)}%
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  {SUBSCRIPTION_PLANS.BUSINESS.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full bg-ghana-green hover:bg-ghana-green/90"
                  onClick={() => handleSubscriptionUpgrade('business')}
                  disabled={isUpgrading !== null || (isActive && tier === 'business')}
                >
                  {isUpgrading === 'business' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isActive && tier === 'business' ? (
                    'Current Plan'
                  ) : (
                    'Upgrade to Business'
                  )}
                </Button>
              </CardFooter>
            </Card>

            {/* Enterprise Plan - Yearly */}
            <Card className={`border ${tier === 'enterprise' && showCurrentPlan ? 'border-ghana-green' : 'border-gray-200'} h-full flex flex-col`}>
              {tier === 'enterprise' && showCurrentPlan && (
                <div className="bg-ghana-green text-white text-center py-1 text-sm font-medium">
                  Current Plan
                </div>
              )}
              <CardHeader>
                <CardTitle>Enterprise</CardTitle>
                <CardDescription>For established businesses with multiple team members</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">
                    {billingCycle === 'monthly'
                      ? `GHS ${SUBSCRIPTION_PLANS.ENTERPRISE.price}`
                      : `GHS ${SUBSCRIPTION_PLANS.ENTERPRISE.yearlyPrice}`}
                  </span>
                  <span className="text-gray-500">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>

                  {billingCycle === 'yearly' && (
                    <div className="text-green-600 text-sm mt-1">
                      Save {calculateSavings(SUBSCRIPTION_PLANS.ENTERPRISE.price, SUBSCRIPTION_PLANS.ENTERPRISE.yearlyPrice)}%
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  {SUBSCRIPTION_PLANS.ENTERPRISE.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full bg-ghana-green hover:bg-ghana-green/90"
                  onClick={() => handleSubscriptionUpgrade('enterprise')}
                  disabled={isUpgrading !== null || (isActive && tier === 'enterprise')}
                >
                  {isUpgrading === 'enterprise' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isActive && tier === 'enterprise' ? (
                    'Current Plan'
                  ) : (
                    'Upgrade to Enterprise'
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SubscriptionPlans;
