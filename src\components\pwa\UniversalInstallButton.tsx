import React, { useState, useEffect } from 'react';
import { Download, X, Share2, MoreVertical, Plus, Monitor, Laptop } from 'lucide-react';

// Add keyframes for pulse animation
const pulseKeyframes = `
@keyframes ios-button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 166, 81, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 166, 81, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 166, 81, 0);
  }
}
`;

// Add style tag to head
if (typeof document !== 'undefined') {
  try {
    // Add iOS Chrome specific CSS fix - more aggressive
    const iosChromeFixCSS = `
      /* General fixed position */
      #universal-install-button {
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        z-index: 999999 !important;
        transform: translateZ(0) !important;
        -webkit-transform: translateZ(0) !important;
      }

      /* iOS specific fixes */
      @supports (-webkit-touch-callout: none) {
        #universal-install-button {
          position: fixed !important;
          bottom: 20px !important;
          right: 20px !important;
          z-index: 999999 !important;
          transform: translateZ(0) !important;
          -webkit-transform: translateZ(0) !important;
          -webkit-backface-visibility: hidden !important;
          backface-visibility: hidden !important;
          -webkit-perspective: 1000px !important;
          perspective: 1000px !important;
        }

        /* Force hardware acceleration */
        body {
          -webkit-overflow-scrolling: touch;
        }
      }
    `;

    const style = document.createElement('style');
    style.textContent = pulseKeyframes + iosChromeFixCSS;
    document.head.appendChild(style);

    // Add additional script to ensure button stays fixed on iOS Chrome
    if (/CriOS/i.test(navigator.userAgent) && /iPad|iPhone|iPod/i.test(navigator.userAgent)) {
      // Initial positioning
      setTimeout(() => {
        const button = document.getElementById('universal-install-button');
        if (button) {
          button.style.position = 'fixed';
          button.style.bottom = '20px';
          button.style.right = '20px';
          button.style.zIndex = '999999';
        }
      }, 100);

      // Reposition on scroll
      window.addEventListener('scroll', function() {
        const button = document.getElementById('universal-install-button');
        if (button) {
          button.style.position = 'fixed';
          button.style.bottom = '20px';
          button.style.right = '20px';
          button.style.zIndex = '999999';
        }
      });

      // Reposition on resize
      window.addEventListener('resize', function() {
        const button = document.getElementById('universal-install-button');
        if (button) {
          button.style.position = 'fixed';
          button.style.bottom = '20px';
          button.style.right = '20px';
          button.style.zIndex = '999999';
        }
      });

      // Reposition on orientation change
      window.addEventListener('orientationchange', function() {
        setTimeout(() => {
          const button = document.getElementById('universal-install-button');
          if (button) {
            button.style.position = 'fixed';
            button.style.bottom = '20px';
            button.style.right = '20px';
            button.style.zIndex = '999999';
          }
        }, 100);
      });
    }
  } catch (error) {
    console.error('Error adding styles:', error);
  }
}

// Simple iOS detection
const isIOS = () => {
  if (typeof window === 'undefined') return false;

  try {
    // Check for iOS devices
    const isIOSDevice =
      /iPad|iPhone|iPod/i.test(navigator.userAgent) ||
      (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) ||
      (navigator.userAgent.includes("Mac") && "ontouchend" in document);

    // Also check for Chrome on iOS (CriOS)
    const isChromeOnIOS = /CriOS/i.test(navigator.userAgent);

    console.log('iOS detection:', { isIOSDevice, isChromeOnIOS, userAgent: navigator.userAgent });

    return isIOSDevice || isChromeOnIOS;
  } catch (error) {
    console.error('Error detecting iOS:', error);
    return false;
  }
};

// Simple Chrome detection
const isChrome = () => {
  if (typeof window === 'undefined') return false;
  try {
    return /CriOS/i.test(navigator.userAgent) || /Chrome/i.test(navigator.userAgent);
  } catch (error) {
    console.error('Error detecting Chrome:', error);
    return false;
  }
};

// Detect if it's an Android device
const isAndroid = () => {
  if (typeof window === 'undefined') return false;
  try {
    return /Android/i.test(navigator.userAgent);
  } catch (error) {
    console.error('Error detecting Android:', error);
    return false;
  }
};

// Detect if it's a desktop browser
const isDesktop = () => {
  if (typeof window === 'undefined') return false;
  try {
    return window.innerWidth > 1024 && !(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
  } catch (error) {
    console.error('Error detecting desktop:', error);
    return false;
  }
};

// Check if the browser supports direct installation
const supportsDirectInstall = () => {
  if (typeof window === 'undefined') return false;
  try {
    // Check for Chrome's install prompt
    if ((window as any).deferredPrompt) {
      return true;
    }

    // Check for browser-specific installation features
    const isChromium = navigator.userAgent.includes('Chrome') || navigator.userAgent.includes('Edg');
    const hasInstallableManifest = document.head.querySelector('link[rel="manifest"]') !== null;

    return isChromium && hasInstallableManifest;
  } catch (error) {
    console.error('Error checking direct install support:', error);
    return false;
  }
};

// Function to try to trigger Chrome's installation UI
const triggerChromeInstallUI = () => {
  try {
    // Create a custom event that might trigger Chrome's install UI
    const installEvent = new CustomEvent('chrome-pwa-install-trigger', {
      bubbles: true,
      composed: true,
      detail: { source: 'install-button' }
    });
    document.dispatchEvent(installEvent);

    // Also try to focus the address bar to draw attention to the install icon
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
    window.focus();

    // Scroll to top to make address bar visible
    window.scrollTo(0, 0);

    return true;
  } catch (error) {
    console.error('Failed to trigger Chrome install UI:', error);
    return false;
  }
};

const UniversalInstallButton: React.FC = () => {
  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    // Show button for all platforms
    try {
      console.log('Device detection:', {
        isIOS: isIOS(),
        isAndroid: isAndroid(),
        isChrome: isChrome(),
        isDesktop: isDesktop(),
        userAgent: navigator.userAgent
      });

      // Don't show if already installed - check multiple ways
      if (
        (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) ||
        (window.navigator && (window.navigator as any).standalone === true) ||
        document.referrer.includes('android-app://')
      ) {
        console.log('App already installed, not showing button');
        return;
      }

      // Special fix for iOS Chrome
      if (/CriOS/i.test(navigator.userAgent) && /iPad|iPhone|iPod/i.test(navigator.userAgent)) {
        console.log('iOS Chrome detected, applying special fixes');

        // Apply fix after a short delay to ensure DOM is ready
        setTimeout(() => {
          const viewportHeight = window.innerHeight;
          document.documentElement.style.setProperty('--vh', `${viewportHeight * 0.01}px`);

          // Force repaint to ensure button is visible
          document.body.style.display = 'none';
          document.body.offsetHeight; // Trigger reflow
          document.body.style.display = '';

          // Create a container div that's always at the bottom of the viewport
          const container = document.createElement('div');
          container.id = 'ios-chrome-install-container';
          container.style.position = 'fixed';
          container.style.bottom = '0';
          container.style.right = '0';
          container.style.width = '80px';
          container.style.height = '80px';
          container.style.zIndex = '999998'; // Just below the button
          container.style.pointerEvents = 'none'; // Don't block interactions
          document.body.appendChild(container);

          // Add a periodic check to ensure the button stays visible
          setInterval(() => {
            const button = document.getElementById('universal-install-button');
            if (button) {
              button.style.position = 'fixed';
              button.style.bottom = '20px';
              button.style.right = '20px';
              button.style.zIndex = '999999';
            }
          }, 1000);
        }, 100);
      }

      // Listen for beforeinstallprompt event on all platforms that support it
      console.log('Setting up beforeinstallprompt listener for all platforms');

      window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent the mini-infobar from appearing on mobile
        e.preventDefault();
        console.log('beforeinstallprompt event fired');

        // Store the event for later use
        (window as any).deferredPrompt = e;
      });

      setShowButton(true);
    } catch (error) {
      console.error('Error in useEffect:', error);
    }
  }, []);

  const showInstructions = async () => {
    try {
      // Check for Android or desktop with native install prompt
      if ((window as any).deferredPrompt) {
        console.log('Using native install prompt');

        // Show the install prompt
        const promptEvent = (window as any).deferredPrompt;
        promptEvent.prompt();

        // Wait for the user to respond to the prompt
        const { outcome } = await promptEvent.userChoice;
        console.log(`User response to the install prompt: ${outcome}`);

        // Clear the saved prompt
        (window as any).deferredPrompt = null;

        // If the user accepted, hide the button
        if (outcome === 'accepted') {
          setShowButton(false);
        }

        return;
      }

      // Only iOS needs special instructions
      const iOS = isIOS();

      // For desktop, try to trigger direct installation
      if (isDesktop() && !iOS) {
        console.log('Desktop detected, attempting direct installation');

        // Check if we can trigger direct installation
        if (supportsDirectInstall()) {
          // For Chrome, Edge, and other Chromium browsers
          try {
            // Try to trigger the browser's built-in install UI
            // First, check if we have a deferred prompt
            if ((window as any).deferredPrompt) {
              // We already handled this case above, but just in case
              console.log('Using deferred prompt for installation');
              return;
            }

            // Try to trigger Chrome's install UI directly
            if (triggerChromeInstallUI()) {
              console.log('Successfully triggered Chrome install UI');

              // Show a message pointing to the address bar after a short delay
              setTimeout(() => {
                // Only show the message if the app hasn't been installed yet
                if (!window.matchMedia('(display-mode: standalone)').matches) {
                  alert('Click the install icon in your browser\'s address bar to install this app.');
                }
              }, 1000);
            }
            return;
          } catch (error) {
            console.error('Failed to trigger direct installation:', error);
          }
        }

        // If we couldn't trigger a direct install, show a simple message
        alert('To install this app, click the install icon in your browser\'s address bar or menu.');
        return;
      }

      // For Android without native prompt, show a simple message
      if (!iOS && !isDesktop()) {
        alert('To install this app, tap the menu icon in your browser and select "Add to Home screen" or "Install app".');
        return;
      }

      // For iOS, show the detailed modal
      const desktop = isDesktop();
      const chrome = isChrome();

      // Check if dark mode is enabled
      const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

      // Create modal container
      const modal = document.createElement('div');
      modal.style.position = 'fixed';
      modal.style.top = '0';
      modal.style.left = '0';
      modal.style.width = '100%';
      modal.style.height = '100%';
      modal.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      modal.style.zIndex = '9999999';
      modal.style.display = 'flex';
      modal.style.alignItems = 'center';
      modal.style.justifyContent = 'center';
      modal.style.backdropFilter = 'blur(4px)';
      modal.style.WebkitBackdropFilter = 'blur(4px)';

      // Create content container
      const content = document.createElement('div');
      content.style.backgroundColor = isDarkMode ? '#1f2937' : 'white';
      content.style.color = isDarkMode ? '#e5e7eb' : '#374151';
      content.style.borderRadius = '12px';
      content.style.padding = '24px';
      content.style.maxWidth = '90%';
      content.style.width = '400px';
      content.style.boxShadow = isDarkMode ?
        '0 10px 25px rgba(0, 0, 0, 0.5)' :
        '0 10px 25px rgba(0, 0, 0, 0.2)';
      content.style.overflowY = 'auto';
      content.style.maxHeight = '90vh';

      // Create header
      const header = document.createElement('div');
      header.style.display = 'flex';
      header.style.alignItems = 'center';
      header.style.justifyContent = 'space-between';
      header.style.marginBottom = '20px';

      const title = document.createElement('h3');
      title.textContent = 'Install Payvoicer';
      title.style.fontSize = '20px';
      title.style.fontWeight = 'bold';
      title.style.margin = '0';
      title.style.color = isDarkMode ? '#FFD700' : '#00A651';
      title.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';

      const closeButton = document.createElement('button');
      closeButton.innerHTML = '×';
      closeButton.style.background = 'transparent';
      closeButton.style.border = 'none';
      closeButton.style.fontSize = '24px';
      closeButton.style.cursor = 'pointer';
      closeButton.style.color = isDarkMode ? '#e5e7eb' : '#6b7280';
      closeButton.style.padding = '0';
      closeButton.style.lineHeight = '1';

      closeButton.onclick = function() {
        document.body.removeChild(modal);
      };

      header.appendChild(title);
      header.appendChild(closeButton);

      // Create instructions
      const instructions = document.createElement('div');

      if (desktop) {
        // Desktop instructions
        const browserName = chrome ? 'Chrome' : 'your browser';

        const step1 = document.createElement('div');
        step1.style.marginBottom = '16px';

        const step1Title = document.createElement('p');
        step1Title.style.fontWeight = '500';
        step1Title.style.margin = '0 0 5px 0';
        step1Title.textContent = `1. Click on the menu icon in ${browserName}`;

        const step1Desc = document.createElement('p');
        step1Desc.style.margin = '0';
        step1Desc.style.fontSize = '14px';
        step1Desc.style.color = '#6b7280';
        step1Desc.textContent = chrome ? 'Look for the three dots (⋮) in the top-right corner' : 'Look for the menu in the top-right corner';

        step1.appendChild(step1Title);
        step1.appendChild(step1Desc);

        const step2 = document.createElement('div');
        step2.style.marginBottom = '16px';

        const step2Title = document.createElement('p');
        step2Title.style.fontWeight = '500';
        step2Title.style.margin = '0 0 5px 0';
        step2Title.textContent = chrome ? '2. Select "More tools" > "Create shortcut..."' : '2. Look for "Install" or "Add to desktop" option';

        const step2Desc = document.createElement('p');
        step2Desc.style.margin = '0';
        step2Desc.style.fontSize = '14px';
        step2Desc.style.color = '#6b7280';
        step2Desc.textContent = 'This will add the app to your desktop';

        step2.appendChild(step2Title);
        step2.appendChild(step2Desc);

        const step3 = document.createElement('div');

        const step3Title = document.createElement('p');
        step3Title.style.fontWeight = '500';
        step3Title.style.margin = '0 0 5px 0';
        step3Title.textContent = chrome ? '3. Check "Open as window" and click "Create"' : '3. Follow the prompts to complete installation';

        const step3Desc = document.createElement('p');
        step3Desc.style.margin = '0';
        step3Desc.style.fontSize = '14px';
        step3Desc.style.color = '#6b7280';
        step3Desc.textContent = 'You can now access the app from your desktop';

        step3.appendChild(step3Title);
        step3.appendChild(step3Desc);

        instructions.appendChild(step1);
        instructions.appendChild(step2);
        instructions.appendChild(step3);
      } else if (iOS) {
        // iOS instructions
        const step1 = document.createElement('div');
        step1.style.marginBottom = '16px';

        const step1Title = document.createElement('p');
        step1Title.style.fontWeight = '500';
        step1Title.style.margin = '0 0 5px 0';
        step1Title.textContent = chrome ? 'Tap the Menu (⋮)' : 'Tap the Share (↑) icon';

        const step1Desc = document.createElement('p');
        step1Desc.style.margin = '0';
        step1Desc.style.fontSize = '14px';
        step1Desc.style.color = '#6b7280';
        step1Desc.textContent = chrome ? 'Located at the top-right of your browser' : 'Located at the bottom of your browser';

        step1.appendChild(step1Title);
        step1.appendChild(step1Desc);

        const step2 = document.createElement('div');
        step2.style.marginBottom = '16px';

        const step2Title = document.createElement('p');
        step2Title.style.fontWeight = '500';
        step2Title.style.margin = '0 0 5px 0';
        step2Title.textContent = chrome ? 'Tap "Share..." then "Add to Home Screen"' : 'Scroll down and tap "Add to Home Screen"';

        const step2Desc = document.createElement('p');
        step2Desc.style.margin = '0';
        step2Desc.style.fontSize = '14px';
        step2Desc.style.color = '#6b7280';
        step2Desc.textContent = 'You may need to scroll down to find this option';

        step2.appendChild(step2Title);
        step2.appendChild(step2Desc);

        const step3 = document.createElement('div');

        const step3Title = document.createElement('p');
        step3Title.style.fontWeight = '500';
        step3Title.style.margin = '0 0 5px 0';
        step3Title.textContent = 'Tap "Add" in the top right corner';

        const step3Desc = document.createElement('p');
        step3Desc.style.margin = '0';
        step3Desc.style.fontSize = '14px';
        step3Desc.style.color = '#6b7280';
        step3Desc.textContent = 'The app will be added to your home screen';

        step3.appendChild(step3Title);
        step3.appendChild(step3Desc);

        instructions.appendChild(step1);
        instructions.appendChild(step2);
        instructions.appendChild(step3);
      } else {
        // Check if it's Android
        const isAndroid = /Android/i.test(navigator.userAgent);

        if (isAndroid) {
          // Android-specific instructions
          const step1 = document.createElement('div');
          step1.style.marginBottom = '16px';

          const step1Title = document.createElement('p');
          step1Title.style.fontWeight = '500';
          step1Title.style.margin = '0 0 5px 0';
          step1Title.textContent = 'Tap the menu icon (⋮) in Chrome';

          const step1Desc = document.createElement('p');
          step1Desc.style.margin = '0';
          step1Desc.style.fontSize = '14px';
          step1Desc.style.color = '#6b7280';
          step1Desc.textContent = 'Located in the top-right corner of your browser';

          step1.appendChild(step1Title);
          step1.appendChild(step1Desc);

          const step2 = document.createElement('div');
          step2.style.marginBottom = '16px';

          const step2Title = document.createElement('p');
          step2Title.style.fontWeight = '500';
          step2Title.style.margin = '0 0 5px 0';
          step2Title.textContent = 'Tap "Install app" or "Add to Home screen"';

          const step2Desc = document.createElement('p');
          step2Desc.style.margin = '0';
          step2Desc.style.fontSize = '14px';
          step2Desc.style.color = '#6b7280';
          step2Desc.textContent = 'You\'ll see this option in the menu';

          step2.appendChild(step2Title);
          step2.appendChild(step2Desc);

          const step3 = document.createElement('div');

          const step3Title = document.createElement('p');
          step3Title.style.fontWeight = '500';
          step3Title.style.margin = '0 0 5px 0';
          step3Title.textContent = 'Tap "Install" in the prompt';

          const step3Desc = document.createElement('p');
          step3Desc.style.margin = '0';
          step3Desc.style.fontSize = '14px';
          step3Desc.style.color = '#6b7280';
          step3Desc.textContent = 'The app will be installed and added to your home screen';

          step3.appendChild(step3Title);
          step3.appendChild(step3Desc);

          instructions.appendChild(step1);
          instructions.appendChild(step2);
          instructions.appendChild(step3);
        } else {
          // Other mobile devices
          const step1 = document.createElement('div');
          step1.style.marginBottom = '16px';

          const step1Title = document.createElement('p');
          step1Title.style.fontWeight = '500';
          step1Title.style.margin = '0 0 5px 0';
          step1Title.textContent = 'Tap the menu icon in your browser';

          const step1Desc = document.createElement('p');
          step1Desc.style.margin = '0';
          step1Desc.style.fontSize = '14px';
          step1Desc.style.color = '#6b7280';
          step1Desc.textContent = 'Usually three dots (⋮) or similar in the corner';

          step1.appendChild(step1Title);
          step1.appendChild(step1Desc);

          const step2 = document.createElement('div');
          step2.style.marginBottom = '16px';

          const step2Title = document.createElement('p');
          step2Title.style.fontWeight = '500';
          step2Title.style.margin = '0 0 5px 0';
          step2Title.textContent = 'Look for "Add to Home screen" or similar';

          const step2Desc = document.createElement('p');
          step2Desc.style.margin = '0';
          step2Desc.style.fontSize = '14px';
          step2Desc.style.color = '#6b7280';
          step2Desc.textContent = 'The wording may vary depending on your browser';

          step2.appendChild(step2Title);
          step2.appendChild(step2Desc);

          const step3 = document.createElement('div');

          const step3Title = document.createElement('p');
          step3Title.style.fontWeight = '500';
          step3Title.style.margin = '0 0 5px 0';
          step3Title.textContent = 'Follow the prompts to complete installation';

          const step3Desc = document.createElement('p');
          step3Desc.style.margin = '0';
          step3Desc.style.fontSize = '14px';
          step3Desc.style.color = '#6b7280';
          step3Desc.textContent = 'The app will be added to your home screen';

          step3.appendChild(step3Title);
          step3.appendChild(step3Desc);

          instructions.appendChild(step1);
          instructions.appendChild(step2);
          instructions.appendChild(step3);
        }
      }

      // Create button
      const button = document.createElement('button');
      button.textContent = 'Got it';
      button.style.backgroundColor = isDarkMode ? '#FFD700' : '#00A651';
      button.style.color = isDarkMode ? '#1f2937' : 'white';
      button.style.border = 'none';
      button.style.padding = '10px 0';
      button.style.borderRadius = '6px';
      button.style.fontWeight = 'bold';
      button.style.cursor = 'pointer';
      button.style.width = '100%';
      button.style.marginTop = '20px';

      button.onclick = function() {
        document.body.removeChild(modal);
      };

      // Assemble modal
      content.appendChild(header);
      content.appendChild(instructions);
      content.appendChild(button);
      modal.appendChild(content);

      // Add to body
      document.body.appendChild(modal);
    } catch (error) {
      console.error('Error showing instructions:', error);
      // Fallback to alert if the modal fails
      alert('To install this app: look for the install option in your browser menu or add to home screen option.');
    }
  };

  if (!showButton) return null;

  try {
    // Use an install icon to make it self-explanatory
    const getIcon = () => {
      if (isDesktop()) {
        // Desktop install icon
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
            <line x1="8" y1="21" x2="16" y2="21"></line>
            <line x1="12" y1="17" x2="12" y2="21"></line>
          </svg>
        );
      } else {
        // Mobile download icon
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="26"
            height="26"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
        );
      }
    };

    return (
      <div
        id="universal-install-button"
        title="Install App"
        aria-label="Install App"
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 999999,
          backgroundColor: '#00A651',
          color: 'white',
          width: isDesktop() ? '48px' : '56px',
          height: isDesktop() ? '48px' : '56px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15), 0 8px 20px rgba(0, 166, 81, 0.25)',
          cursor: 'pointer',
          // iOS-specific fixes to ensure it's always visible
          WebkitTransform: 'translateZ(0)',
          transform: 'translateZ(0)',
          WebkitBackfaceVisibility: 'hidden',
          backfaceVisibility: 'hidden',
          WebkitPerspective: '1000px',
          perspective: '1000px',
          margin: 0,
          padding: 0,
          // Add pulsing animation
          animation: 'ios-button-pulse 2s infinite',
          WebkitAnimation: 'ios-button-pulse 2s infinite',
          // Additional iOS Chrome fixes
          WebkitPositionSticky: 'fixed',
          WebkitPosition: 'fixed',
          // Make sure it's always visible
          opacity: 1,
          visibility: 'visible',
          // Ensure it's above all other elements
          isolation: 'isolate',
          // Add a border to make it more visible
          border: '2px solid rgba(255, 255, 255, 0.8)'
        }}
        onMouseEnter={(e) => {
          // Add tooltip
          const tooltip = document.createElement('div');
          tooltip.id = 'install-tooltip';
          tooltip.textContent = 'Install App';
          tooltip.style.position = 'fixed';
          tooltip.style.bottom = isDesktop() ? '75px' : '85px';
          tooltip.style.right = '20px';
          tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
          tooltip.style.color = 'white';
          tooltip.style.padding = '8px 12px';
          tooltip.style.borderRadius = '4px';
          tooltip.style.fontSize = '14px';
          tooltip.style.zIndex = '999998';
          tooltip.style.pointerEvents = 'none';
          document.body.appendChild(tooltip);
        }}
        onMouseLeave={() => {
          // Remove tooltip
          const tooltip = document.getElementById('install-tooltip');
          if (tooltip) {
            document.body.removeChild(tooltip);
          }
        }}
        onClick={showInstructions}
        onMouseOver={(e) => {
          const target = e.currentTarget;
          target.style.transform = 'translateY(-3px) scale(1.05)';
          target.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.25), 0 15px 30px rgba(0, 166, 81, 0.3)';
          target.style.animation = 'none';
        }}
        onMouseOut={(e) => {
          const target = e.currentTarget;
          target.style.transform = 'translateZ(0)';
          target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
          target.style.animation = 'ios-button-pulse 2s infinite';
        }}
        onMouseDown={(e) => {
          const target = e.currentTarget;
          target.style.transform = 'translateY(0) scale(0.95)';
          target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 166, 81, 0.2)';
        }}
        onMouseUp={(e) => {
          const target = e.currentTarget;
          target.style.transform = 'translateY(-3px) scale(1.05)';
          target.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.25), 0 15px 30px rgba(0, 166, 81, 0.3)';
        }}
      >
        {getIcon()}
      </div>
    );
  } catch (error) {
    console.error('Error rendering UniversalInstallButton:', error);
    return null;
  }
};

export default UniversalInstallButton;
