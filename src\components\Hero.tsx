
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { ArrowRight, CheckCircle, Star, Shield, Clock, ChevronDown } from 'lucide-react';
import { motion } from 'framer-motion';
import AnimatedSection from './AnimatedSection';
import { AnimatedButton } from './ui/animated-button';

const Hero: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Trigger animations after component mounts
    setIsVisible(true);
  }, []);

  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-white via-gray-50 to-white dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 py-20 md:py-28">
      {/* Animated background elements */}
      <div className="absolute inset-0 ghana-pattern opacity-10 z-0"></div>
      <div className="absolute top-20 left-10 w-64 h-64 bg-ghana-gold/20 dark:bg-ghana-gold/30 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-72 h-72 bg-ghana-green/20 dark:bg-ghana-green/30 rounded-full blur-3xl animate-pulse"></div>

      {/* Small decorative elements */}
      <div className="absolute top-40 left-[15%] w-6 h-6 bg-ghana-gold/40 dark:bg-ghana-gold/50 rounded-full animate-float"></div>
      <div className="absolute top-60 right-[20%] w-4 h-4 bg-ghana-green/40 dark:bg-ghana-green/50 rounded-full animate-bounce"></div>
      <div className="absolute bottom-40 left-[30%] w-5 h-5 bg-ghana-red/30 dark:bg-ghana-red/40 rounded-full animate-bounce"></div>

      <div className="container relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div className={`flex flex-col gap-6 transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
            <div className="inline-block bg-ghana-gold/20 dark:bg-ghana-gold/30 px-4 py-1.5 rounded-full animate-pulse">
              <span className="text-sm font-semibold text-ghana-black dark:text-ghana-gold flex items-center">
                <Star className="h-4 w-4 mr-1.5 text-ghana-gold" />
                GRA VAT-Compliant Invoicing
              </span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-display leading-tight dark:text-foreground">
              <span className="text-ghana-green dark:text-ghana-gold relative">
                Simplified
                <span className="absolute -bottom-2 left-0 w-full h-1 bg-ghana-gold/60 rounded-full"></span>
              </span>{" "}
              Invoicing for Ghanaian Businesses
            </h1>

            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-lg">
              Easily create, manage, and track GRA VAT-compliant invoices. Perfect for freelancers and businesses across Ghana.
            </p>

            <div className="flex flex-col sm:flex-row flex-wrap gap-4 mt-4">
              <Link to="/auth" className="group">
                <Button className="bg-ghana-green hover:bg-ghana-green/90 text-white px-8 py-6 text-lg w-full sm:w-auto transition-all duration-300 transform group-hover:scale-105 shadow-lg hover:shadow-xl dark:bg-ghana-green/90 dark:hover:bg-ghana-green">
                  Get Started Free
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Link to="/demo">
                <Button variant="outline" className="px-8 py-6 text-lg border-2 border-ghana-green text-ghana-green hover:bg-ghana-green hover:text-white transition-all duration-300 dark:border-ghana-gold dark:text-ghana-gold dark:hover:bg-ghana-gold/80 dark:hover:text-black">
                  See Demo
                </Button>
              </Link>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4 p-4 bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm">
              <div className="flex -space-x-3">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className={`w-10 h-10 rounded-full border-2 border-white dark:border-gray-700 bg-ghana-gold-light dark:bg-ghana-gold flex items-center justify-center text-xs font-semibold shadow-md transition-transform hover:scale-110 hover:z-10`}>
                    {i}
                  </div>
                ))}
              </div>
              <div>
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">Trusted by 2,000+ businesses in Ghana</span>
                  <div className="flex ml-2">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <Star key={i} className="h-3.5 w-3.5 text-ghana-gold fill-ghana-gold" />
                    ))}
                  </div>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Join the growing community of satisfied users</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 mt-2">
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <CheckCircle className="h-4 w-4 mr-1.5 text-ghana-green dark:text-ghana-gold" />
                <span>GRA Compliant</span>
              </div>
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <Shield className="h-4 w-4 mr-1.5 text-ghana-green dark:text-ghana-gold" />
                <span>Secure Platform</span>
              </div>
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <Clock className="h-4 w-4 mr-1.5 text-ghana-green dark:text-ghana-gold" />
                <span>Quick Setup</span>
              </div>
            </div>
          </div>

          <div className={`relative transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
            {/* Decorative elements */}
            <div className="absolute -top-12 -left-12 w-40 h-40 bg-ghana-gold rounded-full opacity-20 dark:opacity-30 blur-3xl"></div>
            <div className="absolute -bottom-12 -right-12 w-40 h-40 bg-ghana-green rounded-full opacity-20 dark:opacity-30 blur-3xl"></div>

            {/* Invoice preview with animation */}
            <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden border border-gray-100 dark:border-gray-700 transition-all duration-500 hover:shadow-ghana-green/20 dark:hover:shadow-ghana-gold/20 hover:-translate-y-1">
              {/* GRA Compliance Badge */}
              <div className="absolute -right-12 -top-12 w-24 h-24 bg-ghana-green/90 dark:bg-ghana-green rotate-45 flex items-end justify-center pb-1 shadow-lg">
                <span className="text-white text-xs font-bold rotate-45">GRA Approved</span>
              </div>

              <div className="bg-gradient-to-r from-ghana-green to-ghana-green-light dark:from-ghana-green/90 dark:to-ghana-green py-3 px-4 text-white font-semibold text-sm flex justify-between items-center">
                <span>Invoice Preview</span>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 rounded-full bg-white/30"></div>
                  <div className="w-2 h-2 rounded-full bg-white/60"></div>
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                </div>
              </div>

              <div className="p-6">
                <div className="flex justify-between items-start mb-8">
                  <div>
                    <h3 className="font-bold text-xl dark:text-white">Your Company</h3>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">Accra, Ghana</p>
                  </div>
                  <div className="bg-ghana-gold dark:bg-ghana-gold/90 px-4 py-2 rounded-md shadow-md transform rotate-3 hover:rotate-0 transition-transform duration-300">
                    <span className="font-bold text-black">INVOICE</span>
                  </div>
                </div>

                <div className="space-y-3 mb-8">
                  <div className="flex justify-between border-b border-dashed border-gray-200 dark:border-gray-700 pb-2 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors px-2">
                    <span className="text-gray-500 dark:text-gray-400">Web Development</span>
                    <span className="font-medium dark:text-white">2,500 GHS</span>
                  </div>
                  <div className="flex justify-between border-b border-dashed border-gray-200 dark:border-gray-700 pb-2 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors px-2">
                    <span className="text-gray-500 dark:text-gray-400">UI/UX Design</span>
                    <span className="font-medium dark:text-white">1,800 GHS</span>
                  </div>
                  <div className="flex justify-between border-b border-dashed border-gray-200 dark:border-gray-700 pb-2 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors px-2">
                    <span className="text-gray-500 dark:text-gray-400">Consultation</span>
                    <span className="font-medium dark:text-white">900 GHS</span>
                  </div>
                </div>

                <div className="flex justify-between font-bold text-lg mb-2 bg-ghana-green/5 dark:bg-ghana-green/10 p-2 rounded-md">
                  <span className="dark:text-white">Total</span>
                  <span className="dark:text-white">5,200 GHS</span>
                </div>

                <div className="flex justify-between text-sm text-ghana-green dark:text-ghana-gold mb-6">
                  <span>VAT (12.5%)</span>
                  <span>650 GHS</span>
                </div>

                <div className="flex justify-center">
                  <div className="py-2 px-4 bg-gray-100 dark:bg-gray-700 rounded-md text-center hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">GRA Verified</div>
                    <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 mx-auto mb-2 relative overflow-hidden">
                      {/* Animated QR code effect */}
                      <div className="absolute inset-0 grid grid-cols-4 grid-rows-4 gap-0.5">
                        {Array(16).fill(0).map((_, i) => (
                          <div key={i} className={`bg-gray-800 dark:bg-gray-900 ${Math.random() > 0.5 ? 'opacity-100' : 'opacity-30'}`}></div>
                        ))}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Scan to verify</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Scroll indicator */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-12 animate-bounce hidden md:block">
              <ChevronDown className="h-6 w-6 text-ghana-green dark:text-ghana-gold" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
