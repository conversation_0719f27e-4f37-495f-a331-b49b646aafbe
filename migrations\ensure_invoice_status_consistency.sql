-- Ensure invoice status consistency across the application
-- This script creates triggers and functions to maintain invoice status consistency

-- Create a function to check if an invoice has payments and update its status
CREATE OR REPLACE FUNCTION public.sync_invoice_payment_status()
RETURNS TRIGGER AS $$
BEGIN
  -- If a new payment is added with 'successful' status, update the invoice status to 'paid'
  IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') AND NEW.status = 'successful' THEN
    UPDATE invoices
    SET 
      status = 'paid',
      updated_at = NOW(),
      paid_at = COALESCE(paid_at, NOW())
    WHERE id = NEW.invoice_id AND status != 'paid';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to automatically update invoice status when a payment is added or updated
DROP TRIGGER IF EXISTS trigger_sync_invoice_payment_status ON invoice_payments;
CREATE TRIGGER trigger_sync_invoice_payment_status
AFTER INSERT OR UPDATE ON invoice_payments
FOR EACH ROW
EXECUTE FUNCTION sync_invoice_payment_status();

-- Create a function to check all invoices for payment status consistency
CREATE OR REPLACE FUNCTION public.check_all_invoices_payment_status()
RETURNS VOID AS $$
DECLARE
  invoice_record RECORD;
BEGIN
  -- Loop through all invoices that are not marked as paid
  FOR invoice_record IN 
    SELECT i.id 
    FROM invoices i
    WHERE i.status != 'paid'
  LOOP
    -- Check if the invoice has any successful payments
    IF EXISTS (
      SELECT 1 FROM invoice_payments 
      WHERE invoice_id = invoice_record.id AND status = 'successful'
    ) THEN
      -- Update the invoice to paid status
      UPDATE invoices
      SET 
        status = 'paid',
        updated_at = NOW(),
        paid_at = COALESCE(paid_at, NOW())
      WHERE id = invoice_record.id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check a specific invoice's payment status
CREATE OR REPLACE FUNCTION public.check_invoice_payment_status(p_invoice_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  has_successful_payment BOOLEAN;
  invoice_updated BOOLEAN := FALSE;
BEGIN
  -- Check if the invoice has any successful payments
  SELECT EXISTS (
    SELECT 1 FROM invoice_payments 
    WHERE invoice_id = p_invoice_id AND status = 'successful'
  ) INTO has_successful_payment;
  
  -- If it has successful payments but is not marked as paid, update it
  IF has_successful_payment THEN
    UPDATE invoices
    SET 
      status = 'paid',
      updated_at = NOW(),
      paid_at = COALESCE(paid_at, NOW())
    WHERE id = p_invoice_id AND status != 'paid'
    RETURNING TRUE INTO invoice_updated;
  END IF;
  
  -- Return whether the invoice has successful payments
  RETURN has_successful_payment;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.sync_invoice_payment_status() TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_all_invoices_payment_status() TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_invoice_payment_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_invoice_payment_status(UUID) TO anon;
