import { useState, useEffect } from 'react';
import { 
  submitInvoiceToGra, 
  checkGraApiHealth, 
  GraInvoiceRequest, 
  GraInvoiceResponse 
} from '@/services/graApiService.v8.1';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, CheckCircle, AlertCircle, Info } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface GraInvoiceSubmissionProps {
  invoice: any; // Your invoice data structure
  onSuccess?: (response: GraInvoiceResponse) => void;
  onError?: (error: Error) => void;
}

export function GraInvoiceSubmission({ invoice, onSuccess, onError }: GraInvoiceSubmissionProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isHealthChecking, setIsHealthChecking] = useState(false);
  const [apiStatus, setApiStatus] = useState<'unknown' | 'up' | 'down'>('unknown');
  const [response, setResponse] = useState<GraInvoiceResponse | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // Check API health on component mount
  useEffect(() => {
    checkApiHealth();
  }, []);

  const checkApiHealth = async () => {
    setIsHealthChecking(true);
    try {
      const healthResponse = await checkGraApiHealth();
      setApiStatus(healthResponse.status === 'UP' ? 'up' : 'down');
    } catch (error) {
      console.error('API health check failed:', error);
      setApiStatus('down');
    } finally {
      setIsHealthChecking(false);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Convert your invoice data to GRA format
      const graInvoiceRequest = convertToGraFormat(invoice);
      
      // Submit to GRA
      const response = await submitInvoiceToGra(graInvoiceRequest);
      
      // Handle success
      setResponse(response);
      if (onSuccess) onSuccess(response);
    } catch (err) {
      // Handle error
      const error = err instanceof Error ? err : new Error('Unknown error occurred');
      setError(error);
      if (onError) onError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Convert your invoice data to GRA API format
  const convertToGraFormat = (invoice: any): GraInvoiceRequest => {
    // Calculate totals
    const totalAmount = invoice.items.reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0);
    const totalVat = invoice.items.reduce((sum: number, item: any) => {
      const taxRate = item.taxRate || 15; // Default to 15% if not specified
      return sum + ((item.quantity * item.unitPrice) * (taxRate / 100));
    }, 0);
    
    // Calculate levies
    const totalLevy = invoice.items.reduce((sum: number, item: any) => {
      // NHIL 2.5% + GETFL 2.5% + COVID 1% + CST 5% + TOURISM 1%
      return sum + (
        item.levyAmountA || 0) + 
        (item.levyAmountB || 0) + 
        (item.levyAmountC || 0) + 
        (item.levyAmountD || 0) + 
        (item.levyAmountE || 0
      );
    }, 0);
    
    return {
      currency: invoice.currency || 'GHS',
      exchangeRate: invoice.exchangeRate || 1.0,
      invoiceNumber: invoice.invoiceNumber,
      totalLevy,
      userName: invoice.userName || 'System User',
      flag: 'INVOICE',
      calculationType: 'INCLUSIVE',
      totalVat,
      transactionDate: invoice.date || new Date().toISOString(),
      totalAmount,
      businessPartnerName: invoice.customerName,
      businessPartnerTin: invoice.customerTin || 'C0000000000',
      saleType: 'NORMAL',
      discountType: 'GENERAL',
      discountAmount: invoice.discountAmount || 0,
      items: invoice.items.map((item: any) => ({
        itemCode: item.code,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        levyAmountA: item.levyAmountA || 0, // NHIL 2.5%
        levyAmountB: item.levyAmountB || 0, // GETFL 2.5%
        levyAmountC: item.levyAmountC || 0, // COVID 1%
        levyAmountD: item.levyAmountD || 0, // CST 5%
        levyAmountE: item.levyAmountE || 0, // TOURISM 1%
        discountAmount: item.discountAmount || 0,
        taxCode: item.taxCode || 'B', // B for taxable
        taxRate: item.taxRate || 15 // 15% VAT
      }))
    };
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          GRA E-VAT Integration
          <Badge variant={apiStatus === 'up' ? 'success' : apiStatus === 'down' ? 'destructive' : 'outline'}>
            {apiStatus === 'up' ? 'API Online' : apiStatus === 'down' ? 'API Offline' : 'API Status Unknown'}
          </Badge>
        </CardTitle>
        <CardDescription>
          Submit this invoice to the Ghana Revenue Authority for VAT compliance
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error.message}</AlertDescription>
          </Alert>
        )}
        
        {response && (
          <Alert className="mb-4">
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Invoice Submitted Successfully</AlertTitle>
            <AlertDescription>
              <div className="mt-2">
                <p><strong>Invoice Number:</strong> {response.response.mesaage.num}</p>
                <p><strong>Signature:</strong> {response.response.mesaage.ysdcregsig}</p>
                {response.response.qr_code && (
                  <div className="mt-2">
                    <p><strong>QR Code:</strong></p>
                    <img 
                      src={response.response.qr_code} 
                      alt="GRA QR Code" 
                      className="mt-2 max-w-[200px] border border-gray-200 rounded"
                    />
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}
        
        {!response && !error && (
          <div className="py-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>VAT Compliance</AlertTitle>
              <AlertDescription>
                This invoice will be submitted to the GRA E-VAT system for tax compliance.
              </AlertDescription>
            </Alert>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={checkApiHealth} 
          disabled={isHealthChecking}
        >
          {isHealthChecking && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Check API Status
        </Button>
        
        <Button 
          onClick={handleSubmit} 
          disabled={isSubmitting || apiStatus === 'down'}
        >
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Submit to GRA
        </Button>
      </CardFooter>
    </Card>
  );
}
