/**
 * Environment Configuration
 *
 * This file centralizes all environment variable access to ensure consistent
 * handling and error reporting across the application.
 */

// Supabase Configuration
export const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || '';
export const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
export const SUPABASE_SERVICE_ROLE_KEY = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || '';

// Paystack Configuration
export const PAYSTACK_PUBLIC_KEY = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || '';
export const PAYSTACK_SECRET_KEY = import.meta.env.VITE_PAYSTACK_SECRET_KEY || '';
export const PAYSTACK_WEBHOOK_SECRET = import.meta.env.VITE_PAYSTACK_WEBHOOK_SECRET || '';

// Paystack Subscription Plans
export const PAYSTACK_PLANS = {
  freelancer: {
    monthly: import.meta.env.VITE_PAYSTACK_FREELANCER_MONTHLY_PLAN || 'PLN_freelancer_monthly',
    yearly: import.meta.env.VITE_PAYSTACK_FREELANCER_YEARLY_PLAN || 'PLN_freelancer_yearly',
  },
  business: {
    monthly: import.meta.env.VITE_PAYSTACK_BUSINESS_MONTHLY_PLAN || 'PLN_business_monthly',
    yearly: import.meta.env.VITE_PAYSTACK_BUSINESS_YEARLY_PLAN || 'PLN_business_yearly',
  },
  enterprise: {
    monthly: import.meta.env.VITE_PAYSTACK_ENTERPRISE_MONTHLY_PLAN || 'PLN_enterprise_monthly',
    yearly: import.meta.env.VITE_PAYSTACK_ENTERPRISE_YEARLY_PLAN || 'PLN_enterprise_yearly',
  }
};

// GRA API Configuration
// Note: Company TIN, name, and security key are stored in the database
// and provided by users in the application, not in environment variables
export const GRA_CONFIG = {
  isProduction: import.meta.env.VITE_GRA_PRODUCTION === 'true',
  mockMode: import.meta.env.VITE_GRA_MOCK_MODE !== 'false', // Default to true unless explicitly set to "false"
};

// API Proxy Configuration
export const API_PROXY_URL = import.meta.env.VITE_API_PROXY_URL || 'http://localhost:3000';

// Application Configuration
export const APP_URL = import.meta.env.VITE_APP_URL || 'http://localhost:8080';
export const IS_DEVELOPMENT = import.meta.env.DEV === true;
export const IS_PRODUCTION = import.meta.env.PROD === true;

// Admin Configuration
export const ADMIN_EMAILS = import.meta.env.VITE_ADMIN_EMAILS || '';

// Validate required environment variables
export const validateEnvironment = (): { valid: boolean; missingVars: string[] } => {
  const requiredVars = [
    { name: 'SUPABASE_URL', value: SUPABASE_URL },
    { name: 'SUPABASE_ANON_KEY', value: SUPABASE_ANON_KEY },
  ];

  const missingVars = requiredVars
    .filter(v => !v.value)
    .map(v => v.name);

  return {
    valid: missingVars.length === 0,
    missingVars
  };
};

// Log environment validation on import
const validation = validateEnvironment();
if (!validation.valid) {
  console.error(`Missing required environment variables: ${validation.missingVars.join(', ')}`);
  console.error('Please check your .env file and make sure all required variables are set.');
}
