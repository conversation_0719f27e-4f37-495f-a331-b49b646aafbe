-- Create the paystack_integrations table
CREATE TABLE IF NOT EXISTS paystack_integrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  public_key VARCHAR(255) NOT NULL,
  secret_key VARCHAR(255) NOT NULL,
  webhook_secret VARCHAR(255),
  is_test BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a unique index to ensure one integration per user
CREATE UNIQUE INDEX IF NOT EXISTS idx_paystack_integrations_user_id ON paystack_integrations(user_id);

-- Create a unique index to ensure one integration per organization
CREATE UNIQUE INDEX IF NOT EXISTS idx_paystack_integrations_organization_id ON paystack_integrations(organization_id) WHERE organization_id IS NOT NULL;

-- Enable Row Level Security
ALTER TABLE paystack_integrations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can view their own integrations
CREATE POLICY "Users can view their own Paystack integrations"
ON paystack_integrations FOR SELECT
USING (auth.uid() = user_id);

-- Users can create their own integrations
CREATE POLICY "Users can create their own Paystack integrations"
ON paystack_integrations FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Users can update their own integrations
CREATE POLICY "Users can update their own Paystack integrations"
ON paystack_integrations FOR UPDATE
USING (auth.uid() = user_id);

-- Users can delete their own integrations
CREATE POLICY "Users can delete their own Paystack integrations"
ON paystack_integrations FOR DELETE
USING (auth.uid() = user_id);

-- Organization owners can manage organization integrations
CREATE POLICY "Organization owners can manage Paystack integrations"
ON paystack_integrations FOR ALL
USING (
  organization_id IN (
    SELECT id FROM organizations
    WHERE owner_id = auth.uid()
  )
);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_paystack_integrations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the updated_at timestamp
CREATE TRIGGER update_paystack_integrations_updated_at
BEFORE UPDATE ON paystack_integrations
FOR EACH ROW
EXECUTE FUNCTION update_paystack_integrations_updated_at();

-- Create a function to encrypt sensitive data
CREATE OR REPLACE FUNCTION encrypt_paystack_keys()
RETURNS TRIGGER AS $$
BEGIN
  -- In a real implementation, you would use proper encryption here
  -- For now, we'll just store the keys as is, but in production
  -- you should use pgcrypto or another encryption method
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to encrypt sensitive data
CREATE TRIGGER encrypt_paystack_keys
BEFORE INSERT OR UPDATE ON paystack_integrations
FOR EACH ROW
EXECUTE FUNCTION encrypt_paystack_keys();
