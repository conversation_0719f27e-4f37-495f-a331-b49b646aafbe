const fs = require('fs');
const { createCanvas } = require('canvas');
const path = require('path');

// Icon sizes needed for PWA
const iconSizes = [
    72, 96, 128, 144, 152, 192, 384, 512
];

// Function to generate a single icon
function generateIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Background
    ctx.fillStyle = '#00A651'; // Ghana green
    ctx.fillRect(0, 0, size, size);
    
    // Add a gold accent
    ctx.fillStyle = '#FFD700'; // Ghana gold
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(size * 0.4, 0);
    ctx.lineTo(0, size * 0.4);
    ctx.closePath();
    ctx.fill();
    
    // Add text
    const fontSize = Math.max(size / 5, 12);
    ctx.font = `bold ${fontSize}px Arial`;
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('P', size / 2, size / 2 - fontSize / 2);
    ctx.fillText('V', size / 2, size / 2 + fontSize / 2);
    
    // Add border
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = Math.max(size / 50, 1);
    ctx.strokeRect(0, 0, size, size);
    
    return canvas;
}

// Function to save icon to file
function saveIcon(canvas, filename) {
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(filename, buffer);
    console.log(`Generated ${filename}`);
}

// Generate all icons
function generateAllIcons() {
    const iconsDir = path.join(__dirname, 'icons');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(iconsDir)) {
        fs.mkdirSync(iconsDir, { recursive: true });
    }
    
    // Generate icons for each size
    iconSizes.forEach(size => {
        const canvas = generateIcon(size);
        const filename = path.join(iconsDir, `icon-${size}x${size}.png`);
        saveIcon(canvas, filename);
    });
    
    // Generate apple touch icon
    const appleTouchIcon = generateIcon(180);
    saveIcon(appleTouchIcon, path.join(iconsDir, 'apple-touch-icon.png'));
    
    // Generate favicon
    const favicon = generateIcon(32);
    saveIcon(favicon, path.join(iconsDir, 'favicon.ico'));
}

// Run the generator
generateAllIcons();
