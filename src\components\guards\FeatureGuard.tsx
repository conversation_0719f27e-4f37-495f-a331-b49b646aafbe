import React from 'react';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { getRequiredTierForFeature, getTierName } from '@/utils/subscriptionUtils';

interface FeatureGuardProps {
  children: React.ReactNode;
  featureCode: string;
  fallbackUrl?: string;
  fallbackComponent?: React.ReactNode;
  showUpgradeMessage?: boolean;
}

/**
 * A component that guards access to features based on subscription level.
 * If the user doesn't have access to the feature, it shows an upgrade prompt.
 *
 * @param children The content to render if the user has access to the feature
 * @param featureCode The feature code to check against the subscription
 * @param fallbackUrl The URL to redirect to for upgrading (defaults to subscription management)
 * @param fallbackComponent Custom component to render if the user doesn't have access
 * @param showUpgradeMessage Whether to show the upgrade message (defaults to true)
 *
 * Feature codes include:
 * - basic_invoicing: Basic invoice creation (Free plan)
 * - medium_invoicing: Up to 50 invoices per month (Business plan)
 * - unlimited_invoices: Unlimited invoices (Enterprise plan)
 * - limited_clients: Up to 3 clients (Free plan)
 * - client_management: Unlimited clients (Business plan)
 * - gra_compliance: GRA VAT-compliant invoices (All plans)
 * - advanced_dashboard: Advanced dashboard (Business plan)
 * - paystack_integration: Paystack payment integration (Business plan)
 * - priority_support: Priority support (Business plan)
 * - team_members_1: 1 team member (Business plan)
 * - team_members_5: 5 team members (Enterprise plan)
 * - advanced_analytics: Advanced reporting & analytics (Enterprise plan)
 * - team_roles: Team roles & permissions (Enterprise plan)
 * - api_access: API access (Enterprise plan)
 * - phone_support: Phone support (Enterprise plan)
 */
const FeatureGuard: React.FC<FeatureGuardProps> = ({
  children,
  featureCode,
  fallbackUrl = '/subscription-management',
  fallbackComponent,
  showUpgradeMessage = true
}) => {
  const { canAccessFeature, tier } = useSubscription();

  // Check if user has access to the feature
  const hasAccess = canAccessFeature(featureCode);

  // If user has access, render the children
  if (hasAccess) {
    return <>{children}</>;
  }

  // If a custom fallback component is provided, render it
  if (fallbackComponent) {
    return <>{fallbackComponent}</>;
  }

  // If we don't want to show the upgrade message, render nothing
  if (!showUpgradeMessage) {
    return null;
  }

  // Get the required tier for this feature
  const requiredTier = getRequiredTierForFeature(featureCode);
  const requiredTierName = getTierName(requiredTier);
  const currentTierName = getTierName(tier);

  // Check if the user's tier is actually higher than the required tier
  const tierHierarchy = { 'free': 0, 'freelancer': 1, 'business': 2, 'enterprise': 3 };
  const userTierLevel = tierHierarchy[tier] || 0;
  const requiredTierLevel = tierHierarchy[requiredTier] || 0;
  const isUserTierHigher = userTierLevel > requiredTierLevel;

  // Otherwise, render the default upgrade prompt
  return (
    <div className={`${isUserTierHigher ? 'bg-red-50 border-red-200' : 'bg-amber-50 border-amber-200'} border rounded-md p-6 my-4`}>
      <div className="flex items-center mb-4">
        <Lock className={`h-5 w-5 mr-2 ${isUserTierHigher ? 'text-red-600' : 'text-amber-600'}`} />
        <h3 className={`text-lg font-medium ${isUserTierHigher ? 'text-red-800' : 'text-amber-800'}`}>
          {isUserTierHigher ? 'Access Control Error' : 'Subscription Upgrade Required'}
        </h3>
      </div>
      <p className={`${isUserTierHigher ? 'text-red-700' : 'text-amber-700'} mb-4`}>
        {isUserTierHigher ?
          `You should have access to this feature with your ${currentTierName} plan. Please contact support.` :
          `This feature requires the ${requiredTierName} plan or higher. Your current plan is ${currentTierName}. Upgrade your subscription to access this feature.`
        }
      </p>
      <Link to={fallbackUrl}>
        <Button className="bg-ghana-green hover:bg-ghana-green/90">
          {isUserTierHigher ? 'Contact Support' : `Upgrade to ${requiredTierName}`}
        </Button>
      </Link>
    </div>
  );
};

export default FeatureGuard;
