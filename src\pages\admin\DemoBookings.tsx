import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Calendar, 
  Search, 
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Mail,
  Phone,
  Building
} from 'lucide-react';
import { useSession } from '@/contexts/SessionContext';
import { supabase } from '@/integrations/supabase/client';
import { isAdmin, isAdminEmail } from '@/utils/adminUtils';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, <PERSON>Title } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

interface DemoBooking {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message?: string;
  preferred_date?: string;
  preferred_time?: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

const DemoBookingsAdminPage = () => {
  const navigate = useNavigate();
  const { session, user } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [bookings, setBookings] = useState<DemoBooking[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBooking, setSelectedBooking] = useState<DemoBooking | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    const checkAdminAndFetchBookings = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!session || !user) {
          setIsLoading(false);
          setError('You must be logged in to access the admin dashboard.');
          return;
        }

        const userId = session.user.id;
        const userEmail = user.email || '';
        
        // Check if user is admin
        try {
          const adminStatus = await isAdmin(userId, userEmail);
          
          if (adminStatus) {
            setIsAuthorized(true);
            fetchDemoBookings();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        } catch (adminError) {
          console.error('Error checking admin status:', adminError);
          
          if (isAdminEmail(userEmail)) {
            setIsAuthorized(true);
            fetchDemoBookings();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error in admin verification:', error);
        setIsAuthorized(false);
        setError('An error occurred while verifying admin permissions.');
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAndFetchBookings();
  }, [session, user, navigate]);

  const fetchDemoBookings = async () => {
    try {
      // Since we don't have a demo_bookings table yet, we'll create mock data
      // In a real implementation, you would fetch from your demo_bookings table
      const mockBookings: DemoBooking[] = [
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          company: 'Acme Corp',
          message: 'Interested in learning more about your invoicing solution.',
          preferred_date: '2024-01-15',
          preferred_time: '14:00',
          status: 'pending',
          created_at: '2024-01-10T10:00:00Z',
          updated_at: '2024-01-10T10:00:00Z'
        },
        {
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '+**********',
          company: 'Tech Solutions',
          message: 'Need a demo for our accounting team.',
          preferred_date: '2024-01-16',
          preferred_time: '10:00',
          status: 'confirmed',
          created_at: '2024-01-09T15:30:00Z',
          updated_at: '2024-01-11T09:00:00Z'
        }
      ];

      setBookings(mockBookings);
    } catch (error) {
      console.error('Error fetching demo bookings:', error);
      setError('An error occurred while fetching demo bookings.');
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         booking.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         booking.company?.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (activeTab === 'all') return matchesSearch;
    return matchesSearch && booking.status === activeTab;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      confirmed: 'default',
      completed: 'outline',
      cancelled: 'destructive'
    } as const;
    
    const icons = {
      pending: <Clock className="mr-1 h-3 w-3" />,
      confirmed: <CheckCircle className="mr-1 h-3 w-3" />,
      completed: <CheckCircle className="mr-1 h-3 w-3" />,
      cancelled: <XCircle className="mr-1 h-3 w-3" />
    };
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {icons[status as keyof typeof icons]}
        {status}
      </Badge>
    );
  };

  const updateBookingStatus = async (bookingId: string, newStatus: string) => {
    // In a real implementation, you would update the database
    setBookings(prev => prev.map(booking => 
      booking.id === bookingId 
        ? { ...booking, status: newStatus as any, updated_at: new Date().toISOString() }
        : booking
    ));
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!isAuthorized) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-5">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Demo Bookings</h1>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">{bookings.length} Total</Badge>
            <Badge variant="default">{bookings.filter(b => b.status === 'pending').length} Pending</Badge>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Demo Requests</CardTitle>
            <CardDescription>
              Manage demo booking requests from potential customers.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <div className="flex items-center justify-between">
                <TabsList>
                  <TabsTrigger value="all">All Bookings</TabsTrigger>
                  <TabsTrigger value="pending">Pending</TabsTrigger>
                  <TabsTrigger value="confirmed">Confirmed</TabsTrigger>
                  <TabsTrigger value="completed">Completed</TabsTrigger>
                  <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
                </TabsList>
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search bookings..."
                    value={searchQuery}
                    onChange={handleSearch}
                    className="w-64"
                  />
                </div>
              </div>

              <TabsContent value={activeTab} className="space-y-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Contact</TableHead>
                        <TableHead>Company</TableHead>
                        <TableHead>Preferred Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBookings.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            No demo bookings found.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredBookings.map((booking) => (
                          <TableRow key={booking.id}>
                            <TableCell>
                              <div>
                                <div className="flex items-center font-medium">
                                  <User className="mr-2 h-4 w-4" />
                                  {booking.name}
                                </div>
                                <div className="flex items-center text-sm text-muted-foreground">
                                  <Mail className="mr-2 h-3 w-3" />
                                  {booking.email}
                                </div>
                                {booking.phone && (
                                  <div className="flex items-center text-sm text-muted-foreground">
                                    <Phone className="mr-2 h-3 w-3" />
                                    {booking.phone}
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {booking.company ? (
                                <div className="flex items-center">
                                  <Building className="mr-2 h-4 w-4" />
                                  {booking.company}
                                </div>
                              ) : (
                                <span className="text-muted-foreground">Not specified</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {booking.preferred_date ? (
                                <div>
                                  <div className="flex items-center">
                                    <Calendar className="mr-2 h-4 w-4" />
                                    {new Date(booking.preferred_date).toLocaleDateString()}
                                  </div>
                                  {booking.preferred_time && (
                                    <div className="text-sm text-muted-foreground">
                                      {booking.preferred_time}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <span className="text-muted-foreground">Flexible</span>
                              )}
                            </TableCell>
                            <TableCell>{getStatusBadge(booking.status)}</TableCell>
                            <TableCell>
                              {new Date(booking.created_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedBooking(booking);
                                    setIsViewDialogOpen(true);
                                  }}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                {booking.status === 'pending' && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => updateBookingStatus(booking.id, 'confirmed')}
                                  >
                                    Confirm
                                  </Button>
                                )}
                                {booking.status === 'confirmed' && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => updateBookingStatus(booking.id, 'completed')}
                                  >
                                    Complete
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* View Booking Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Demo Booking Details</DialogTitle>
            <DialogDescription>
              Complete information about this demo booking request.
            </DialogDescription>
          </DialogHeader>
          {selectedBooking && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Name</h3>
                  <p className="text-sm">{selectedBooking.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
                  <p className="text-sm">{selectedBooking.email}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Phone</h3>
                  <p className="text-sm">{selectedBooking.phone || 'Not provided'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Company</h3>
                  <p className="text-sm">{selectedBooking.company || 'Not provided'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Preferred Date</h3>
                  <p className="text-sm">
                    {selectedBooking.preferred_date 
                      ? new Date(selectedBooking.preferred_date).toLocaleDateString()
                      : 'Flexible'
                    }
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Preferred Time</h3>
                  <p className="text-sm">{selectedBooking.preferred_time || 'Flexible'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                  <p className="text-sm">{getStatusBadge(selectedBooking.status)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Created</h3>
                  <p className="text-sm">{new Date(selectedBooking.created_at).toLocaleString()}</p>
                </div>
              </div>
              {selectedBooking.message && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Message</h3>
                  <p className="text-sm bg-muted p-3 rounded">{selectedBooking.message}</p>
                </div>
              )}
            </div>
          )}
          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
            {selectedBooking?.status === 'pending' && (
              <Button 
                onClick={() => {
                  updateBookingStatus(selectedBooking.id, 'confirmed');
                  setIsViewDialogOpen(false);
                }}
              >
                Confirm Booking
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default DemoBookingsAdminPage;
