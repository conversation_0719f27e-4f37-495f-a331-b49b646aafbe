import React, { useState, useEffect, useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useSession } from '@/contexts/SessionContext';
import { motion } from 'framer-motion';

// UI Components
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';
import {
  BarChart3,
  CreditCard,
  FileText,
  Home,
  LineChart,
  LogOut,
  Menu,
  Settings,
  User,
  Users,
  Bell,
  Sun,
  Moon,
  ChevronDown,
  Laptop,
  ShieldCheck
} from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import NotificationBell from '@/components/notifications/NotificationBell';
import AdminIcon from '@/components/AdminIcon';
import { AnimatedButton } from '@/components/ui/animated-button';
import PageTransition from '@/components/PageTransition';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

// Types
interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { user, signOut } = useSession();
  const { canAccessFeature } = useSubscription();
  const { theme, setTheme } = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Memoize the navigation items to prevent unnecessary re-renders
  const navItems = useMemo(() => {
    // Base navigation items
    const baseNavItems = [
      { path: '/dashboard', label: 'Dashboard', icon: <Home className="h-5 w-5" /> },
      { path: '/invoices', label: 'Invoices', icon: <FileText className="h-5 w-5" /> },
      { path: '/clients', label: 'Clients', icon: <Users className="h-5 w-5" /> },
      { path: '/reports', label: 'Reports', icon: <BarChart3 className="h-5 w-5" /> },
      { path: '/subscription', label: 'Subscription', icon: <CreditCard className="h-5 w-5" /> },
    ];

    // Settings is always available
    const settingsItem = { path: '/settings', label: 'Settings', icon: <Settings className="h-5 w-5" /> };

    // Advanced navigation items (based on subscription)
    const advancedNavItems = [];

    // Add analytics if the subscription context is loaded and user has access
    try {
      if (canAccessFeature && canAccessFeature('analytics')) {
        advancedNavItems.push({
          path: '/analytics',
          label: 'Analytics',
          icon: <BarChart3 className="h-5 w-5" />
        });
      }

      // Add advanced analytics if the subscription context is loaded and user has access
      if (canAccessFeature && canAccessFeature('advanced_analytics')) {
        advancedNavItems.push({
          path: '/advanced-analytics',
          label: 'Advanced Analytics',
          icon: <LineChart className="h-5 w-5" />
        });
      }
    } catch (error) {
      console.error('Error checking feature access:', error);
    }

    // Combine all navigation items
    return [...baseNavItems, ...advancedNavItems, settingsItem];
  }, [canAccessFeature]); // Only recalculate when canAccessFeature changes

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/auth');
      toast({
        title: 'Signed out',
        description: 'You have been signed out successfully',
      });
    } catch (error) {
      console.error('Error signing out:', error);
      toast({
        title: 'Error',
        description: 'Failed to sign out',
        variant: 'destructive',
      });
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (!user || !user.email) return 'U';
    return user.email.charAt(0).toUpperCase();
  };

  // Check if a nav item is active
  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col dark:bg-background">
      {/* Top Navigation Bar */}
      <header className="bg-card border-b border-border sticky top-0 z-30 dark:shadow-md">
        <div className="container mx-auto px-2 sm:px-4 h-16 flex items-center justify-between">
          {/* Logo and Mobile Menu Button */}
          <div className="flex items-center gap-2">
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild className="md:hidden">
                <AnimatedButton variant="ghost" size="icon" className="mr-1" animationType="scale">
                  <Menu className="h-5 w-5" />
                </AnimatedButton>
              </SheetTrigger>
              <SheetContent side="left" className="w-[85vw] max-w-[300px] p-0 dark:bg-card dark:border-border">
                <SheetHeader className="p-4 border-b dark:border-border">
                  <SheetTitle>
                    <Link to="/" className="flex items-center gap-2">
                      <div className="h-8 w-auto">
                        <img src="/logo.png" alt="Receipta Logo" className="h-full w-auto dark:dark-invert" />
                      </div>
                      <span className="font-display font-bold text-xl dark:text-foreground">Payvoicer</span>
                    </Link>
                  </SheetTitle>
                </SheetHeader>
                <nav className="p-4 overflow-y-auto max-h-[calc(100vh-80px)]">
                  {/* User Profile Section */}
                  <div className="flex items-center gap-3 p-2 border-b mb-4 dark:border-border">
                    <Link to="/profile" className="flex-shrink-0" onClick={() => setIsMobileMenuOpen(false)}>
                      <Avatar className="h-10 w-10 cursor-pointer hover:ring-2 hover:ring-ghana-green/20 transition-all dark:border dark:border-border">
                        <AvatarImage src={user?.user_metadata?.avatar_url} />
                        <AvatarFallback className="bg-ghana-gold text-ghana-black dark:bg-ghana-gold/90">
                          {getUserInitials()}
                        </AvatarFallback>
                      </Avatar>
                    </Link>
                    <div className="flex flex-col">
                      <span className="font-medium dark:text-foreground">{user?.user_metadata?.full_name || 'User'}</span>
                      <span className="text-xs text-muted-foreground truncate max-w-[180px] dark:text-muted-foreground">{user?.email}</span>
                    </div>
                  </div>

                  {/* Admin Link - Only visible for admin users */}
                  <AdminIcon inDropdown={true} />

                  {/* Navigation Items */}
                  <div className="mb-4">
                    <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                      Navigation
                    </div>
                    <ul className="space-y-1">
                      {navItems.map((item) => (
                        <li key={item.path}>
                          <motion.div
                            whileHover={{ x: 3 }}
                            whileTap={{ scale: 0.98 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Link
                              to={item.path}
                              className={`flex items-center gap-3 px-3 py-2.5 rounded-md transition-colors ${
                                isActive(item.path)
                                  ? 'bg-ghana-green text-white dark:bg-ghana-green/90'
                                  : 'hover:bg-gray-100 dark:hover:bg-gray-800/30 dark:text-foreground'
                              }`}
                              onClick={() => setIsMobileMenuOpen(false)}
                            >
                              {item.icon}
                              <span className="text-base">{item.label}</span>
                            </Link>
                          </motion.div>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Settings Section */}
                  <div className="mb-4">
                    <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                      Settings
                    </div>
                    <ul className="space-y-1">
                      <li>
                        <Link to="/notifications" className="flex items-center gap-3 px-3 py-2.5 rounded-md hover:bg-gray-100 transition-colors" onClick={() => setIsMobileMenuOpen(false)}>
                          <Bell className="h-5 w-5" />
                          <span className="text-base">Notifications</span>
                        </Link>
                      </li>
                      <li className="px-3 py-1.5 text-xs font-semibold text-muted-foreground">
                        Theme: {theme === 'system' ? 'System' : theme === 'dark' ? 'Dark' : 'Light'}
                      </li>
                      <li>
                        <motion.div
                          whileHover={{ x: 3 }}
                          whileTap={{ scale: 0.98 }}
                          transition={{ duration: 0.2 }}
                        >
                          <div
                            className={`flex items-center gap-3 px-3 py-2.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800/30 transition-colors cursor-pointer ${
                              theme === 'light' ? 'bg-gray-100 dark:bg-gray-800/40' : ''
                            }`}
                            onClick={() => {
                              setTheme('light');
                              setIsMobileMenuOpen(false);
                            }}
                          >
                            <Sun className="h-5 w-5 dark:text-foreground" />
                            <span className={`text-base dark:text-foreground ${theme === 'light' ? 'font-medium text-ghana-green dark:text-ghana-gold' : ''}`}>Light</span>
                          </div>
                        </motion.div>
                      </li>
                      <li>
                        <motion.div
                          whileHover={{ x: 3 }}
                          whileTap={{ scale: 0.98 }}
                          transition={{ duration: 0.2 }}
                        >
                          <div
                            className={`flex items-center gap-3 px-3 py-2.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800/30 transition-colors cursor-pointer ${
                              theme === 'dark' ? 'bg-gray-100 dark:bg-gray-800/40' : ''
                            }`}
                            onClick={() => {
                              setTheme('dark');
                              setIsMobileMenuOpen(false);
                            }}
                          >
                            <Moon className="h-5 w-5 dark:text-foreground" />
                            <span className={`text-base dark:text-foreground ${theme === 'dark' ? 'font-medium text-ghana-green dark:text-ghana-gold' : ''}`}>Dark</span>
                          </div>
                        </motion.div>
                      </li>
                      <li>
                        <motion.div
                          whileHover={{ x: 3 }}
                          whileTap={{ scale: 0.98 }}
                          transition={{ duration: 0.2 }}
                        >
                          <div
                            className={`flex items-center gap-3 px-3 py-2.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800/30 transition-colors cursor-pointer ${
                              theme === 'system' ? 'bg-gray-100 dark:bg-gray-800/40' : ''
                            }`}
                            onClick={() => {
                              setTheme('system');
                              setIsMobileMenuOpen(false);
                            }}
                          >
                            <Laptop className="h-5 w-5 dark:text-foreground" />
                            <span className={`text-base dark:text-foreground ${theme === 'system' ? 'font-medium text-ghana-green dark:text-ghana-gold' : ''}`}>System</span>
                          </div>
                        </motion.div>
                      </li>
                    </ul>
                  </div>

                  <Separator className="my-4 dark:bg-border" />

                  {/* Sign Out Button */}
                  <motion.div
                    whileHover={{ x: 3 }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 px-3 py-3 h-auto dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-950/30"
                      onClick={() => {
                        setIsMobileMenuOpen(false);
                        handleSignOut();
                      }}
                    >
                      <LogOut className="mr-3 h-5 w-5" />
                      <span className="text-base">Sign Out</span>
                    </Button>
                  </motion.div>
                </nav>
              </SheetContent>
            </Sheet>

            <Link to="/" className="flex items-center gap-2">
              <div className="h-8 w-auto">
                <img src="/logo.png" alt="Receipta Logo" className="h-full w-auto dark:dark-invert" />
              </div>
              <span className="font-display font-bold text-xl hidden md:inline dark:text-foreground">Payvoicer</span>
            </Link>
          </div>

          {/* User Menu */}
          <div className="flex items-center gap-2 sm:gap-4">
            {/* Admin Icon - Only visible for admin users */}
            <AdminIcon />

            {/* Notification Bell */}
            <NotificationBell />

            {/* User Profile and Dropdown */}
            <div className="flex items-center gap-2">
              {/* Direct link to profile when clicking the avatar */}
              <Link to="/profile">
                <Avatar className="h-9 w-9 cursor-pointer hover:ring-2 hover:ring-ghana-green/20 transition-all">
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback className="bg-ghana-gold text-ghana-black">
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
              </Link>

              {/* Dropdown menu for quick actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="gap-1 h-9 px-2 text-sm">
                    <span className="hidden sm:inline-block">{user?.user_metadata?.full_name?.split(' ')[0] || 'Account'}</span>
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 dark:bg-card dark:border-border dark:shadow-md" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none dark:text-foreground">{user?.user_metadata?.full_name || 'User'}</p>
                      <p className="text-xs leading-none text-muted-foreground dark:text-muted-foreground">{user?.email}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  {/* Account Management */}
                  <Link to="/profile" className="w-full">
                    <DropdownMenuItem>
                      <User className="mr-2 h-4 w-4" />
                      <span>My Profile</span>
                    </DropdownMenuItem>
                  </Link>
                  <Link to="/dashboard" className="w-full">
                    <DropdownMenuItem>
                      <Home className="mr-2 h-4 w-4" />
                      <span>Dashboard</span>
                    </DropdownMenuItem>
                  </Link>
                  <Link to="/invoices" className="w-full">
                    <DropdownMenuItem>
                      <FileText className="mr-2 h-4 w-4" />
                      <span>My Invoices</span>
                    </DropdownMenuItem>
                  </Link>

                  {/* Settings */}
                  <DropdownMenuSeparator />
                  <Link to="/settings" className="w-full">
                    <DropdownMenuItem>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                  </Link>
                  <Link to="/subscription" className="w-full">
                    <DropdownMenuItem>
                      <CreditCard className="mr-2 h-4 w-4" />
                      <span>Subscription</span>
                    </DropdownMenuItem>
                  </Link>
                  <Link to="/notifications" className="w-full">
                    <DropdownMenuItem>
                      <Bell className="mr-2 h-4 w-4" />
                      <span>Notifications</span>
                    </DropdownMenuItem>
                  </Link>

                  {/* Appearance */}
                  <DropdownMenuSeparator className="dark:bg-border" />
                  <DropdownMenuLabel className="font-normal text-xs text-muted-foreground">
                    Theme
                  </DropdownMenuLabel>
                  <DropdownMenuItem
                    onClick={() => setTheme('light')}
                    className={`dark:focus:bg-gray-800/50 dark:text-foreground ${theme === 'light' ? 'bg-accent dark:bg-gray-800/70' : ''}`}
                  >
                    <Sun className="mr-2 h-4 w-4" />
                    <span>Light</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setTheme('dark')}
                    className={`dark:focus:bg-gray-800/50 dark:text-foreground ${theme === 'dark' ? 'bg-accent dark:bg-gray-800/70' : ''}`}
                  >
                    <Moon className="mr-2 h-4 w-4" />
                    <span>Dark</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setTheme('system')}
                    className={`dark:focus:bg-gray-800/50 dark:text-foreground ${theme === 'system' ? 'bg-accent dark:bg-gray-800/70' : ''}`}
                  >
                    <Laptop className="mr-2 h-4 w-4" />
                    <span>System</span>
                  </DropdownMenuItem>

                  {/* Sign Out */}
                  <DropdownMenuSeparator className="dark:bg-border" />
                  <DropdownMenuItem
                    className="text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400 dark:focus:bg-gray-800/50"
                    onClick={handleSignOut}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content Area with Sidebar */}
      <div className="flex flex-1">
        {/* Sidebar - Desktop Only */}
        <aside className="hidden md:block w-64 bg-card border-r border-border shrink-0 dark:shadow-md">
          <nav className="p-4 sticky top-16">
            <ul className="space-y-2">
              {navItems.map((item) => (
                <li key={item.path}>
                  <motion.div
                    whileHover={{ x: 3 }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      to={item.path}
                      className={`flex items-center gap-3 px-3 py-2 rounded-md transition-colors ${
                        isActive(item.path)
                          ? 'bg-ghana-green text-white dark:bg-ghana-green/90'
                          : 'hover:bg-gray-100 dark:text-foreground dark:hover:bg-gray-800/30'
                      }`}
                    >
                      {item.icon}
                      <span>{item.label}</span>
                    </Link>
                  </motion.div>
                </li>
              ))}
              <Separator className="my-4 dark:bg-border" />
              <li>
                <AnimatedButton
                  variant="ghost"
                  className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-950/30"
                  onClick={handleSignOut}
                  animationType="scale"
                >
                  <LogOut className="mr-2 h-5 w-5" />
                  <span>Sign Out</span>
                </AnimatedButton>
              </li>
            </ul>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 overflow-auto pb-16 md:pb-0 dark:bg-background">
          <PageTransition>
            {children}
          </PageTransition>
        </main>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-card border-t border-border z-30 dark:shadow-lg">
        <div className="flex justify-around items-center h-16">
          {navItems.slice(0, 5).map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`flex flex-col items-center justify-center w-full h-full px-1 ${
                isActive(item.path)
                  ? 'text-ghana-green dark:text-ghana-gold'
                  : 'text-gray-500 hover:text-ghana-green dark:text-gray-300 dark:hover:text-ghana-gold'
              }`}
            >
              <div className={`p-1 rounded-full ${isActive(item.path) ? 'bg-ghana-green/10 dark:bg-ghana-gold/10' : ''}`}>
                {item.icon}
              </div>
              <span className="text-xs mt-1 font-medium">{item.label}</span>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
