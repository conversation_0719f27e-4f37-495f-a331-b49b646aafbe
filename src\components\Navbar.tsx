
import React from 'react';
// We're using our custom ScrollLink instead of the default Link
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSession } from '@/contexts/SessionContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import ScrollLink from './ScrollLink';
import AdminIcon from './AdminIcon';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  User,
  Settings,
  LogOut,
  LayoutDashboard,
  CreditCard,
  FileText,
  Sun,
  Moon,
  ChevronDown,
  Bell,
  Laptop,
  ShieldCheck
} from 'lucide-react';

// Use ScrollLink for all navigation to ensure smooth scrolling to top
const Link = ScrollLink;

const Navbar: React.FC = () => {
  const isMobile = useIsMobile();
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const { user, signOut } = useSession();
  const { theme, setTheme } = useTheme();

  const getInitials = (name: string | null | undefined) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <nav className="fixed top-0 left-0 right-0 bg-card/95 backdrop-blur-md shadow-md py-4 z-50 transition-all duration-300 border-b border-border dark:bg-card/95">
      <div className="container flex items-center justify-between">
        <Link to="/" className="flex items-center gap-2">
          <div className="h-8 w-auto">
            <img src="/logo.png" alt="Receipta Logo" className="h-full w-auto dark:dark-invert" />
          </div>
          <span className="font-display font-bold text-xl dark:text-foreground">Payvoicer</span>
        </Link>

        {isMobile ? (
          <>
            <Button
              variant={isMenuOpen ? "ghost" : "outline"}
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`p-2 ${isMenuOpen ? 'bg-gray-100 dark:bg-gray-800' : 'dark:border-gray-700 dark:text-foreground'}`}
              aria-label="Toggle menu"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                {isMenuOpen ? (
                  <>
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </>
                ) : (
                  <>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                  </>
                )}
              </svg>
              <span className="sr-only">{isMenuOpen ? 'Close menu' : 'Open menu'}</span>
            </Button>
            {isMenuOpen && (
              <div className="absolute top-full left-0 right-0 bg-card shadow-md py-4 px-6 flex flex-col gap-2 animate-fade-in max-h-[80vh] overflow-y-auto z-50 border-b border-border dark:shadow-lg">
                {/* Main Navigation */}
                <div className="mb-2">
                  <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                    Navigation
                  </div>
                  <Link to="/features" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                    <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                      Features
                    </div>
                  </Link>
                  <Link to="/pricing" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                    <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                      Pricing
                    </div>
                  </Link>
                  <Link to="/about" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                    <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                      About
                    </div>
                  </Link>
                  <Link to="/contact" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                    <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                      Contact
                    </div>
                  </Link>
                </div>
                {user ? (
                  <>
                    <div className="flex items-center gap-3 p-2 border-b mb-2 dark:border-border">
                      <Link to="/profile" className="flex-shrink-0">
                        <Avatar className="h-10 w-10 cursor-pointer hover:ring-2 hover:ring-ghana-green/20 transition-all dark:border dark:border-border">
                          <AvatarImage src={user?.user_metadata?.avatar_url} />
                          <AvatarFallback className="dark:bg-ghana-gold/90 dark:text-ghana-black">{getInitials(user?.user_metadata?.full_name || user?.email)}</AvatarFallback>
                        </Avatar>
                      </Link>
                      <div className="flex flex-col">
                        <span className="font-medium dark:text-foreground">{user?.user_metadata?.full_name || 'User'}</span>
                        <span className="text-xs text-muted-foreground truncate max-w-[180px] dark:text-muted-foreground">{user?.email}</span>
                      </div>
                    </div>

                    {/* Account Management */}
                    <div className="mb-2">
                      <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                        Account
                      </div>
                      <Link to="/profile" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                        <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                          <User className="h-4 w-4" />
                          My Profile
                        </div>
                      </Link>
                      <Link to="/dashboard" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                        <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                          <LayoutDashboard className="h-4 w-4" />
                          Dashboard
                        </div>
                      </Link>
                      <Link to="/invoices" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                        <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                          <FileText className="h-4 w-4" />
                          My Invoices
                        </div>
                      </Link>
                    </div>

                    {/* Settings */}
                    <div className="mb-2">
                      <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                        Settings
                      </div>
                      <Link to="/settings" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                        <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                          <Settings className="h-4 w-4" />
                          Settings
                        </div>
                      </Link>
                      <Link to="/subscription-management" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                        <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                          <CreditCard className="h-4 w-4" />
                          Subscription
                        </div>
                      </Link>
                      <Link to="/notifications" className="hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                        <div className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md dark:text-foreground">
                          <Bell className="h-4 w-4" />
                          Notifications
                        </div>
                      </Link>
                      <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground">
                        Theme: {theme === 'system' ? 'System' : theme === 'dark' ? 'Dark' : 'Light'}
                      </div>
                      <div
                        className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md cursor-pointer dark:text-foreground"
                        onClick={() => setTheme('light')}
                      >
                        <Sun className="h-4 w-4" />
                        <span className={theme === 'light' ? 'font-medium text-ghana-green dark:text-ghana-gold' : ''}>Light</span>
                      </div>
                      <div
                        className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md cursor-pointer dark:text-foreground"
                        onClick={() => setTheme('dark')}
                      >
                        <Moon className="h-4 w-4" />
                        <span className={theme === 'dark' ? 'font-medium text-ghana-green dark:text-ghana-gold' : ''}>Dark</span>
                      </div>
                      <div
                        className="flex items-center gap-2 py-2 px-2 hover:bg-gray-50 dark:hover:bg-gray-800/30 rounded-md cursor-pointer dark:text-foreground"
                        onClick={() => setTheme('system')}
                      >
                        <Laptop className="h-4 w-4" />
                        <span className={theme === 'system' ? 'font-medium text-ghana-green dark:text-ghana-gold' : ''}>System</span>
                      </div>
                    </div>

                    {/* Sign Out */}
                    <Button
                      variant="outline"
                      onClick={signOut}
                      className="w-full mt-2 text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:border-red-900/30 dark:hover:bg-red-950/30 dark:hover:text-red-300"
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      Sign Out
                    </Button>
                  </>
                ) : (
                  <div className="flex flex-col gap-2 mt-2">
                    <Link to="/auth">
                      <Button variant="outline" className="w-full dark:border-border dark:text-foreground dark:hover:bg-gray-800/30">Log In</Button>
                    </Link>
                    <Link to="/auth">
                      <Button
                        className="w-full bg-ghana-green hover:bg-ghana-green-light text-white dark:bg-ghana-green/90 dark:hover:bg-ghana-green"
                        onClick={() => {
                          // Set signup mode by adding query param
                          localStorage.setItem('authMode', 'signup');
                        }}
                      >
                        Sign Up
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            )}
          </>
        ) : (
          <>
            <div className="flex items-center gap-8">
              <Link to="/features" className="hover:text-ghana-green transition-colors dark:text-foreground dark:hover:text-ghana-gold">Features</Link>
              <Link to="/pricing" className="hover:text-ghana-green transition-colors dark:text-foreground dark:hover:text-ghana-gold">Pricing</Link>
              <Link to="/about" className="hover:text-ghana-green transition-colors dark:text-foreground dark:hover:text-ghana-gold">About</Link>
              <Link to="/contact" className="hover:text-ghana-green transition-colors dark:text-foreground dark:hover:text-ghana-gold">Contact</Link>
              {user && (
                <Link to="/dashboard" className="hover:text-ghana-green transition-colors font-medium dark:text-foreground dark:hover:text-ghana-gold">Dashboard</Link>
              )}
            </div>
            {user ? (
              <div className="flex items-center gap-4">
                {/* Admin Icon - Only visible for admin users */}
                <AdminIcon />

                <div className="flex items-center gap-2">
                  {/* Direct link to profile when clicking the avatar */}
                  <Link to="/profile">
                    <Avatar className="h-9 w-9 cursor-pointer hover:ring-2 hover:ring-ghana-green/20 transition-all dark:border dark:border-border">
                      <AvatarImage src={user?.user_metadata?.avatar_url} />
                      <AvatarFallback className="dark:bg-ghana-gold/90 dark:text-ghana-black">{getInitials(user?.user_metadata?.full_name || user?.email)}</AvatarFallback>
                    </Avatar>
                  </Link>

                  {/* Dropdown menu for quick actions */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="gap-1 h-9 px-2 text-sm dark:text-foreground dark:hover:bg-gray-800/30">
                        <span className="hidden sm:inline-block">{user?.user_metadata?.full_name?.split(' ')[0] || 'Account'}</span>
                        <ChevronDown className="h-4 w-4 opacity-50" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56 dark:bg-card dark:border-border dark:shadow-md" align="end" forceMount>
                      <DropdownMenuLabel className="font-normal">
                        <div className="flex flex-col space-y-1">
                          <p className="text-sm font-medium leading-none dark:text-foreground">{user?.user_metadata?.full_name || 'User'}</p>
                          <p className="text-xs leading-none text-muted-foreground dark:text-muted-foreground">{user?.email}</p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator className="dark:bg-border" />

                      {/* Account Management */}
                      <Link to="/profile" className="w-full">
                        <DropdownMenuItem className="dark:focus:bg-gray-800/50 dark:text-foreground">
                          <User className="mr-2 h-4 w-4" />
                          <span>My Profile</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link to="/dashboard" className="w-full">
                        <DropdownMenuItem className="dark:focus:bg-gray-800/50 dark:text-foreground">
                          <LayoutDashboard className="mr-2 h-4 w-4" />
                          <span>Dashboard</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link to="/invoices" className="w-full">
                        <DropdownMenuItem className="dark:focus:bg-gray-800/50 dark:text-foreground">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>My Invoices</span>
                        </DropdownMenuItem>
                      </Link>

                      {/* Admin Dashboard Link - This will be conditionally rendered by AdminIcon component */}
                      <AdminIcon inDropdown={true} />

                      {/* Settings */}
                      <DropdownMenuSeparator className="dark:bg-border" />
                      <Link to="/settings" className="w-full">
                        <DropdownMenuItem className="dark:focus:bg-gray-800/50 dark:text-foreground">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>Settings</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link to="/subscription-management" className="w-full">
                        <DropdownMenuItem className="dark:focus:bg-gray-800/50 dark:text-foreground">
                          <CreditCard className="mr-2 h-4 w-4" />
                          <span>Subscription</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link to="/notifications" className="w-full">
                        <DropdownMenuItem className="dark:focus:bg-gray-800/50 dark:text-foreground">
                          <Bell className="mr-2 h-4 w-4" />
                          <span>Notifications</span>
                        </DropdownMenuItem>
                      </Link>

                      {/* Appearance */}
                      <DropdownMenuSeparator className="dark:bg-border" />
                      <DropdownMenuLabel className="font-normal text-xs text-muted-foreground">
                        Theme
                      </DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => setTheme('light')}
                        className={`dark:focus:bg-gray-800/50 dark:text-foreground ${theme === 'light' ? 'bg-accent dark:bg-gray-800/70' : ''}`}
                      >
                        <Sun className="mr-2 h-4 w-4" />
                        <span>Light</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setTheme('dark')}
                        className={`dark:focus:bg-gray-800/50 dark:text-foreground ${theme === 'dark' ? 'bg-accent dark:bg-gray-800/70' : ''}`}
                      >
                        <Moon className="mr-2 h-4 w-4" />
                        <span>Dark</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setTheme('system')}
                        className={`dark:focus:bg-gray-800/50 dark:text-foreground ${theme === 'system' ? 'bg-accent dark:bg-gray-800/70' : ''}`}
                      >
                        <Laptop className="mr-2 h-4 w-4" />
                        <span>System</span>
                      </DropdownMenuItem>

                      {/* Sign Out */}
                      <DropdownMenuSeparator className="dark:bg-border" />
                      <DropdownMenuItem
                        className="text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400 dark:focus:bg-gray-800/50"
                        onClick={signOut}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        <span>Sign Out</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-4">
                <Link to="/auth">
                  <Button variant="outline" className="dark:border-border dark:text-foreground dark:hover:bg-gray-800/30">Log In</Button>
                </Link>
                <Link to="/auth">
                  <Button
                    className="bg-ghana-green hover:bg-ghana-green-light text-white dark:bg-ghana-green/90 dark:hover:bg-ghana-green"
                    onClick={() => {
                      // Set signup mode by adding query param
                      localStorage.setItem('authMode', 'signup');
                    }}
                  >
                    Sign Up
                  </Button>
                </Link>
              </div>
            )}
          </>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
