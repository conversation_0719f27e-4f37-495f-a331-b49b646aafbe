import { ReactNode, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  CreditCard,
  Settings,
  FileText,
  BarChart3,
  Bell,
  HelpCircle,
  ChevronRight,
  ChevronLeft,
  Briefcase,
  GitBranch,
  LifeBuoy,
  BookOpen,
  Code,
  Shield,
  CheckSquare,
  Lock,
  Calendar
} from 'lucide-react';
import { useSession } from '@/contexts/SessionContext';
import { isAdmin } from '@/utils/adminUtils';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';

interface AdminLayoutProps {
  children: ReactNode;
}

interface SidebarItem {
  title: string;
  icon: ReactNode;
  href: string;
  isActive?: boolean;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const navigate = useNavigate();
  const { session, user } = useSession();
  const [isAdminUser, setIsAdmin] = useState<boolean | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!session || !user) {
        setIsAdmin(false);
        return;
      }

      const userId = session.user.id;
      const userEmail = user.email || '';

      const adminStatus = await isAdmin(userId, userEmail);
      setIsAdmin(adminStatus);

      if (!adminStatus) {
        navigate('/dashboard');
      }
    };

    checkAdminStatus();
  }, [session, user, navigate]);

  // Loading state
  if (isAdminUser === null) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Not admin, should redirect
  if (isAdminUser === false) {
    return null;
  }

  const sidebarItems: SidebarItem[] = [
    // Dashboard & Core
    { title: 'Overview', icon: <LayoutDashboard size={20} />, href: '/admin' },
    { title: 'Users', icon: <Users size={20} />, href: '/admin/users' },
    { title: 'Payments', icon: <CreditCard size={20} />, href: '/admin/payments' },
    { title: 'Invoices', icon: <FileText size={20} />, href: '/admin/invoices' },
    { title: 'Analytics', icon: <BarChart3 size={20} />, href: '/admin/analytics' },

    // Content Management
    { title: 'Blog', icon: <FileText size={20} />, href: '/admin/blog' },
    { title: 'Press', icon: <FileText size={20} />, href: '/admin/press' },
    { title: 'Careers', icon: <Briefcase size={20} />, href: '/admin/careers' },
    { title: 'Roadmap', icon: <GitBranch size={20} />, href: '/admin/roadmap' },
    { title: 'FAQs', icon: <HelpCircle size={20} />, href: '/admin/faqs' },

    // Documentation
    { title: 'Help Center', icon: <LifeBuoy size={20} />, href: '/admin/help-center' },
    { title: 'Guides', icon: <BookOpen size={20} />, href: '/admin/guides' },
    { title: 'API Docs', icon: <Code size={20} />, href: '/admin/api-docs' },
    { title: 'Community', icon: <Users size={20} />, href: '/admin/community' },

    // Legal & Compliance
    { title: 'Terms of Service', icon: <FileText size={20} />, href: '/admin/terms' },
    { title: 'Privacy Policy', icon: <Shield size={20} />, href: '/admin/privacy' },
    { title: 'GRA Compliance', icon: <CheckSquare size={20} />, href: '/admin/gra-compliance' },
    { title: 'Security Policy', icon: <Lock size={20} />, href: '/admin/security' },

    // Demo & Settings
    { title: 'Demo Bookings', icon: <Calendar size={20} />, href: '/admin/demo-bookings' },
    { title: 'Notifications', icon: <Bell size={20} />, href: '/admin/notifications' },
    { title: 'Settings', icon: <Settings size={20} />, href: '/admin/settings' },
  ];

  const SidebarContent = () => {
    // Group sidebar items
    const dashboardItems = sidebarItems.slice(0, 5);
    const contentItems = sidebarItems.slice(5, 10);
    const documentationItems = sidebarItems.slice(10, 14);
    const legalItems = sidebarItems.slice(14, 18);
    const settingsItems = sidebarItems.slice(18);

    return (
      <div className="flex h-full flex-col gap-2">
        <div className="flex h-14 items-center border-b px-4">
          <div className="flex items-center gap-2 font-semibold">
            <LayoutDashboard className="h-6 w-6" />
            <span>Admin Dashboard</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="ml-auto h-8 w-8 lg:hidden"
            onClick={() => setIsMobileOpen(false)}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Close sidebar</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="ml-auto h-8 w-8 hidden lg:flex"
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Toggle sidebar</span>
          </Button>
        </div>
        <ScrollArea className="flex-1 px-2">
          <div className="space-y-4 py-2">
            {/* Dashboard & Core */}
            <div>
              <h4 className="mb-1 px-2 text-xs font-semibold text-muted-foreground">Dashboard</h4>
              <div className="space-y-1">
                {dashboardItems.map((item) => (
                  <Button
                    key={item.title}
                    variant={item.isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start",
                      item.isActive && "bg-muted font-medium"
                    )}
                    onClick={() => {
                      navigate(item.href);
                      setIsMobileOpen(false);
                    }}
                  >
                    {item.icon}
                    <span className="ml-2">{item.title}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Content Management */}
            <div>
              <h4 className="mb-1 px-2 text-xs font-semibold text-muted-foreground">Content</h4>
              <div className="space-y-1">
                {contentItems.map((item) => (
                  <Button
                    key={item.title}
                    variant={item.isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start",
                      item.isActive && "bg-muted font-medium"
                    )}
                    onClick={() => {
                      navigate(item.href);
                      setIsMobileOpen(false);
                    }}
                  >
                    {item.icon}
                    <span className="ml-2">{item.title}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Documentation */}
            <div>
              <h4 className="mb-1 px-2 text-xs font-semibold text-muted-foreground">Documentation</h4>
              <div className="space-y-1">
                {documentationItems.map((item) => (
                  <Button
                    key={item.title}
                    variant={item.isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start",
                      item.isActive && "bg-muted font-medium"
                    )}
                    onClick={() => {
                      navigate(item.href);
                      setIsMobileOpen(false);
                    }}
                  >
                    {item.icon}
                    <span className="ml-2">{item.title}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Legal & Compliance */}
            <div>
              <h4 className="mb-1 px-2 text-xs font-semibold text-muted-foreground">Legal & Compliance</h4>
              <div className="space-y-1">
                {legalItems.map((item) => (
                  <Button
                    key={item.title}
                    variant={item.isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start",
                      item.isActive && "bg-muted font-medium"
                    )}
                    onClick={() => {
                      navigate(item.href);
                      setIsMobileOpen(false);
                    }}
                  >
                    {item.icon}
                    <span className="ml-2">{item.title}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Settings & Other */}
            <div>
              <h4 className="mb-1 px-2 text-xs font-semibold text-muted-foreground">Settings & Other</h4>
              <div className="space-y-1">
                {settingsItems.map((item) => (
                  <Button
                    key={item.title}
                    variant={item.isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start",
                      item.isActive && "bg-muted font-medium"
                    )}
                    onClick={() => {
                      navigate(item.href);
                      setIsMobileOpen(false);
                    }}
                  >
                    {item.icon}
                    <span className="ml-2">{item.title}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>
      </div>
    );
  };

  return (
    <div className="flex min-h-screen flex-col">
      {/* Mobile sidebar */}
      <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
        <SheetContent side="left" className="w-64 p-0 sm:max-w-xs">
          <SidebarContent />
        </SheetContent>
      </Sheet>

      <div className="flex flex-1">
        {/* Desktop sidebar */}
        <aside
          className={cn(
            "fixed hidden h-screen border-r lg:flex",
            isSidebarOpen ? "w-64" : "w-16"
          )}
        >
          {isSidebarOpen ? (
            <SidebarContent />
          ) : (
            <div className="flex h-full w-full flex-col items-center py-4">
              <Button
                variant="ghost"
                size="icon"
                className="my-2"
                onClick={() => setIsSidebarOpen(true)}
              >
                <ChevronRight className="h-4 w-4" />
                <span className="sr-only">Expand sidebar</span>
              </Button>
              <Separator className="mb-4" />
              <div className="flex flex-col items-center gap-4">
                {sidebarItems.map((item) => (
                  <Button
                    key={item.title}
                    variant={item.isActive ? "secondary" : "ghost"}
                    size="icon"
                    className={cn(
                      "h-9 w-9",
                      item.isActive && "bg-muted"
                    )}
                    onClick={() => navigate(item.href)}
                    title={item.title}
                  >
                    {item.icon}
                    <span className="sr-only">{item.title}</span>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </aside>

        <div
          className={cn(
            "flex flex-1 flex-col",
            isSidebarOpen ? "lg:pl-64" : "lg:pl-16"
          )}
        >
          <header className="sticky top-0 z-10 flex h-14 items-center gap-4 border-b bg-background px-4 sm:px-6 lg:px-8">
            <Button
              variant="outline"
              size="icon"
              className="lg:hidden"
              onClick={() => setIsMobileOpen(true)}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Open sidebar</span>
            </Button>
            <div className="flex-1">
              <h1 className="text-lg font-semibold">Admin Dashboard</h1>
            </div>
          </header>
          <main className="flex-1 p-4 sm:p-6 lg:p-8">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
