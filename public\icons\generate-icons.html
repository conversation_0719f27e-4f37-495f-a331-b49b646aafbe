<!DOCTYPE html>
<html>
<head>
    <title>Generate PWA Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .icon-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .icon-item {
            text-align: center;
        }
        button {
            padding: 10px 15px;
            background: #00A651;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Generate PWA Icons</h1>
    <p>This tool generates all the necessary icons for your PWA with the Payvoicer branding.</p>
    
    <div>
        <button onclick="generateAllIcons()">Generate All Icons</button>
        <button onclick="downloadAllIcons()">Download All Icons</button>
    </div>
    
    <div class="icon-container" id="iconContainer"></div>
    
    <script>
        // Icon sizes needed for PWA
        const iconSizes = [
            72, 96, 128, 144, 152, 192, 384, 512
        ];
        
        // Function to generate a single icon
        function generateIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#00A651'; // Ghana green
            ctx.fillRect(0, 0, size, size);
            
            // Add a gold accent
            ctx.fillStyle = '#FFD700'; // Ghana gold
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(size * 0.4, 0);
            ctx.lineTo(0, size * 0.4);
            ctx.closePath();
            ctx.fill();
            
            // Add text
            const fontSize = Math.max(size / 5, 12);
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('P', size / 2, size / 2 - fontSize / 2);
            ctx.fillText('V', size / 2, size / 2 + fontSize / 2);
            
            // Add border
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = Math.max(size / 50, 1);
            ctx.strokeRect(0, 0, size, size);
            
            return canvas;
        }
        
        // Function to generate all icons
        function generateAllIcons() {
            const container = document.getElementById('iconContainer');
            container.innerHTML = '';
            
            iconSizes.forEach(size => {
                const canvas = generateIcon(size);
                const wrapper = document.createElement('div');
                wrapper.className = 'icon-item';
                
                const label = document.createElement('p');
                label.textContent = `${size}x${size}`;
                
                wrapper.appendChild(canvas);
                wrapper.appendChild(label);
                container.appendChild(wrapper);
                
                // Save reference to download later
                canvas.setAttribute('data-size', size);
            });
        }
        
        // Function to download all icons
        function downloadAllIcons() {
            const canvases = document.querySelectorAll('canvas[data-size]');
            
            canvases.forEach(canvas => {
                const size = canvas.getAttribute('data-size');
                const link = document.createElement('a');
                link.download = `icon-${size}x${size}.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
            
            // Also generate apple touch icon
            const appleTouchIcon = generateIcon(180);
            const link = document.createElement('a');
            link.download = 'apple-touch-icon.png';
            link.href = appleTouchIcon.toDataURL('image/png');
            link.click();
            
            // Generate favicon
            const favicon = generateIcon(32);
            const faviconLink = document.createElement('a');
            faviconLink.download = 'favicon.ico';
            faviconLink.href = favicon.toDataURL('image/png');
            faviconLink.click();
        }
        
        // Generate icons on page load
        window.onload = generateAllIcons;
    </script>
</body>
</html>
