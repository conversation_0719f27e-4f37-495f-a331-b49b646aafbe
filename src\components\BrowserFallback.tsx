import React from 'react';

/**
 * A simple fallback component that renders when there are browser compatibility issues
 */
const BrowserFallback: React.FC = () => {
  // Detect browser
  const isChromeIOS = /CriOS/i.test(navigator.userAgent);
  const isIOS = /iPad|iPhone|iPod/i.test(navigator.userAgent) && !(window as any).MSStream;
  
  // Get browser-specific message
  const getBrowserMessage = () => {
    if (isIOS && isChromeIOS) {
      return (
        <>
          <p>It looks like you're using Chrome on iOS, which may have some compatibility issues with this application.</p>
          <p>For the best experience, we recommend:</p>
          <ul className="list-disc pl-6 mt-2 mb-4 text-left">
            <li>Using Safari on your iOS device</li>
            <li>Updating Chrome to the latest version</li>
            <li>Disabling content blockers or extensions</li>
          </ul>
        </>
      );
    }
    
    return (
      <p>We're having trouble loading the application in your browser. This might be due to browser compatibility issues or network problems.</p>
    );
  };
  
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-50">
      <div className="w-full max-w-md p-6 bg-white rounded-xl shadow-lg">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-ghana-green">Payvoicer</h2>
          <p className="text-gray-500">GRA VAT-Compliant Invoicing</p>
        </div>
        
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded">
          <h3 className="text-lg font-medium text-red-800 mb-2">Application Loading Issue</h3>
          {getBrowserMessage()}
        </div>
        
        <div className="space-y-4">
          <button
            onClick={() => window.location.reload()}
            className="w-full py-2 px-4 bg-ghana-green hover:bg-ghana-green/90 text-white rounded-md transition-colors"
          >
            Refresh Page
          </button>
          
          {isIOS && isChromeIOS && (
            <a
              href="https://apps.apple.com/us/app/safari/id1146562112"
              className="block w-full py-2 px-4 bg-gray-100 hover:bg-gray-200 text-center text-gray-800 rounded-md transition-colors"
            >
              Open in Safari
            </a>
          )}
          
          <div className="text-center text-sm text-gray-500 mt-4">
            <p>If the problem persists, please contact support.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrowserFallback;
