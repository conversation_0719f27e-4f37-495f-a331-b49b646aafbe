-- Create GRA credentials table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'gra_credentials') THEN
        CREATE TABLE public.gra_credentials (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
            company_tin TEXT NOT NULL,
            company_name TEXT NOT NULL,
            company_security_key TEXT NOT NULL,
            is_test BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            CONSTRAINT user_or_org_required CHECK (
                (user_id IS NOT NULL AND organization_id IS NULL) OR
                (user_id IS NULL AND organization_id IS NOT NULL)
            )
        );

        -- Add RLS policies
        ALTER TABLE public.gra_credentials ENABLE ROW LEVEL SECURITY;

        -- Policy for users to select their own credentials
        CREATE POLICY "Users can select their own credentials" ON public.gra_credentials
            FOR SELECT
            USING (auth.uid() = user_id);

        -- Policy for users to insert their own credentials
        CREATE POLICY "Users can insert their own credentials" ON public.gra_credentials
            FOR INSERT
            WITH CHECK (auth.uid() = user_id);

        -- Policy for users to update their own credentials
        CREATE POLICY "Users can update their own credentials" ON public.gra_credentials
            FOR UPDATE
            USING (auth.uid() = user_id);

        -- Policy for users to delete their own credentials
        CREATE POLICY "Users can delete their own credentials" ON public.gra_credentials
            FOR DELETE
            USING (auth.uid() = user_id);

        -- Policy for organization members to select organization credentials
        CREATE POLICY "Organization members can select organization credentials" ON public.gra_credentials
            FOR SELECT
            USING (
                EXISTS (
                    SELECT 1 FROM organization_members
                    WHERE organization_id = gra_credentials.organization_id
                    AND user_id = auth.uid()
                )
            );

        -- Policy for organization members to insert organization credentials
        CREATE POLICY "Organization members can insert organization credentials" ON public.gra_credentials
            FOR INSERT
            WITH CHECK (
                EXISTS (
                    SELECT 1 FROM organization_members
                    WHERE organization_id = NEW.organization_id
                    AND user_id = auth.uid()
                    AND role = 'admin'
                )
            );

        -- Policy for organization members to update organization credentials
        CREATE POLICY "Organization members can update organization credentials" ON public.gra_credentials
            FOR UPDATE
            USING (
                EXISTS (
                    SELECT 1 FROM organization_members
                    WHERE organization_id = gra_credentials.organization_id
                    AND user_id = auth.uid()
                    AND role = 'admin'
                )
            );

        -- Policy for organization members to delete organization credentials
        CREATE POLICY "Organization members can delete organization credentials" ON public.gra_credentials
            FOR DELETE
            USING (
                EXISTS (
                    SELECT 1 FROM organization_members
                    WHERE organization_id = gra_credentials.organization_id
                    AND user_id = auth.uid()
                    AND role = 'admin'
                )
            );

        RAISE NOTICE 'Created gra_credentials table with RLS policies';
    ELSE
        RAISE NOTICE 'gra_credentials table already exists';
    END IF;
END
$$;
