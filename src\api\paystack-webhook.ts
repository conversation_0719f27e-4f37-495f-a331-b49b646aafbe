/**
 * Paystack Webhook Handler
 * 
 * This file handles Paystack webhook events for payment notifications.
 * It should be deployed as an API endpoint that Paystack can call.
 */

import { supabase } from '@/integrations/supabase/client';
import { getPaystackCredentials } from '@/services/integrationService';
import { updateInvoiceStatus } from '@/services/databaseService';
import { createPaymentRecord } from '@/services/paymentService';
import crypto from 'crypto';

/**
 * Verify Paystack webhook signature
 * @param payload The raw request body
 * @param signature The signature from the request header
 * @param secret The webhook secret
 * @returns True if the signature is valid
 */
const verifySignature = (
  payload: string,
  signature: string,
  secret: string
): boolean => {
  const hash = crypto
    .createHmac('sha512', secret)
    .update(payload)
    .digest('hex');
  
  return hash === signature;
};

/**
 * Handle Paystack webhook event
 * @param event The webhook event
 * @param signature The signature from the request header
 * @returns Response object
 */
export const handlePaystackWebhook = async (
  event: any,
  signature: string
): Promise<{ status: number; body: string }> => {
  try {
    // Store the raw event in the database
    const { data: webhookRecord, error: webhookError } = await supabase
      .from('payment_webhooks')
      .insert({
        provider: 'paystack',
        event_type: event.event,
        event_id: event.id,
        payload: event
      })
      .select()
      .single();
    
    if (webhookError) {
      console.error('Error storing webhook event:', webhookError);
      return { status: 500, body: 'Error storing webhook event' };
    }
    
    // Only process charge.success events
    if (event.event !== 'charge.success') {
      return { status: 200, body: 'Event ignored (not charge.success)' };
    }
    
    const { data } = event;
    
    // Extract metadata
    const metadata = data.metadata || {};
    const invoiceId = metadata.invoice_id;
    
    if (!invoiceId) {
      return { status: 200, body: 'Event ignored (no invoice_id in metadata)' };
    }
    
    // Get the invoice
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();
    
    if (invoiceError) {
      console.error('Error fetching invoice:', invoiceError);
      
      // Update webhook record with error
      await supabase
        .from('payment_webhooks')
        .update({
          processed: true,
          processing_error: 'Invoice not found'
        })
        .eq('id', webhookRecord.id);
      
      return { status: 404, body: 'Invoice not found' };
    }
    
    // Get the user's Paystack credentials to verify the webhook
    const credentials = await getPaystackCredentials(invoice.user_id);
    
    if (credentials?.webhookSecret) {
      // Verify the webhook signature
      const isValid = verifySignature(
        JSON.stringify(event),
        signature,
        credentials.webhookSecret
      );
      
      if (!isValid) {
        console.error('Invalid webhook signature');
        
        // Update webhook record with error
        await supabase
          .from('payment_webhooks')
          .update({
            processed: true,
            processing_error: 'Invalid webhook signature'
          })
          .eq('id', webhookRecord.id);
        
        return { status: 401, body: 'Invalid signature' };
      }
    }
    
    // Update invoice status
    const updatedInvoice = await updateInvoiceStatus(invoiceId, 'paid');
    
    if (!updatedInvoice) {
      console.error('Error updating invoice status');
      
      // Update webhook record with error
      await supabase
        .from('payment_webhooks')
        .update({
          processed: true,
          processing_error: 'Error updating invoice status'
        })
        .eq('id', webhookRecord.id);
      
      return { status: 500, body: 'Error updating invoice status' };
    }
    
    // Create payment record
    await createPaymentRecord({
      invoice_id: invoiceId,
      amount: data.amount / 100, // Convert from kobo to GHS
      payment_method: 'paystack',
      transaction_reference: data.reference,
      status: 'successful'
    });
    
    // Update webhook record as processed
    await supabase
      .from('payment_webhooks')
      .update({
        processed: true
      })
      .eq('id', webhookRecord.id);
    
    return { status: 200, body: 'Webhook processed successfully' };
  } catch (error) {
    console.error('Error processing Paystack webhook:', error);
    return { status: 500, body: 'Internal server error' };
  }
};

/**
 * Express middleware for handling Paystack webhooks
 */
export const paystackWebhookMiddleware = (req: any, res: any) => {
  const signature = req.headers['x-paystack-signature'];
  
  if (!signature) {
    return res.status(400).send('Missing signature header');
  }
  
  const payload = req.body;
  
  // Process the webhook asynchronously
  handlePaystackWebhook(payload, signature)
    .then(({ status, body }) => {
      res.status(status).send(body);
    })
    .catch((error) => {
      console.error('Error in Paystack webhook middleware:', error);
      res.status(500).send('Internal server error');
    });
};
