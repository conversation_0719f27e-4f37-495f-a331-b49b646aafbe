-- Add invoice_settings column to user_preferences table
ALTER TABLE user_preferences 
ADD COLUMN IF NOT EXISTS invoice_settings JSONB DEFAULT '{"gra_mode_enabled": false, "default_currency": "GHS", "auto_send_reminders": true, "payment_terms_days": 14}'::jsonb;

-- Update existing rows to have the default invoice_settings
UPDATE user_preferences
SET invoice_settings = '{"gra_mode_enabled": false, "default_currency": "GHS", "auto_send_reminders": true, "payment_terms_days": 14}'::jsonb
WHERE invoice_settings IS NULL;
