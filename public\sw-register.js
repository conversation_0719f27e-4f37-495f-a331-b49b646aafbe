/**
 * Service Worker Registration Script
 * This script registers the service worker for offline functionality
 */

// Only register service worker in production or when supported
if ('serviceWorker' in navigator && (window.location.protocol === 'https:' || window.location.hostname === 'localhost')) {
  window.addEventListener('load', function() {
    // Use a try-catch to handle any errors during registration
    try {
      navigator.serviceWorker.register('/service-worker.js')
        .then(function(registration) {
          console.log('ServiceWorker registration successful with scope: ', registration.scope);
          
          // Check for updates to the Service Worker
          registration.addEventListener('updatefound', function() {
            // If updatefound is fired, it means that there's a new service worker being installed
            const installingWorker = registration.installing;
            console.log('A new service worker is being installed:', installingWorker);
            
            // You can listen for changes to the installing service worker's state
            installingWorker.addEventListener('statechange', function() {
              // If the service worker becomes "installed", notify the user if needed
              if (installingWorker.state === 'installed') {
                if (navigator.serviceWorker.controller) {
                  // At this point, the old content will have been purged and the fresh content will
                  // have been added to the cache.
                  console.log('New content is available; please refresh.');
                  
                  // Optional: Show a notification to the user
                  if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification('App Update Available', {
                      body: 'A new version of the app is available. Refresh to update.',
                      icon: '/icons/icon-192x192.png'
                    });
                  }
                } else {
                  // At this point, everything has been precached.
                  console.log('Content is cached for offline use.');
                }
              }
            });
          });
        })
        .catch(function(error) {
          console.error('Service worker registration failed:', error);
        });
        
      // Handle service worker updates
      navigator.serviceWorker.addEventListener('controllerchange', function() {
        console.log('Controller changed. Reloading content...');
        // Optional: You can reload the page automatically when the service worker is updated
        // window.location.reload();
      });
    } catch (error) {
      console.error('Error during service worker registration:', error);
    }
  });
}
