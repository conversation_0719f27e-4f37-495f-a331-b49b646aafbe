import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Check, X } from 'lucide-react';

interface PricingFeature {
  name: string;
  included: boolean;
  featureCode?: string;
  isActive?: boolean;
}

interface AnimatedPricingCardProps {
  name: string;
  description: string;
  price: number;
  billingCycle: 'monthly' | 'yearly';
  features: PricingFeature[];
  ctaText: string;
  onCtaClick: () => void;
  isPopular?: boolean;
  isCurrentPlan?: boolean;
  isLoading?: boolean;
  className?: string;
  delay?: number;
  yearlyDiscount?: number;
}

/**
 * AnimatedPricingCard - A component for displaying pricing cards with animations
 */
const AnimatedPricingCard: React.FC<AnimatedPricingCardProps> = ({
  name,
  description,
  price,
  billingCycle,
  features,
  ctaText,
  onCtaClick,
  isPopular = false,
  isCurrentPlan = false,
  isLoading = false,
  className = '',
  delay = 0,
  yearlyDiscount
}) => {
  const yearlyPrice = yearlyDiscount ? price * 12 - yearlyDiscount : price * 10; // Default to 2 months free

  return (
    <motion.div
      className={cn(
        'bg-white rounded-xl shadow-xl overflow-hidden border relative transition-all duration-500 hover:shadow-2xl hover:-translate-y-1 group',
        isPopular ? 'border-ghana-green' : 'border-gray-200',
        className
      )}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: '-50px' }}
      transition={{ 
        duration: 0.6, 
        delay,
        ease: [0.25, 0.1, 0.25, 1]
      }}
    >
      {/* Decorative top gradient bar */}
      <div className={`h-2 w-full ${isPopular ? 'bg-gradient-to-r from-ghana-green to-ghana-green-light' : 'bg-gradient-to-r from-gray-200 to-gray-300'}`}></div>

      {isPopular && (
        <motion.div 
          className="bg-ghana-green text-white text-xs font-semibold px-4 py-1.5 absolute top-4 right-4 rounded-full shadow-md"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: delay + 0.3, duration: 0.4, type: 'spring' }}
        >
          MOST POPULAR
        </motion.div>
      )}

      <div className="p-8">
        <motion.h3 
          className="text-2xl font-bold mb-2 group-hover:text-ghana-green transition-colors duration-300"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: delay + 0.1, duration: 0.4 }}
        >
          {name}
        </motion.h3>
        
        <motion.p 
          className="text-gray-600 mb-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + 0.2, duration: 0.4 }}
        >
          {description}
        </motion.p>
        
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + 0.3, duration: 0.4 }}
        >
          <span className="text-4xl font-bold">
            ₵{billingCycle === 'monthly' ? price : yearlyPrice}
          </span>
          <span className="text-gray-600">
            /{billingCycle === 'monthly' ? 'month' : 'year'}
          </span>

          {billingCycle === 'yearly' && price > 0 && (
            <motion.div 
              className="text-ghana-green text-sm mt-2 font-medium"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: delay + 0.4, duration: 0.3 }}
            >
              Save ₵{(price * 12) - yearlyPrice} per year
            </motion.div>
          )}
        </motion.div>

        <motion.button
          className={`w-full py-6 transition-all duration-300 group-hover:shadow-lg rounded-md font-medium ${
            isPopular
              ? 'bg-ghana-green hover:bg-ghana-green/90 text-white'
              : 'bg-gray-900 hover:bg-gray-800 text-white'
          }`}
          onClick={onCtaClick}
          disabled={isLoading || isCurrentPlan}
          whileHover={{ y: -3, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' }}
          whileTap={{ y: 0, boxShadow: 'none' }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + 0.5, duration: 0.4 }}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </div>
          ) : isCurrentPlan ? (
            'Current Plan'
          ) : (
            <div className="flex items-center justify-center">
              {ctaText}
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-5 w-5 ml-2 transition-transform group-hover:translate-x-1" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </div>
          )}
        </motion.button>
      </div>

      <div className="border-t border-gray-200 p-8 bg-gray-50">
        <h4 className="font-semibold mb-6 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-ghana-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          What's included:
        </h4>
        <motion.ul 
          className="space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + 0.6, duration: 0.4 }}
        >
          {features.map((feature, featureIndex) => (
            <motion.li 
              key={featureIndex} 
              className="flex items-start group/feature"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: delay + 0.6 + (featureIndex * 0.05), duration: 0.3 }}
            >
              {feature.included ? (
                <motion.div
                  whileHover={{ scale: 1.2 }}
                  transition={{ duration: 0.2 }}
                >
                  <Check className={`h-5 w-5 mr-3 flex-shrink-0 ${feature.isActive ? 'text-ghana-green animate-pulse' : 'text-ghana-green'}`} />
                </motion.div>
              ) : (
                <X className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
              )}
              <span className={`${feature.included ? 'text-gray-800' : 'text-gray-500'} ${feature.isActive ? 'font-medium' : ''} transition-colors duration-300 group-hover/feature:text-ghana-green`}>
                {feature.name}
                {feature.isActive && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    Active
                  </span>
                )}
              </span>
            </motion.li>
          ))}
        </motion.ul>
      </div>
    </motion.div>
  );
};

export default AnimatedPricingCard;
