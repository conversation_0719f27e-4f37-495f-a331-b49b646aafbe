import React, { useState, useEffect } from 'react';
import { useSession } from '@/contexts/SessionContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, CheckCircle, AlertCircle, CreditCard, Shield, Clock, ArrowRight } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { initializePaystackTransaction, verifyPaystackTransaction } from '@/services/paystackService';
import { updateUserSubscription } from '@/services/databaseService';
import { useNavigate } from 'react-router-dom';

// Subscription plan details
const SUBSCRIPTION_PLANS = {
  free: {
    name: 'Free',
    price: 'Free',
    yearlyPrice: 'Free',
    color: 'bg-gray-500',
    features: ['5 invoices per month', '3 clients', 'Basic invoicing']
  },
  freelancer: {
    name: 'Freelancer',
    price: '₵49/month',
    yearlyPrice: '₵490/year',
    color: 'bg-blue-500',
    features: ['20 invoices per month', '10 clients', 'Invoice customization']
  },
  business: {
    name: 'Business',
    price: '₵99/month',
    yearlyPrice: '₵990/year',
    color: 'bg-purple-500',
    features: ['50 invoices per month', '50 clients', 'Team members', 'Reports']
  },
  enterprise: {
    name: 'Enterprise',
    price: '₵199/month',
    yearlyPrice: '₵1990/year',
    color: 'bg-ghana-green',
    features: ['Unlimited invoices', 'Unlimited clients', 'Advanced reports', 'API access']
  }
};

// Plan prices in GHS
const PLAN_PRICES = {
  freelancer: {
    monthly: 49,
    yearly: 490
  },
  business: {
    monthly: 99,
    yearly: 990
  },
  enterprise: {
    monthly: 199,
    yearly: 1990
  }
};

const SubscriptionUpgrade: React.FC = () => {
  const { user } = useSession();
  const { subscription, tier, billingCycle, refreshSubscription } = useSubscription();
  const navigate = useNavigate();

  const [selectedPlan, setSelectedPlan] = useState<string>(tier);
  const [isYearly, setIsYearly] = useState<boolean>(billingCycle === 'yearly');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [clientIp, setClientIp] = useState<string | null>(null);

  // Get client IP for security logging
  useEffect(() => {
    const getClientIp = async () => {
      try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        setClientIp(data.ip);
      } catch (error) {
        console.error('Error getting client IP:', error);
      }
    };

    getClientIp();
  }, []);

  // Handle plan selection
  const handlePlanSelect = (plan: string) => {
    setSelectedPlan(plan);
  };

  // Handle billing cycle toggle
  const handleBillingCycleToggle = (checked: boolean) => {
    setIsYearly(checked);
  };

  // Calculate price based on selected plan and billing cycle
  const calculatePrice = (plan: string): number => {
    if (plan === 'free') return 0;

    const planPrices = PLAN_PRICES[plan as keyof typeof PLAN_PRICES];
    return isYearly ? planPrices.yearly : planPrices.monthly;
  };

  // Handle subscription upgrade
  const handleUpgrade = async () => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to upgrade your subscription.',
        variant: 'destructive'
      });
      return;
    }

    // Don't process if already processing
    if (isProcessing) return;

    // Don't process if selecting the current plan with the same billing cycle
    if (selectedPlan === tier && (isYearly ? billingCycle === 'yearly' : billingCycle === 'monthly')) {
      toast({
        title: 'Info',
        description: 'You are already subscribed to this plan.',
        variant: 'default'
      });
      return;
    }

    // Don't allow downgrading from a higher tier to a lower tier
    const tierOrder = { free: 0, freelancer: 1, business: 2, enterprise: 3 };
    if (tierOrder[selectedPlan as keyof typeof tierOrder] < tierOrder[tier as keyof typeof tierOrder]) {
      toast({
        title: 'Downgrade Not Allowed',
        description: 'Please contact support to downgrade your subscription.',
        variant: 'destructive'
      });
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    try {
      const price = calculatePrice(selectedPlan);

      // If upgrading to free plan, update subscription directly
      if (selectedPlan === 'free') {
        await updateUserSubscription(user.id, {
          plan_type: 'free',
          billing_cycle: 'monthly',
          status: 'active',
          payment_status: 'active'
        });

        await refreshSubscription();

        toast({
          title: 'Subscription Updated',
          description: 'Your subscription has been downgraded to the free plan.',
          variant: 'default'
        });

        navigate('/dashboard');
        return;
      }

      // For paid plans, initialize Paystack transaction
      const selectedBillingCycle = isYearly ? 'yearly' : 'monthly';

      // Generate a unique reference
      const reference = `SUB-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Initialize Paystack transaction
      const result = await initializePaystackTransaction({
        email: user.email,
        amount: price * 100, // Convert to kobo
        callback_url: `${window.location.origin}/subscription/callback`,
        metadata: {
          user_id: user.id,
          subscription_id: subscription?.id,
          planType: selectedPlan,
          period: selectedBillingCycle,
          previous_plan: tier,
          previous_billing_cycle: billingCycle,
          client_ip: clientIp,
          user_agent: navigator.userAgent
        }
      });

      if (!result) {
        throw new Error('Failed to initialize payment');
      }

      // Store transaction details in localStorage for verification
      localStorage.setItem('subscription_upgrade_reference', result.reference);
      localStorage.setItem('subscription_upgrade_plan', selectedPlan);
      localStorage.setItem('subscription_upgrade_billing_cycle', selectedBillingCycle);

      // Store subscription ID for verification
      if (subscription?.id) {
        localStorage.setItem('subscription_id', subscription.id);
      }

      // Payment was successful, verify the transaction
      const verificationResult = await verifyPaystackTransaction(
        result.reference,
        subscription?.id // Pass the subscription ID for secure verification
      );

      if (!verificationResult || verificationResult.status !== 'success') {
        // Double-check localStorage as a last resort
        const paystackStatus = localStorage.getItem('paystack_payment_status');
        const storedReference = localStorage.getItem('paystack_reference');

        if (paystackStatus !== 'success' || storedReference !== result.reference) {
          throw new Error('Payment verification failed');
        }

        console.warn('Using localStorage verification as last resort');
      } else {
        console.log('Payment verification successful:', verificationResult);
      }

      // Update subscription in database
      await updateUserSubscription(user.id, {
        plan_type: selectedPlan, // Use plan_type instead of tier
        billing_cycle: selectedBillingCycle,
        status: 'active',
        payment_status: 'active',
        last_payment_date: new Date().toISOString(),
        next_payment_date: new Date(
          isYearly
            ? new Date().setFullYear(new Date().getFullYear() + 1)
            : new Date().setMonth(new Date().getMonth() + 1)
        ).toISOString(),
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(
          isYearly
            ? new Date().setFullYear(new Date().getFullYear() + 1)
            : new Date().setMonth(new Date().getMonth() + 1)
        ).toISOString()
      });

      // Refresh subscription context
      await refreshSubscription();

      toast({
        title: 'Subscription Upgraded',
        description: `Your subscription has been upgraded to the ${SUBSCRIPTION_PLANS[selectedPlan as keyof typeof SUBSCRIPTION_PLANS].name} plan.`,
        variant: 'default'
      });

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      setPaymentError(error.message || 'An error occurred while processing your payment');

      toast({
        title: 'Payment Failed',
        description: error.message || 'An error occurred while processing your payment',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Upgrade Subscription</CardTitle>
        <CardDescription>Choose a plan that fits your business needs</CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Billing Cycle Toggle */}
        <div className="flex justify-center items-center space-x-4 mb-8">
          <span className={`text-sm ${!isYearly ? 'font-bold' : ''}`}>Monthly</span>
          <Switch
            checked={isYearly}
            onCheckedChange={handleBillingCycleToggle}
            id="billing-cycle"
          />
          <div className="flex items-center">
            <span className={`text-sm ${isYearly ? 'font-bold' : ''}`}>Yearly</span>
            <Badge className="ml-2 bg-ghana-green text-white">Save 17%</Badge>
          </div>
        </div>

        {/* Plan Selection */}
        <Tabs defaultValue={tier} onValueChange={handlePlanSelect} className="w-full">
          <TabsList className="grid grid-cols-4 mb-8">
            <TabsTrigger value="free">Free</TabsTrigger>
            <TabsTrigger value="freelancer">Freelancer</TabsTrigger>
            <TabsTrigger value="business">Business</TabsTrigger>
            <TabsTrigger value="enterprise">Enterprise</TabsTrigger>
          </TabsList>

          {Object.entries(SUBSCRIPTION_PLANS).map(([planId, plan]) => (
            <TabsContent key={planId} value={planId} className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold">{plan.name} Plan</h3>
                  <p className="text-gray-500">
                    {planId === 'free'
                      ? 'Free forever'
                      : isYearly
                        ? plan.yearlyPrice
                        : plan.price}
                  </p>
                </div>
                <Badge className={`${plan.color} text-white`}>
                  {planId === tier ? 'Current Plan' : ''}
                </Badge>
              </div>

              <div className="border rounded-lg p-4 bg-gray-50">
                <h4 className="font-medium mb-2">Features:</h4>
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-ghana-green flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {paymentError && (
                <div className="bg-red-50 p-4 rounded-md flex items-start gap-2 text-red-600">
                  <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium">Payment Error</p>
                    <p className="text-sm">{paymentError}</p>
                  </div>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => navigate('/dashboard')}>
          Cancel
        </Button>

        <Button
          onClick={handleUpgrade}
          disabled={isProcessing || (selectedPlan === tier && (isYearly ? billingCycle === 'yearly' : billingCycle === 'monthly'))}
          className="bg-ghana-green hover:bg-ghana-green/90"
        >
          {isProcessing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              {selectedPlan === tier && (isYearly ? billingCycle === 'yearly' : billingCycle === 'monthly')
                ? 'Current Plan'
                : selectedPlan === 'free'
                  ? 'Downgrade to Free'
                  : `Upgrade to ${SUBSCRIPTION_PLANS[selectedPlan as keyof typeof SUBSCRIPTION_PLANS].name}`}
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SubscriptionUpgrade;
