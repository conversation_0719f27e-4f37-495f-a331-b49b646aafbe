-- Add a function to check if an invoice has been paid

-- Create a function to check if an invoice has any successful payments
CREATE OR REPLACE FUNCTION public.check_invoice_has_payments(
  p_invoice_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  has_payments BOOLEAN;
BEGIN
  SELECT EXISTS(
    SELECT 1 FROM invoice_payments 
    WHERE invoice_id = p_invoice_id 
    AND status = 'successful'
  ) INTO has_payments;
  
  RETURN has_payments;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.check_invoice_has_payments(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_invoice_has_payments(UUID) TO anon;

-- Create a function to ensure an invoice is marked as paid if it has payments
CREATE OR REPLACE FUNCTION public.ensure_invoice_paid_status(
  p_invoice_id UUID
)
RETURNS SETOF invoices
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  has_payments BOOLEAN;
BEGIN
  -- Check if the invoice has payments
  SELECT EXISTS(
    SELECT 1 FROM invoice_payments 
    WHERE invoice_id = p_invoice_id 
    AND status = 'successful'
  ) INTO has_payments;
  
  -- If it has payments, ensure it's marked as paid
  IF has_payments THEN
    RETURN QUERY
    UPDATE invoices
    SET 
      status = 'paid',
      updated_at = NOW(),
      paid_at = COALESCE(paid_at, NOW())
    WHERE id = p_invoice_id
    AND status != 'paid'
    RETURNING *;
  ELSE
    -- Just return the invoice without changes
    RETURN QUERY
    SELECT * FROM invoices WHERE id = p_invoice_id;
  END IF;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.ensure_invoice_paid_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.ensure_invoice_paid_status(UUID) TO anon;
