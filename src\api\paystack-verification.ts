/**
 * Client-side Paystack verification API
 * 
 * This is a fallback mechanism for environments where the server-side API is not available.
 * It uses localStorage to verify transactions based on the data stored during the payment process.
 * 
 * NOTE: This is not as secure as server-side verification and should only be used during development
 * or as a fallback when server-side verification fails.
 */

/**
 * Verify a Paystack transaction using localStorage data
 * @param reference The transaction reference
 * @returns The verification result or null if verification failed
 */
export const verifyTransaction = (reference: string) => {
  try {
    // Get stored payment data
    const storedStatus = localStorage.getItem('paystack_payment_status');
    const storedReference = localStorage.getItem('paystack_reference');
    const selectedPlan = localStorage.getItem('subscription_upgrade_plan') || localStorage.getItem('selectedPlan');
    const selectedBillingCycle = localStorage.getItem('subscription_upgrade_billing_cycle') || localStorage.getItem('selectedBillingCycle');
    
    // Verify that the reference matches and status is success
    if (storedStatus !== 'success' || storedReference !== reference) {
      return null;
    }
    
    // Create a verification result
    return {
      status: 'success',
      data: {
        status: 'success',
        reference: reference,
        amount: 0, // We don't know the exact amount
        metadata: {
          planType: selectedPlan,
          period: selectedBillingCycle
        },
        customer: {
          email: localStorage.getItem('user_email') || '',
          customer_code: '',
          id: 0
        }
      }
    };
  } catch (error) {
    console.error('Error in client-side verification:', error);
    return null;
  }
};

/**
 * API handler for Paystack transaction verification
 * @param request The request object
 * @returns The verification result
 */
export const handleVerifyTransaction = (request: { reference: string }) => {
  const { reference } = request;
  
  if (!reference) {
    return {
      status: 'error',
      message: 'Transaction reference is required'
    };
  }
  
  const result = verifyTransaction(reference);
  
  if (!result) {
    return {
      status: 'error',
      message: 'Transaction verification failed'
    };
  }
  
  return result;
};
