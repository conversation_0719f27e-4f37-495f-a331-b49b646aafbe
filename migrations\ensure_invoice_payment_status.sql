-- Ensure invoice payment status is properly updated
-- This script creates triggers and functions to ensure invoices are marked as paid when payments are made

-- Create a function to automatically update invoice status when a payment is recorded
CREATE OR REPLACE FUNCTION public.update_invoice_status_on_payment()
RETURNS TRIGGER AS $$
BEGIN
    -- If a new payment with 'successful' status is inserted, update the invoice status to 'paid'
    IF NEW.status = 'successful' THEN
        UPDATE invoices
        SET 
            status = 'paid',
            updated_at = NOW(),
            paid_at = COALESCE(paid_at, NOW())
        WHERE id = NEW.invoice_id AND status != 'paid';
        
        RAISE NOTICE 'Updated invoice % status to paid based on payment record', NEW.invoice_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update invoice status when a payment is recorded
DROP TRIGGER IF EXISTS trigger_update_invoice_status_on_payment ON invoice_payments;
CREATE TRIGGER trigger_update_invoice_status_on_payment
AFTER INSERT OR UPDATE ON invoice_payments
FOR EACH ROW
EXECUTE FUNCTION update_invoice_status_on_payment();

-- Create a function to check and update invoice payment status
CREATE OR REPLACE FUNCTION public.check_and_update_invoice_payment_status(
    p_invoice_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    has_successful_payment BOOLEAN;
    invoice_updated BOOLEAN := FALSE;
BEGIN
    -- Check if the invoice has any successful payments
    SELECT EXISTS (
        SELECT 1 FROM invoice_payments 
        WHERE invoice_id = p_invoice_id AND status = 'successful'
    ) INTO has_successful_payment;
    
    -- If it has successful payments but is not marked as paid, update it
    IF has_successful_payment THEN
        UPDATE invoices
        SET 
            status = 'paid',
            updated_at = NOW(),
            paid_at = COALESCE(paid_at, NOW())
        WHERE id = p_invoice_id AND status != 'paid'
        RETURNING TRUE INTO invoice_updated;
        
        IF invoice_updated THEN
            RAISE NOTICE 'Updated invoice % status to paid', p_invoice_id;
        END IF;
    END IF;
    
    -- Return whether the invoice has successful payments
    RETURN has_successful_payment;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.check_and_update_invoice_payment_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_and_update_invoice_payment_status(UUID) TO anon;

-- Create a function to check and update all invoices with payments
CREATE OR REPLACE FUNCTION public.check_and_update_all_invoice_payment_statuses()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    invoice_record RECORD;
    updated_count INTEGER := 0;
BEGIN
    -- Find all invoices that have successful payments but aren't marked as paid
    FOR invoice_record IN 
        SELECT DISTINCT i.id
        FROM invoices i
        JOIN invoice_payments p ON i.id = p.invoice_id
        WHERE p.status = 'successful' AND i.status != 'paid'
    LOOP
        -- Update the invoice status to paid
        UPDATE invoices
        SET 
            status = 'paid',
            updated_at = NOW(),
            paid_at = COALESCE(paid_at, NOW())
        WHERE id = invoice_record.id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RAISE NOTICE 'Updated % invoices to paid status', updated_count;
    RETURN updated_count;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.check_and_update_all_invoice_payment_statuses() TO authenticated;

-- Run the function to fix existing invoices
SELECT public.check_and_update_all_invoice_payment_statuses();
