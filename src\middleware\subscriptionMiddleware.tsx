import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSession } from '@/contexts/SessionContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { toast } from '@/hooks/use-toast';
import { SubscriptionTier } from '@/contexts/SubscriptionContext';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  requiredTier: SubscriptionTier;
  requiredFeature?: string;
  fallbackPath?: string;
}

/**
 * SubscriptionGuard component
 * 
 * This component protects routes and features based on subscription tier.
 * It checks if the user has access to the required tier or feature.
 * If not, it redirects to the fallback path or shows an error message.
 * 
 * @param children The protected content
 * @param requiredTier The minimum subscription tier required
 * @param requiredFeature Optional specific feature required
 * @param fallbackPath Path to redirect to if access is denied
 */
export const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  children,
  requiredTier,
  requiredFeature,
  fallbackPath = '/subscription'
}) => {
  const { user } = useSession();
  const { canAccessTier, canAccessFeature, isLoading } = useSubscription();
  const navigate = useNavigate();
  
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [isChecking, setIsChecking] = useState<boolean>(true);
  
  useEffect(() => {
    const checkAccess = async () => {
      if (!user) {
        setHasAccess(false);
        setIsChecking(false);
        return;
      }
      
      if (isLoading) {
        setIsChecking(true);
        return;
      }
      
      let tierAccess = canAccessTier(requiredTier);
      let featureAccess = true;
      
      if (requiredFeature) {
        featureAccess = await canAccessFeature(requiredFeature);
      }
      
      const hasFullAccess = tierAccess && featureAccess;
      setHasAccess(hasFullAccess);
      setIsChecking(false);
      
      if (!hasFullAccess) {
        toast({
          title: 'Subscription Required',
          description: `You need a ${requiredTier} subscription or higher to access this feature.`,
          variant: 'destructive'
        });
        
        navigate(fallbackPath);
      }
    };
    
    checkAccess();
  }, [user, isLoading, requiredTier, requiredFeature, canAccessTier, canAccessFeature, navigate, fallbackPath]);
  
  if (isChecking) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-ghana-green"></div>
      </div>
    );
  }
  
  return hasAccess ? <>{children}</> : null;
};

/**
 * withSubscriptionGuard HOC
 * 
 * Higher-order component that wraps a component with SubscriptionGuard.
 * 
 * @param Component The component to protect
 * @param requiredTier The minimum subscription tier required
 * @param requiredFeature Optional specific feature required
 * @param fallbackPath Path to redirect to if access is denied
 */
export const withSubscriptionGuard = <P extends object>(
  Component: React.ComponentType<P>,
  requiredTier: SubscriptionTier,
  requiredFeature?: string,
  fallbackPath?: string
) => {
  return (props: P) => (
    <SubscriptionGuard
      requiredTier={requiredTier}
      requiredFeature={requiredFeature}
      fallbackPath={fallbackPath}
    >
      <Component {...props} />
    </SubscriptionGuard>
  );
};

/**
 * useSubscriptionGuard hook
 * 
 * Custom hook to check if user has access to a specific tier or feature.
 * Returns a function to check access and a loading state.
 */
export const useSubscriptionGuard = () => {
  const { user } = useSession();
  const { canAccessTier, canAccessFeature, isLoading } = useSubscription();
  const navigate = useNavigate();
  
  const checkAccess = async (
    requiredTier: SubscriptionTier,
    requiredFeature?: string,
    fallbackPath: string = '/subscription'
  ): Promise<boolean> => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'You need to be logged in to access this feature.',
        variant: 'destructive'
      });
      
      navigate('/login');
      return false;
    }
    
    if (isLoading) {
      return false;
    }
    
    let tierAccess = canAccessTier(requiredTier);
    let featureAccess = true;
    
    if (requiredFeature) {
      featureAccess = await canAccessFeature(requiredFeature);
    }
    
    const hasAccess = tierAccess && featureAccess;
    
    if (!hasAccess) {
      toast({
        title: 'Subscription Required',
        description: `You need a ${requiredTier} subscription or higher to access this feature.`,
        variant: 'destructive'
      });
      
      navigate(fallbackPath);
    }
    
    return hasAccess;
  };
  
  return {
    checkAccess,
    isChecking: isLoading
  };
};

export default SubscriptionGuard;
