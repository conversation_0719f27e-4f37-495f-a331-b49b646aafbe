-- Complete RLS Reset
-- This script completely resets all RLS policies and triggers on the invoices table
-- and creates a minimal set of secure policies

-- 1. Temporarily disable RLS
ALTER TABLE invoices DISABLE ROW LEVEL SECURITY;

-- 2. Drop all triggers on the invoices table
DO $$
DECLARE
    trigger_record RECORD;
BEGIN
    FOR trigger_record IN
        SELECT tgname
        FROM pg_trigger
        WHERE tgrelid = 'invoices'::regclass
        AND tgname != 'enforce_invoice_update_security_trigger' -- Keep our security trigger
    LOOP
        EXECUTE 'DROP TRIGGER IF EXISTS ' || trigger_record.tgname || ' ON invoices';
        RAISE NOTICE 'Dropped trigger: %', trigger_record.tgname;
    END LOOP;
END $$;

-- 3. Drop all policies on the invoices table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN
        SELECT policyname
        FROM pg_policies
        WHERE tablename = 'invoices'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON invoices';
        RAISE NOTICE 'Dropped policy: %', policy_record.policyname;
    END LOOP;
END $$;

-- 4. Create a minimal set of secure policies

-- Policy for SELECT operations
CREATE POLICY "invoices_select" ON invoices
FOR SELECT
USING (
    -- User owns the invoice
    auth.uid() = user_id
    OR
    -- User is part of the organization that owns the invoice
    EXISTS (
        SELECT 1 FROM organization_members
        WHERE organization_id = invoices.organization_id
        AND user_id = auth.uid()
    )
    OR
    -- Invoice has a valid public access token
    (
        public_access_token IS NOT NULL
        AND (
            public_access_expires_at IS NULL
            OR public_access_expires_at > NOW()
        )
    )
);

-- Policy for INSERT operations
CREATE POLICY "invoices_insert" ON invoices
FOR INSERT
WITH CHECK (
    auth.uid() = user_id
);

-- Policy for UPDATE operations - NO WITH CHECK clause to avoid the error
CREATE POLICY "invoices_update" ON invoices
FOR UPDATE
USING (
    -- User owns the invoice
    auth.uid() = user_id
    OR
    -- User is part of the organization that owns the invoice
    EXISTS (
        SELECT 1 FROM organization_members
        WHERE organization_id = invoices.organization_id
        AND user_id = auth.uid()
    )
    OR
    -- Invoice has a valid public access token
    (
        public_access_token IS NOT NULL
        AND (
            public_access_expires_at IS NULL
            OR public_access_expires_at > NOW()
        )
    )
);

-- Policy for DELETE operations
CREATE POLICY "invoices_delete" ON invoices
FOR DELETE
USING (
    auth.uid() = user_id
);

-- 5. Re-enable RLS
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
