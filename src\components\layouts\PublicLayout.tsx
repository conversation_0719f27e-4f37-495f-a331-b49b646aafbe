import React from 'react';
import { Link } from 'react-router-dom';

interface PublicLayoutProps {
  children: React.ReactNode;
}

const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Simple Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Link to="/" className="flex items-center gap-2">
            <img src="/logo.png" alt="Payvoicer Logo" className="h-8 dark:invert" />
            <span className="font-bold text-xl text-ghana-green dark:text-ghana-gold">Payvoicer</span>
          </Link>

          <div className="flex items-center gap-4">
            <Link
              to="/auth"
              className="text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-ghana-green dark:hover:text-ghana-gold transition-colors"
            >
              Sign In
            </Link>
            <Link
              to="/auth?tab=register"
              className="bg-ghana-green hover:bg-ghana-green/90 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Create Account
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow">
        {children}
      </main>

      {/* Simple Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-center md:text-left">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                &copy; {new Date().getFullYear()} Payvoicer. All rights reserved.
              </p>
            </div>

            <div className="flex items-center gap-6">
              <Link
                to="/privacy-policy"
                className="text-sm text-gray-500 dark:text-gray-400 hover:text-ghana-green dark:hover:text-ghana-gold transition-colors"
              >
                Privacy Policy
              </Link>
              <Link
                to="/terms"
                className="text-sm text-gray-500 dark:text-gray-400 hover:text-ghana-green dark:hover:text-ghana-gold transition-colors"
              >
                Terms of Service
              </Link>
              <Link
                to="/contact"
                className="text-sm text-gray-500 dark:text-gray-400 hover:text-ghana-green dark:hover:text-ghana-gold transition-colors"
              >
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicLayout;
