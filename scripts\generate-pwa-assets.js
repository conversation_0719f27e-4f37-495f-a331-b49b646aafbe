/**
 * PWA Asset Generator Script
 * 
 * This script generates all the necessary PWA assets from the logo.
 * It creates:
 * - Standard icons in various sizes
 * - Maskable icons (with padding for rounded corners)
 * - Monochrome icons (for dark mode)
 * - Splash screens for iOS
 * 
 * To use this script:
 * 1. Install Sharp: npm install sharp
 * 2. Place your logo.png in the public folder
 * 3. Run: node scripts/generate-pwa-assets.js
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Configuration
const SOURCE_LOGO = path.join(__dirname, '../public/logo.png');
const OUTPUT_DIR = path.join(__dirname, '../public');
const SCREENSHOTS_DIR = path.join(OUTPUT_DIR, 'screenshots');
const ICON_SIZES = [72, 96, 128, 144, 152, 192, 384, 512];
const SPLASH_SCREENS = [
  { width: 640, height: 1136, name: 'splash-640x1136.png' }, // iPhone SE
  { width: 750, height: 1334, name: 'splash-750x1334.png' }, // iPhone 8
  { width: 828, height: 1792, name: 'splash-828x1792.png' }, // iPhone XR
  { width: 1125, height: 2436, name: 'splash-1125x2436.png' }, // iPhone X/XS
  { width: 1242, height: 2688, name: 'splash-1242x2688.png' }, // iPhone XS Max
  { width: 1536, height: 2048, name: 'splash-1536x2048.png' }, // iPad
  { width: 1668, height: 2388, name: 'splash-1668x2388.png' }, // iPad Pro 11"
  { width: 2048, height: 2732, name: 'splash-2048x2732.png' }, // iPad Pro 12.9"
];

// Create directories if they don't exist
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

if (!fs.existsSync(SCREENSHOTS_DIR)) {
  fs.mkdirSync(SCREENSHOTS_DIR, { recursive: true });
}

// Generate standard icons
async function generateStandardIcons() {
  console.log('Generating standard icons...');
  
  // Create a copy of the logo as the main icon
  await sharp(SOURCE_LOGO)
    .resize(512, 512)
    .toFile(path.join(OUTPUT_DIR, 'logo.png'));
  
  // Generate icons in different sizes
  for (const size of ICON_SIZES) {
    await sharp(SOURCE_LOGO)
      .resize(size, size)
      .toFile(path.join(OUTPUT_DIR, `icon-${size}x${size}.png`));
    
    console.log(`Generated icon-${size}x${size}.png`);
  }
}

// Generate maskable icons (with padding for safe area)
async function generateMaskableIcons() {
  console.log('Generating maskable icons...');
  
  // Create a maskable icon with padding (10% on each side)
  const logoBuffer = await sharp(SOURCE_LOGO)
    .resize(Math.round(512 * 0.8), Math.round(512 * 0.8))
    .toBuffer();
  
  // Create a background and place the logo in the center
  await sharp({
    create: {
      width: 512,
      height: 512,
      channels: 4,
      background: { r: 0, g: 166, b: 81, alpha: 1 } // Ghana green
    }
  })
    .composite([{
      input: logoBuffer,
      gravity: 'center'
    }])
    .toFile(path.join(OUTPUT_DIR, 'logo-maskable.png'));
  
  console.log('Generated logo-maskable.png');
}

// Generate monochrome icons (for dark mode)
async function generateMonochromeIcons() {
  console.log('Generating monochrome icons...');
  
  // Create a monochrome version of the logo
  await sharp(SOURCE_LOGO)
    .grayscale()
    .toFile(path.join(OUTPUT_DIR, 'logo-monochrome.png'));
  
  console.log('Generated logo-monochrome.png');
}

// Generate splash screens for iOS
async function generateSplashScreens() {
  console.log('Generating splash screens...');
  
  // Resize the logo to 30% of the smallest dimension for splash screens
  const logoBuffer = await sharp(SOURCE_LOGO)
    .resize(Math.round(640 * 0.3), Math.round(640 * 0.3))
    .toBuffer();
  
  for (const screen of SPLASH_SCREENS) {
    // Create a background with the logo in the center
    await sharp({
      create: {
        width: screen.width,
        height: screen.height,
        channels: 4,
        background: { r: 0, g: 166, b: 81, alpha: 1 } // Ghana green
      }
    })
      .composite([{
        input: logoBuffer,
        gravity: 'center'
      }])
      .toFile(path.join(OUTPUT_DIR, screen.name));
    
    console.log(`Generated ${screen.name}`);
  }
}

// Generate placeholder screenshots
async function generatePlaceholderScreenshots() {
  console.log('Generating placeholder screenshots...');
  
  // Desktop screenshots (1280x720)
  await sharp({
    create: {
      width: 1280,
      height: 720,
      channels: 4,
      background: { r: 255, g: 255, b: 255, alpha: 1 }
    }
  })
    .composite([{
      input: Buffer.from('<svg width="1280" height="720"><text x="640" y="360" font-family="Arial" font-size="40" text-anchor="middle" fill="#333">Dashboard Screenshot</text></svg>'),
      gravity: 'center'
    }])
    .toFile(path.join(SCREENSHOTS_DIR, 'dashboard.png'));
  
  await sharp({
    create: {
      width: 1280,
      height: 720,
      channels: 4,
      background: { r: 255, g: 255, b: 255, alpha: 1 }
    }
  })
    .composite([{
      input: Buffer.from('<svg width="1280" height="720"><text x="640" y="360" font-family="Arial" font-size="40" text-anchor="middle" fill="#333">Invoice Creation Screenshot</text></svg>'),
      gravity: 'center'
    }])
    .toFile(path.join(SCREENSHOTS_DIR, 'invoice.png'));
  
  // Mobile screenshots (720x1280)
  await sharp({
    create: {
      width: 720,
      height: 1280,
      channels: 4,
      background: { r: 255, g: 255, b: 255, alpha: 1 }
    }
  })
    .composite([{
      input: Buffer.from('<svg width="720" height="1280"><text x="360" y="640" font-family="Arial" font-size="40" text-anchor="middle" fill="#333">Mobile Dashboard Screenshot</text></svg>'),
      gravity: 'center'
    }])
    .toFile(path.join(SCREENSHOTS_DIR, 'mobile-dashboard.png'));
  
  await sharp({
    create: {
      width: 720,
      height: 1280,
      channels: 4,
      background: { r: 255, g: 255, b: 255, alpha: 1 }
    }
  })
    .composite([{
      input: Buffer.from('<svg width="720" height="1280"><text x="360" y="640" font-family="Arial" font-size="40" text-anchor="middle" fill="#333">Mobile Invoice Screenshot</text></svg>'),
      gravity: 'center'
    }])
    .toFile(path.join(SCREENSHOTS_DIR, 'mobile-invoice.png'));
  
  console.log('Generated placeholder screenshots');
}

// Run all generation functions
async function generateAll() {
  try {
    await generateStandardIcons();
    await generateMaskableIcons();
    await generateMonochromeIcons();
    await generateSplashScreens();
    await generatePlaceholderScreenshots();
    
    console.log('All PWA assets generated successfully!');
    console.log('Note: Replace placeholder screenshots with actual app screenshots for a better user experience.');
  } catch (error) {
    console.error('Error generating PWA assets:', error);
  }
}

generateAll();
