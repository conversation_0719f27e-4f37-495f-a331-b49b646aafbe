import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useSession } from './SessionContext';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: 'light' | 'dark';
  systemTheme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextType>({
  theme: 'system',
  setTheme: () => null,
  resolvedTheme: 'light',
  systemTheme: 'light',
});

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useSession();
  const [theme, setThemeState] = useState<Theme>('system');
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');
  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');
  const [isInitialized, setIsInitialized] = useState(false);

  // Detect system preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    // Set initial value
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');

    // Listen for changes
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Load user preference from database or localStorage
  useEffect(() => {
    const loadThemePreference = async () => {
      let savedTheme: Theme = 'system';

      // Try to get from localStorage first for immediate UI update
      const localTheme = localStorage.getItem('theme') as Theme | null;
      if (localTheme) {
        savedTheme = localTheme;
      }

      // If user is logged in, try to get from database
      if (user) {
        try {
          const { data, error } = await supabase
            .from('user_preferences')
            .select('theme')
            .eq('user_id', user.id)
            .single();

          if (!error && data && data.theme) {
            savedTheme = data.theme as Theme;
            // Update localStorage with the database value
            localStorage.setItem('theme', savedTheme);
          } else if (localTheme) {
            // If we have a local theme but not in DB, save it to DB
            await supabase
              .from('user_preferences')
              .upsert({ user_id: user.id, theme: localTheme });
          }
        } catch (error) {
          console.error('Error loading theme preference:', error);
        }
      }

      setThemeState(savedTheme);
      setIsInitialized(true);
    };

    loadThemePreference();
  }, [user]);

  // Update the resolved theme whenever theme or systemTheme changes
  useEffect(() => {
    if (theme === 'system') {
      setResolvedTheme(systemTheme);
    } else {
      setResolvedTheme(theme);
    }
  }, [theme, systemTheme]);

  // Apply theme to document
  useEffect(() => {
    if (!isInitialized) return;

    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(resolvedTheme);
  }, [resolvedTheme, isInitialized]);

  // Function to set theme
  const setTheme = async (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem('theme', newTheme);

    // If user is logged in, save to database
    if (user) {
      try {
        await supabase
          .from('user_preferences')
          .upsert({ user_id: user.id, theme: newTheme });
      } catch (error) {
        console.error('Error saving theme preference:', error);
      }
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme, resolvedTheme, systemTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
