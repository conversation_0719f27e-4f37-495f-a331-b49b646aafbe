/**
 * Browser Compatibility Script for Payvoicer
 *
 * This script runs before the main application to:
 * 1. Detect browser compatibility issues
 * 2. Apply polyfills and fixes for specific browsers
 * 3. Handle errors that might prevent the app from loading
 */

(function() {
  // Show a basic error page if something goes wrong
  function showErrorPage(message) {
    document.body.innerHTML = `
      <div style="font-family: system-ui, -apple-system, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center;">
        <h2 style="color: #00A651;">Payvoicer</h2>
        <div style="background: #f8f8f8; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h3 style="color: #333;">We're having trouble loading the application</h3>
          <p style="color: #666;">${message}</p>
          <button onclick="window.location.reload()" style="background: #00A651; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 15px;">
            Try Again
          </button>
        </div>
      </div>
    `;
  }

  try {
    // Detect browser
    var ua = navigator.userAgent || '';
    var isCriOS = /CriOS/i.test(ua); // Chrome on iOS
    var isIOS = /iPad|iPhone|iPod/i.test(ua) && !(window.MSStream);

    // Apply fixes for Chrome on iOS
    if (isIOS && isCriOS) {
      // Fix for blank page in Chrome on iOS

      // 1. Load special CSS fixes for Chrome on iOS
      var link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = '/chrome-ios-fixes.css';
      link.id = 'chrome-ios-fixes';
      document.head.appendChild(link);

      // 2. Add inline critical CSS as a fallback
      var style = document.createElement('style');
      style.textContent = `
        :root {
          --ghana-green: #00A651 !important;
          --ghana-gold: #FFD700 !important;
        }

        /* Force hardware acceleration */
        body, html {
          -webkit-transform: translateZ(0);
          -moz-transform: translateZ(0);
          -ms-transform: translateZ(0);
          -o-transform: translateZ(0);
          transform: translateZ(0);
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
        }
      `;
      document.head.appendChild(style);

      // 2. Add meta tags to improve rendering
      var meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no';
      document.head.appendChild(meta);

      // 3. Polyfill for some missing features in Chrome iOS
      if (!window.matchMedia) {
        window.matchMedia = function(query) {
          return {
            matches: false,
            media: query,
            onchange: null,
            addListener: function() {},
            removeListener: function() {},
            addEventListener: function() {},
            removeEventListener: function() {},
            dispatchEvent: function() { return true; }
          };
        };
      }

      // 4. Fix for animation issues
      if (window.requestAnimationFrame) {
        var originalRAF = window.requestAnimationFrame;
        window.requestAnimationFrame = function(callback) {
          return originalRAF(function(timestamp) {
            // Wrap in setTimeout to avoid some Chrome iOS bugs
            setTimeout(function() {
              callback(timestamp);
            }, 0);
          });
        };
      }

      console.log('Applied Chrome iOS compatibility fixes');
    }

    // Add a global error handler
    window.addEventListener('error', function(event) {
      console.error('Global error caught:', event.error);
      // Only show error page for critical errors
      if (document.body.innerHTML === '') {
        showErrorPage('An error occurred while loading the application. Please try refreshing the page.');
      }
      // Prevent the error from bubbling up
      event.preventDefault();
    });

    // Check if the app fails to load within 10 seconds
    var appLoadTimeout = setTimeout(function() {
      if (document.getElementById('root') &&
          (!document.getElementById('root').children ||
           document.getElementById('root').children.length === 0)) {
        showErrorPage('The application is taking too long to load. This might be due to a slow connection or a browser compatibility issue.');
      }
    }, 10000);

    // Clear the timeout when the app loads successfully
    window.addEventListener('load', function() {
      clearTimeout(appLoadTimeout);
    });

  } catch (e) {
    console.error('Error in browser compatibility script:', e);
    showErrorPage('We encountered an issue preparing the application for your browser. Please try refreshing the page.');
  }
})();
