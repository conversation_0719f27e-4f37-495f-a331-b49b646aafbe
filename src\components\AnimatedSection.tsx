import React, { ReactNode, useEffect, useState } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

interface AnimatedSectionProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  duration?: number;
  threshold?: number;
  triggerOnce?: boolean;
}

/**
 * AnimatedSection - A component that animates its children when they come into view
 * Perfect for landing page sections that should animate as the user scrolls
 */
const AnimatedSection: React.FC<AnimatedSectionProps> = ({
  children,
  className = '',
  delay = 0,
  direction = 'up',
  duration = 0.6,
  threshold = 0.1,
  triggerOnce = true
}) => {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    threshold,
    triggerOnce
  });

  // Get initial and animate states based on direction
  const getVariants = () => {
    let initial = { opacity: 0 };
    
    switch (direction) {
      case 'up':
        initial = { ...initial, y: 50 };
        break;
      case 'down':
        initial = { ...initial, y: -50 };
        break;
      case 'left':
        initial = { ...initial, x: -50 };
        break;
      case 'right':
        initial = { ...initial, x: 50 };
        break;
      case 'none':
      default:
        break;
    }

    return {
      hidden: initial,
      visible: {
        opacity: 1,
        y: 0,
        x: 0,
        transition: {
          duration,
          delay,
          ease: [0.25, 0.1, 0.25, 1] // cubic-bezier easing
        }
      }
    };
  };

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={getVariants()}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedSection;
