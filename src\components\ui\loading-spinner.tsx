import React from 'react';
import { Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'default' | 'primary' | 'secondary' | 'ghana-green';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

/**
 * LoadingSpinner - A customizable loading spinner component
 */
export function LoadingSpinner({
  size = 'md',
  color = 'default',
  text,
  fullScreen = false,
  className
}: LoadingSpinnerProps) {
  // Size mappings
  const sizeMap = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  // Color mappings
  const colorMap = {
    default: 'text-gray-500',
    primary: 'text-primary',
    secondary: 'text-secondary',
    'ghana-green': 'text-ghana-green'
  };

  // Container variants
  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: { duration: 0.3 }
    }
  };

  // Spinner variants
  const spinnerVariants = {
    animate: {
      rotate: 360,
      transition: {
        repeat: Infinity,
        duration: 1,
        ease: "linear"
      }
    }
  };

  // Text variants
  const textVariants = {
    initial: { opacity: 0, y: 5 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { 
        delay: 0.2,
        duration: 0.3
      }
    }
  };

  return (
    <motion.div
      className={cn(
        'flex flex-col items-center justify-center gap-3',
        fullScreen && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50',
        className
      )}
      initial="initial"
      animate="animate"
      variants={containerVariants}
    >
      <motion.div
        variants={spinnerVariants}
        animate="animate"
      >
        <Loader2 className={cn(sizeMap[size], colorMap[color])} />
      </motion.div>
      
      {text && (
        <motion.p
          className="text-sm font-medium"
          variants={textVariants}
        >
          {text}
        </motion.p>
      )}
    </motion.div>
  );
}

export default LoadingSpinner;
