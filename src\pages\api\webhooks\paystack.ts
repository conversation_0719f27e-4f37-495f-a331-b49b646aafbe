import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { verifyWebhookSignature, handleWebhookEvent } from '@/services/serverPaystackService';
import { PAYSTACK_WEBHOOK_SECRET } from '@/config/env';

/**
 * Paystack webhook handler
 *
 * This endpoint handles webhook events from Paystack for both:
 * 1. Platform-level subscription management
 * 2. User-provided Paystack integrations for invoice payments
 *
 * It verifies the webhook signature to ensure the request is from Paystack.
 *
 * Events handled:
 * - charge.success: Payment successful
 * - subscription.create: Subscription created
 * - subscription.disable: Subscription cancelled
 * - invoice.payment_failed: Payment failed
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Create a Supabase client with admin privileges
    const supabase = createServerSupabaseClient({ req, res });

    // Get the signature from the headers
    const signature = req.headers['x-paystack-signature'] as string;

    if (!signature) {
      return res.status(400).json({ error: 'Missing Paystack signature' });
    }

    // Get the raw request body
    const rawBody = JSON.stringify(req.body);

    // Extract the reference from the event
    const reference = req.body.data?.reference;

    if (!reference) {
      return res.status(400).json({ error: 'No reference found in event data' });
    }

    // Check if this is an invoice payment
    let webhookSecret = PAYSTACK_WEBHOOK_SECRET;

    if (reference.startsWith('INV-')) {
      // Extract the invoice ID from the reference (format: INV-{invoiceId}-{timestamp})
      const invoiceIdMatch = reference.match(/INV-([a-f0-9-]+)-\d+/);

      if (invoiceIdMatch && invoiceIdMatch[1]) {
        const invoiceId = invoiceIdMatch[1];

        // Get the invoice
        const { data: invoice, error: invoiceError } = await supabase
          .from('invoices')
          .select('user_id')
          .eq('id', invoiceId)
          .single();

        if (!invoiceError && invoice) {
          // Get the user's Paystack integration
          const { data: integration, error: integrationError } = await supabase
            .from('paystack_integrations')
            .select('webhook_secret')
            .eq('user_id', invoice.user_id)
            .single();

          if (!integrationError && integration && integration.webhook_secret) {
            // Use the user's webhook secret
            webhookSecret = integration.webhook_secret;
          }
        }
      }
    }

    // Verify the signature
    const isValid = verifyWebhookSignature(signature, rawBody, webhookSecret);

    if (!isValid) {
      console.error('Invalid Paystack webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    // Handle the webhook event
    const result = await handleWebhookEvent(req.body);

    if (!result.success) {
      console.error('Error handling Paystack webhook:', result.error);
      return res.status(500).json({ error: result.error });
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error processing Paystack webhook:', error);
    return res.status(500).json({ error: 'An error occurred while processing webhook' });
  }
}
