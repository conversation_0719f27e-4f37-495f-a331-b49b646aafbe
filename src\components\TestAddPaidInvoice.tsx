import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

const TestAddPaidInvoice: React.FC = () => {
  const [invoiceId, setInvoiceId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleAddTestInvoice = async () => {
    if (!invoiceId) {
      toast({
        title: 'Error',
        description: 'Please enter an invoice ID',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      // Get current user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) {
        throw new Error('Failed to get current user: ' + userError.message);
      }

      if (!userData.user?.email) {
        throw new Error('User email not found');
      }

      // Check if invoice exists
      const { data: invoiceData, error: invoiceError } = await supabase
        .from('invoices')
        .select('id, invoice_number')
        .eq('id', invoiceId)
        .single();

      if (invoiceError) {
        throw new Error('Invoice not found: ' + invoiceError.message);
      }

      // Add test paid invoice
      const { data, error } = await supabase
        .from('client_paid_invoices')
        .upsert({
          email: userData.user.email,
          invoice_id: invoiceId,
          payment_reference: 'TEST-' + Date.now(),
          payment_date: new Date().toISOString(),
          claimed: true,
          claimed_by_user_id: userData.user.id,
          claimed_at: new Date().toISOString()
        }, {
          onConflict: 'email,invoice_id',
          ignoreDuplicates: false
        })
        .select();

      if (error) {
        throw new Error('Failed to add test invoice: ' + error.message);
      }

      setResult({
        success: true,
        message: `Successfully added test paid invoice for Invoice #${invoiceData.invoice_number}`
      });

      toast({
        title: 'Success',
        description: `Test paid invoice added for Invoice #${invoiceData.invoice_number}`,
        variant: 'default'
      });
    } catch (error: any) {
      console.error('Error adding test paid invoice:', error);
      setResult({
        success: false,
        message: error.message
      });

      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Add Test Paid Invoice</CardTitle>
        <CardDescription>
          This is a testing tool to add a paid invoice to your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="invoiceId" className="text-sm font-medium">
              Invoice ID
            </label>
            <Input
              id="invoiceId"
              value={invoiceId}
              onChange={(e) => setInvoiceId(e.target.value)}
              placeholder="Enter invoice UUID"
            />
            <p className="text-xs text-gray-500">
              Enter the UUID of an existing invoice to add it as a paid invoice
            </p>
          </div>

          {result && (
            <div className={`p-3 rounded-md ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
              <div className="flex items-start gap-2">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                )}
                <p className={result.success ? 'text-green-700' : 'text-red-700'}>
                  {result.message}
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleAddTestInvoice}
          disabled={isLoading || !invoiceId}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : (
            'Add Test Paid Invoice'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TestAddPaidInvoice;
