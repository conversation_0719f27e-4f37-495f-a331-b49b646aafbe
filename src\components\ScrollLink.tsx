import React from 'react';
import { Link, LinkProps } from 'react-router-dom';

/**
 * ScrollLink component that scrolls the window to the top when clicked
 * This is a wrapper around the Link component from react-router-dom
 */
const ScrollLink: React.FC<LinkProps & { children: React.ReactNode }> = ({ 
  children, 
  to, 
  ...props 
}) => {
  const handleClick = () => {
    // Scroll to top with smooth behavior
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  };

  return (
    <Link to={to} onClick={handleClick} {...props}>
      {children}
    </Link>
  );
};

export default ScrollLink;
