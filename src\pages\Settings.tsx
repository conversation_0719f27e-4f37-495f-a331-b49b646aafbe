import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';
import {
  AlertTriangle,
  Bell,
  Check,
  CreditCard,
  FileText,
  Globe,
  Key,
  Laptop,
  LayoutDashboard,
  Loader2,
  Lock,
  Moon,
  Save,
  Sun,
  User,
  CreditCard as PaymentIcon
} from 'lucide-react';

// Custom Components
import PaystackIntegration from '@/components/settings/PaystackIntegration';
import InvoiceSettings from '@/pages/settings/InvoiceSettings';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Layout Components
import DashboardLayout from '@/components/layouts/DashboardLayout';

// Services
import { useSession } from '@/contexts/SessionContext';
import { useTheme } from '@/contexts/ThemeContext';
import {
  getUserPreferences,
  updateEmailNotificationPreferences,
  updateDashboardWidgetPreferences,
  updateThemePreference,
  updateLanguagePreference,
  updateTimezonePreference,
  DEFAULT_EMAIL_NOTIFICATIONS,
  DEFAULT_DASHBOARD_WIDGETS
} from '@/services/preferencesService';
import {
  getGraCredentials,
  saveGraCredentials
} from '@/services/databaseService';
import {
  getUserSubscription,
  getOrganizationSubscription,
  SUBSCRIPTION_PLANS,
  initializeSubscriptionUpgrade
} from '@/services/subscriptionService';
import { Tables } from '@/integrations/supabase/types';

// Form validation schemas
const graCredentialsSchema = z.object({
  company_tin: z.string().min(1, 'Company TIN is required'),
  company_name: z.string().min(1, 'Company name is required'),
  company_security_key: z.string().min(1, 'Security key is required'),
  is_test: z.boolean().default(true)
});

type GraCredentialsFormValues = z.infer<typeof graCredentialsSchema>;

const Settings: React.FC = () => {
  const { user } = useSession();
  const [activeTab, setActiveTab] = useState('notifications');
  const [preferences, setPreferences] = useState<Tables<"user_preferences"> | null>(null);
  const [graCredentials, setGraCredentials] = useState<Tables<"gra_credentials"> | null>(null);
  const [subscription, setSubscription] = useState<Tables<"subscriptions"> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<string | null>(null);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  // Email notification states
  const [emailNotifications, setEmailNotifications] = useState(DEFAULT_EMAIL_NOTIFICATIONS);

  // Dashboard widget states
  const [dashboardWidgets, setDashboardWidgets] = useState(DEFAULT_DASHBOARD_WIDGETS);

  // Language and timezone states
  const [language, setLanguage] = useState('en');
  const [timezone, setTimezone] = useState('Africa/Accra');

  // Initialize GRA credentials form
  const { register, handleSubmit, reset, formState: { errors } } = useForm<GraCredentialsFormValues>({
    resolver: zodResolver(graCredentialsSchema),
    defaultValues: {
      company_tin: '',
      company_name: '',
      company_security_key: '',
      is_test: true
    }
  });

  // Load user preferences and GRA credentials on component mount
  useEffect(() => {
    const loadData = async () => {
      if (user) {
        setIsLoading(true);
        try {
          // Load user preferences
          const userPreferences = await getUserPreferences(user.id);
          if (userPreferences) {
            setPreferences(userPreferences);
            setEmailNotifications(userPreferences.email_notifications as typeof DEFAULT_EMAIL_NOTIFICATIONS);
            setDashboardWidgets(userPreferences.dashboard_widgets as typeof DEFAULT_DASHBOARD_WIDGETS);
            setTheme(userPreferences.theme as 'light' | 'dark' || 'light');
            setLanguage(userPreferences.language || 'en');
            setTimezone(userPreferences.timezone || 'Africa/Accra');
          }

          // Load GRA credentials
          const credentials = await getGraCredentials(user.id);
          if (credentials) {
            setGraCredentials(credentials);
            reset({
              company_tin: credentials.company_tin,
              company_name: credentials.company_name,
              company_security_key: credentials.company_security_key,
              is_test: credentials.is_test
            });
          }

          // Load subscription data
          const userSubscription = await getUserSubscription(user.id);
          if (userSubscription) {
            setSubscription(userSubscription);
          }
        } catch (error) {
          console.error('Error loading settings data:', error);
          toast({
            title: 'Error',
            description: 'Failed to load settings',
            variant: 'destructive'
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadData();
  }, [user, reset]);

  // Handle email notification toggle
  const handleNotificationToggle = async (key: keyof typeof DEFAULT_EMAIL_NOTIFICATIONS, value: boolean) => {
    if (!user) return;

    setEmailNotifications(prev => ({
      ...prev,
      [key]: value
    }));

    try {
      await updateEmailNotificationPreferences(user.id, {
        [key]: value
      });
    } catch (error) {
      console.error('Error updating notification preference:', error);
      toast({
        title: 'Error',
        description: 'Failed to update notification preference',
        variant: 'destructive'
      });
    }
  };

  // Handle dashboard widget toggle
  const handleWidgetToggle = async (key: keyof typeof DEFAULT_DASHBOARD_WIDGETS, value: boolean) => {
    if (!user) return;

    setDashboardWidgets(prev => ({
      ...prev,
      [key]: value
    }));

    try {
      await updateDashboardWidgetPreferences(user.id, {
        [key]: value
      });
    } catch (error) {
      console.error('Error updating widget preference:', error);
      toast({
        title: 'Error',
        description: 'Failed to update widget preference',
        variant: 'destructive'
      });
    }
  };

  // Use the theme context
  const { theme: currentTheme, setTheme: setGlobalTheme } = useTheme();

  // Handle theme change
  const handleThemeChange = async (newTheme: 'light' | 'dark' | 'system') => {
    if (!user) return;

    // Update local state
    setTheme(newTheme);

    // Update global theme
    setGlobalTheme(newTheme);

    try {
      // Save to database
      await updateThemePreference(user.id, newTheme);
    } catch (error) {
      console.error('Error updating theme preference:', error);
      toast({
        title: 'Error',
        description: 'Failed to update theme preference',
        variant: 'destructive'
      });
    }
  };

  // Handle language change
  const handleLanguageChange = async (newLanguage: string) => {
    if (!user) return;

    setLanguage(newLanguage);

    try {
      await updateLanguagePreference(user.id, newLanguage);
    } catch (error) {
      console.error('Error updating language preference:', error);
      toast({
        title: 'Error',
        description: 'Failed to update language preference',
        variant: 'destructive'
      });
    }
  };

  // Handle timezone change
  const handleTimezoneChange = async (newTimezone: string) => {
    if (!user) return;

    setTimezone(newTimezone);

    try {
      await updateTimezonePreference(user.id, newTimezone);
    } catch (error) {
      console.error('Error updating timezone preference:', error);
      toast({
        title: 'Error',
        description: 'Failed to update timezone preference',
        variant: 'destructive'
      });
    }
  };

  // Handle subscription upgrade
  const handleSubscriptionUpgrade = async (planType: string, period: 'monthly' | 'yearly') => {
    if (!user) return;

    setIsUpgrading(true);
    try {
      console.log(`Initializing subscription upgrade: ${planType} (${period})`);

      const result = await initializeSubscriptionUpgrade(
        user.id,
        selectedOrganization,
        planType,
        period,
        user.email || '',
        `${window.location.origin}/subscription/callback`
      );

      if (result) {
        console.log('Subscription upgrade initialized successfully:', result);

        // Show success message
        toast({
          title: 'Redirecting to payment',
          description: 'You will be redirected to Paystack to complete your payment',
        });

        // Redirect to Paystack checkout
        setTimeout(() => {
          window.location.href = result.authorizationUrl;
        }, 1500);
      } else {
        console.error('Subscription upgrade initialization returned null');
        throw new Error('Failed to initialize subscription upgrade');
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to upgrade subscription. Please try again later.',
        variant: 'destructive'
      });
      setIsUpgrading(false);
    }
  };

  // Handle GRA credentials form submission
  const onSubmitGraCredentials = async (data: GraCredentialsFormValues) => {
    if (!user) return;

    setIsSaving(true);
    try {
      const credentialsToSave = {
        user_id: user.id,
        company_tin: data.company_tin,
        company_name: data.company_name,
        company_security_key: data.company_security_key,
        is_test: data.is_test ?? true
      };
      const savedCredentials = await saveGraCredentials(credentialsToSave);

      if (savedCredentials) {
        setGraCredentials(savedCredentials);
        toast({
          title: 'Success',
          description: 'GRA credentials saved successfully'
        });
      } else {
        throw new Error('Failed to save GRA credentials');
      }
    } catch (error) {
      console.error('Error saving GRA credentials:', error);
      toast({
        title: 'Error',
        description: 'Failed to save GRA credentials',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Settings</h1>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-ghana-green" />
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 md:gap-6">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <CardContent className="p-4">
                  <nav className="space-y-2">
                    <Button
                      variant={activeTab === 'notifications' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setActiveTab('notifications')}
                    >
                      <Bell className="mr-2 h-4 w-4" />
                      Notifications
                    </Button>
                    <Button
                      variant={activeTab === 'dashboard' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setActiveTab('dashboard')}
                    >
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      Dashboard
                    </Button>
                    <Button
                      variant={activeTab === 'appearance' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setActiveTab('appearance')}
                    >
                      <Sun className="mr-2 h-4 w-4" />
                      Appearance
                    </Button>
                    <Button
                      variant={activeTab === 'invoice' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setActiveTab('invoice')}
                    >
                      <FileText className="mr-2 h-4 w-4" />
                      Invoice Settings
                    </Button>
                    <Button
                      variant={activeTab === 'gra' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setActiveTab('gra')}
                    >
                      <Key className="mr-2 h-4 w-4" />
                      GRA Credentials
                    </Button>
                    <Button
                      variant={activeTab === 'billing' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setActiveTab('billing')}
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      Billing
                    </Button>
                    <Button
                      variant={activeTab === 'integrations' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setActiveTab('integrations')}
                    >
                      <PaymentIcon className="mr-2 h-4 w-4" />
                      Integrations
                    </Button>
                  </nav>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Notifications Settings */}
              {activeTab === 'notifications' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Email Notifications</CardTitle>
                    <CardDescription>
                      Configure which email notifications you receive
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-4">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <Label htmlFor="invoice-created">Invoice Created</Label>
                          <p className="text-sm text-muted-foreground truncate">
                            Receive an email when a new invoice is created
                          </p>
                        </div>
                        <Switch
                          id="invoice-created"
                          checked={emailNotifications.invoice_created}
                          onCheckedChange={(checked) => handleNotificationToggle('invoice_created', checked)}
                          className="flex-shrink-0"
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="invoice-sent">Invoice Sent</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive an email when an invoice is sent to a client
                          </p>
                        </div>
                        <Switch
                          id="invoice-sent"
                          checked={emailNotifications.invoice_sent}
                          onCheckedChange={(checked) => handleNotificationToggle('invoice_sent', checked)}
                        />
                      </div>

                      <Separator />

                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <Label htmlFor="invoice-paid">Invoice Paid</Label>
                          <p className="text-sm text-muted-foreground truncate">
                            Receive an email when an invoice is paid
                          </p>
                        </div>
                        <Switch
                          id="invoice-paid"
                          checked={emailNotifications.invoice_paid}
                          onCheckedChange={(checked) => handleNotificationToggle('invoice_paid', checked)}
                          className="flex-shrink-0"
                        />
                      </div>

                      <Separator />

                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <Label htmlFor="invoice-overdue">Invoice Overdue</Label>
                          <p className="text-sm text-muted-foreground truncate">
                            Receive an email when an invoice becomes overdue
                          </p>
                        </div>
                        <Switch
                          id="invoice-overdue"
                          checked={emailNotifications.invoice_overdue}
                          onCheckedChange={(checked) => handleNotificationToggle('invoice_overdue', checked)}
                          className="flex-shrink-0"
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="payment-received">Payment Received</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive an email when a payment is received
                          </p>
                        </div>
                        <Switch
                          id="payment-received"
                          checked={emailNotifications.payment_received}
                          onCheckedChange={(checked) => handleNotificationToggle('payment_received', checked)}
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="subscription-renewal">Subscription Renewal</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive an email when your subscription is renewed
                          </p>
                        </div>
                        <Switch
                          id="subscription-renewal"
                          checked={emailNotifications.subscription_renewal}
                          onCheckedChange={(checked) => handleNotificationToggle('subscription_renewal', checked)}
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="subscription-expiring">Subscription Expiring</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive an email when your subscription is about to expire
                          </p>
                        </div>
                        <Switch
                          id="subscription-expiring"
                          checked={emailNotifications.subscription_expiring}
                          onCheckedChange={(checked) => handleNotificationToggle('subscription_expiring', checked)}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Dashboard Settings */}
              {activeTab === 'dashboard' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Dashboard Widgets</CardTitle>
                    <CardDescription>
                      Configure which widgets appear on your dashboard
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-4">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <Label htmlFor="revenue">Revenue</Label>
                          <p className="text-sm text-muted-foreground truncate">
                            Show revenue statistics on your dashboard
                          </p>
                        </div>
                        <Switch
                          id="revenue"
                          checked={dashboardWidgets.revenue}
                          onCheckedChange={(checked) => handleWidgetToggle('revenue', checked)}
                          className="flex-shrink-0"
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="tax-collected">Tax Collected</Label>
                          <p className="text-sm text-muted-foreground">
                            Show tax collection statistics on your dashboard
                          </p>
                        </div>
                        <Switch
                          id="tax-collected"
                          checked={dashboardWidgets.tax_collected}
                          onCheckedChange={(checked) => handleWidgetToggle('tax_collected', checked)}
                        />
                      </div>

                      <Separator />

                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <Label htmlFor="unpaid-invoices">Unpaid Invoices</Label>
                          <p className="text-sm text-muted-foreground truncate">
                            Show unpaid invoices statistics on your dashboard
                          </p>
                        </div>
                        <Switch
                          id="unpaid-invoices"
                          checked={dashboardWidgets.unpaid_invoices}
                          onCheckedChange={(checked) => handleWidgetToggle('unpaid_invoices', checked)}
                          className="flex-shrink-0"
                        />
                      </div>

                      <Separator />

                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <Label htmlFor="clients">Clients</Label>
                          <p className="text-sm text-muted-foreground truncate">
                            Show client statistics on your dashboard
                          </p>
                        </div>
                        <Switch
                          id="clients"
                          checked={dashboardWidgets.clients}
                          onCheckedChange={(checked) => handleWidgetToggle('clients', checked)}
                          className="flex-shrink-0"
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="recent-activity">Recent Activity</Label>
                          <p className="text-sm text-muted-foreground">
                            Show recent activity on your dashboard
                          </p>
                        </div>
                        <Switch
                          id="recent-activity"
                          checked={dashboardWidgets.recent_activity}
                          onCheckedChange={(checked) => handleWidgetToggle('recent_activity', checked)}
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="invoice-status">Invoice Status</Label>
                          <p className="text-sm text-muted-foreground">
                            Show invoice status breakdown on your dashboard
                          </p>
                        </div>
                        <Switch
                          id="invoice-status"
                          checked={dashboardWidgets.invoice_status}
                          onCheckedChange={(checked) => handleWidgetToggle('invoice_status', checked)}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Appearance Settings */}
              {activeTab === 'appearance' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Appearance</CardTitle>
                    <CardDescription>
                      Customize the appearance of your dashboard
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label>Theme</Label>
                      <div className="flex flex-wrap gap-4">
                        <Button
                          variant={theme === 'light' ? 'default' : 'outline'}
                          className="flex-1"
                          onClick={() => handleThemeChange('light')}
                        >
                          <Sun className="mr-2 h-4 w-4" />
                          Light
                        </Button>
                        <Button
                          variant={theme === 'dark' ? 'default' : 'outline'}
                          className="flex-1"
                          onClick={() => handleThemeChange('dark')}
                        >
                          <Moon className="mr-2 h-4 w-4" />
                          Dark
                        </Button>
                        <Button
                          variant={theme === 'system' ? 'default' : 'outline'}
                          className="flex-1"
                          onClick={() => handleThemeChange('system')}
                        >
                          <Laptop className="mr-2 h-4 w-4" />
                          System
                        </Button>
                      </div>
                      <div className="text-sm text-muted-foreground mt-2">
                        Current theme: {currentTheme === 'system' ? 'System' : currentTheme === 'dark' ? 'Dark' : 'Light'}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="language">Language</Label>
                      <Select value={language} onValueChange={handleLanguageChange}>
                        <SelectTrigger id="language">
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="timezone">Timezone</Label>
                      <Select value={timezone} onValueChange={handleTimezoneChange}>
                        <SelectTrigger id="timezone">
                          <SelectValue placeholder="Select timezone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Africa/Accra">Africa/Accra (GMT+0)</SelectItem>
                          <SelectItem value="Europe/London">Europe/London (GMT+0/+1)</SelectItem>
                          <SelectItem value="America/New_York">America/New_York (GMT-5/-4)</SelectItem>
                          <SelectItem value="Asia/Dubai">Asia/Dubai (GMT+4)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Invoice Settings */}
              {activeTab === 'invoice' && (
                <InvoiceSettings />
              )}

              {/* GRA Credentials Settings */}
              {activeTab === 'gra' && (
                <Card>
                  <CardHeader>
                    <CardTitle>GRA E-VAT Integration</CardTitle>
                    <CardDescription>
                      Configure your Ghana Revenue Authority (GRA) E-VAT API credentials for invoice submission
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                      <p className="text-blue-700 font-medium">Enhanced GRA E-VAT Integration</p>
                      <p className="text-sm text-blue-600 mt-1">
                        We've updated our GRA E-VAT integration to support the latest API version (v8.1).
                        Please use the new GRA Settings page to configure your credentials.
                      </p>
                    </div>

                    {subscription && (subscription.plan_type === 'freelancer' || subscription.plan_type === 'business' || subscription.plan_type === 'enterprise') ? (
                      <>
                        {graCredentials ? (
                          <div className="bg-green-50 border border-green-200 rounded-md p-4">
                            <p className="text-green-700 flex items-center">
                              <Check className="h-4 w-4 mr-2" />
                              GRA credentials are configured
                            </p>
                            <p className="text-sm text-green-600 mt-1">
                              You can update your credentials on the GRA Settings page.
                            </p>
                          </div>
                        ) : (
                          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                            <p className="text-yellow-700 flex items-center">
                              <AlertTriangle className="h-4 w-4 mr-2" />
                              GRA credentials not configured
                            </p>
                            <p className="text-sm text-yellow-600 mt-1">
                              Configure your GRA credentials to enable invoice submission to GRA.
                            </p>
                          </div>
                        )}

                        <div className="flex justify-center">
                          <Button
                            className="w-full md:w-auto"
                            onClick={() => window.location.href = '/gra-settings'}
                          >
                            Go to GRA Settings
                          </Button>
                        </div>

                        <div className="text-sm text-muted-foreground">
                          <p className="font-medium mb-2">What you can do on the GRA Settings page:</p>
                          <ul className="list-disc list-inside space-y-1">
                            <li>Configure your GRA E-VAT API credentials</li>
                            <li>Test your API connection</li>
                            <li>Enable mock mode for development</li>
                            <li>Switch between test and production environments</li>
                          </ul>
                        </div>
                      </>
                    ) : (
                      <div className="space-y-6">
                        <div className="bg-amber-50 border border-amber-200 rounded-md p-4">
                          <p className="text-amber-700 flex items-center font-medium">
                            <Lock className="h-4 w-4 mr-2" />
                            Subscription Upgrade Required
                          </p>
                          <p className="text-sm text-amber-600 mt-1">
                            The GRA E-VAT API integration is available on the Freelancer, Business, and Enterprise plans.
                            Upgrade your subscription to access this feature.
                          </p>
                        </div>

                        <div className="flex justify-center">
                          <Button
                            className="w-full md:w-auto bg-ghana-green hover:bg-ghana-green/90"
                            onClick={() => window.location.href = '/subscription-management'}
                          >
                            Upgrade Subscription
                          </Button>
                        </div>

                        <div className="text-sm text-muted-foreground">
                          <p className="font-medium mb-2">Benefits of the GRA E-VAT API integration:</p>
                          <ul className="list-disc list-inside space-y-1">
                            <li>Direct submission of invoices to GRA</li>
                            <li>Real-time validation of invoices</li>
                            <li>Automatic generation of QR codes</li>
                            <li>Compliance with GRA E-VAT regulations</li>
                          </ul>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Integrations Settings */}
              {activeTab === 'integrations' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Payment Integrations</CardTitle>
                      <CardDescription>
                        Connect your payment gateways to process payments for your invoices
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <PaystackIntegration />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Billing Settings */}
              {activeTab === 'billing' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Billing</CardTitle>
                    <CardDescription>
                      Manage your subscription and billing information
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Current Plan */}
                      <div className="p-4 border rounded-lg">
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-medium">Current Plan</h3>
                            <p className="text-sm text-muted-foreground">
                              {subscription ? (
                                <>
                                  {subscription.plan_type === 'freelancer' ? 'Freelancer Plan' :
                                   subscription.plan_type === 'business' ? 'Business Plan' : 'Free Plan'}
                                  {subscription.status === 'active' && (
                                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                      Active
                                    </span>
                                  )}
                                </>
                              ) : (
                                'Free Plan'
                              )}
                            </p>
                            {subscription && (
                              <p className="text-xs text-muted-foreground mt-1">
                                Renews on {new Date(subscription.current_period_end || '').toLocaleDateString()}
                              </p>
                            )}
                          </div>
                          <Button
                            onClick={() => window.location.href = '/subscription-management'}
                            className="bg-ghana-green hover:bg-ghana-green/90"
                          >
                            Manage Subscription
                          </Button>
                        </div>
                      </div>

                      {/* Billing History */}
                      {subscription && subscription.plan_type !== 'free' && (
                        <div className="p-4 border rounded-lg">
                          <h3 className="font-medium mb-4">Billing History</h3>
                          <div className="bg-gray-50 p-4 rounded-md text-center">
                            <p className="text-sm text-muted-foreground">
                              Your billing history will appear here
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Subscription Management */}
                      <div id="subscription-management" className="p-4 border rounded-lg">
                        <h3 className="font-medium mb-4">Subscription Management</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Visit our dedicated subscription management page to:
                        </p>
                        <ul className="text-sm text-muted-foreground mb-6 space-y-2">
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                            <span>View and compare all available plans</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                            <span>Upgrade or downgrade your subscription</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                            <span>Manage billing cycles (monthly/yearly)</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-ghana-green mr-2 shrink-0" />
                            <span>View billing history and invoices</span>
                          </li>
                        </ul>
                        <Button
                          className="w-full bg-ghana-green hover:bg-ghana-green/90"
                          onClick={() => window.location.href = '/subscription-management'}
                        >
                          Go to Subscription Management
                        </Button>
                      </div>

                      {/* Payment Methods */}
                      <div className="p-4 border rounded-lg">
                        <h3 className="font-medium mb-2">Payment Methods</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          We accept all major credit cards, mobile money (MTN Mobile Money, Vodafone Cash, AirtelTigo Money), and bank transfers for annual plans.
                        </p>
                        <div className="flex flex-wrap gap-2">
                          <div className="bg-gray-100 p-2 rounded">
                            <span className="text-xs font-medium">Visa</span>
                          </div>
                          <div className="bg-gray-100 p-2 rounded">
                            <span className="text-xs font-medium">Mastercard</span>
                          </div>
                          <div className="bg-gray-100 p-2 rounded">
                            <span className="text-xs font-medium">MTN Mobile Money</span>
                          </div>
                          <div className="bg-gray-100 p-2 rounded">
                            <span className="text-xs font-medium">Vodafone Cash</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Settings;
