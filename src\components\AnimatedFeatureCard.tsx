import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface AnimatedFeatureCardProps {
  icon?: ReactNode;
  title: string;
  description: string;
  className?: string;
  iconClassName?: string;
  delay?: number;
  cta?: {
    text: string;
    onClick: () => void;
  };
}

/**
 * AnimatedFeatureCard - A component for displaying feature cards with animations
 * Perfect for features, pricing, and other marketing pages
 */
const AnimatedFeatureCard: React.FC<AnimatedFeatureCardProps> = ({
  icon,
  title,
  description,
  className = '',
  iconClassName = '',
  delay = 0,
  cta
}) => {
  return (
    <motion.div
      className={cn(
        'bg-white p-8 rounded-xl shadow-md border border-gray-100 transition-all duration-500 hover:shadow-xl hover:-translate-y-2 group',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: '-50px' }}
      transition={{ 
        duration: 0.5, 
        delay,
        ease: [0.25, 0.1, 0.25, 1]
      }}
    >
      {icon && (
        <motion.div
          className={cn(
            'w-16 h-16 bg-ghana-green/10 rounded-full flex items-center justify-center mb-6 mx-auto',
            iconClassName
          )}
          whileHover={{ scale: 1.1, backgroundColor: 'rgba(0, 128, 0, 0.2)' }}
          transition={{ duration: 0.3 }}
        >
          {icon}
        </motion.div>
      )}
      
      <motion.h3 
        className="text-xl font-semibold mb-4 text-center group-hover:text-ghana-green transition-colors duration-300"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: delay + 0.2, duration: 0.3 }}
      >
        {title}
      </motion.h3>
      
      <motion.p 
        className="text-gray-600 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: delay + 0.3, duration: 0.3 }}
      >
        {description}
      </motion.p>
      
      {cta && (
        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: delay + 0.4, duration: 0.3 }}
        >
          <motion.button
            className="text-ghana-green font-medium flex items-center justify-center mx-auto"
            onClick={cta.onClick}
            whileHover={{ x: 5 }}
            whileTap={{ scale: 0.95 }}
          >
            {cta.text}
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5 ml-2 transition-transform group-hover:translate-x-1" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M14 5l7 7m0 0l-7 7m7-7H3" 
              />
            </svg>
          </motion.button>
        </motion.div>
      )}
    </motion.div>
  );
};

export default AnimatedFeatureCard;
