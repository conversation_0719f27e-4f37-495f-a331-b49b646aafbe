
import React, { useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { <PERSON>R<PERSON>, CheckCircle, Zap, Shield, Clock } from 'lucide-react';

// Fallback for framer-motion if not installed
let useInView: any;
let motion: any;
try {
  const framerMotion = require('framer-motion');
  useInView = framerMotion.useInView;
  motion = framerMotion.motion;

  // Make sure we're using the create method for custom components
  if (!motion.create && typeof motion === 'function') {
    const originalMotion = motion;
    motion = {
      ...framerMotion.motion,
      create: originalMotion
    };
  }
} catch (error) {
  useInView = () => ({ ref: useRef(), isInView: true });
  motion = {
    div: (props: any) => <div {...props} />,
    h2: (props: any) => <h2 {...props} />,
    p: (props: any) => <p {...props} />,
    create: (Component: any) => (props: any) => <Component {...props} />
  };
}

const FeatureItem = ({ icon, text }: { icon: React.ReactNode, text: string }) => (
  <div className="flex items-center space-x-2">
    <div className="text-ghana-green dark:text-ghana-gold">{icon}</div>
    <span className="text-sm text-gray-700 dark:text-gray-300">{text}</span>
  </div>
);

const CTASection: React.FC = () => {
  const ref = useRef(null);
  const isInView = useInView ? useInView(ref, { once: true, amount: 0.3 }) : true;

  const MotionDiv = motion?.div || 'div';

  return (
    <section className="py-20 md:py-28 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950 relative overflow-hidden">
      {/* Abstract pattern for background */}
      <div className="absolute inset-0 ghana-pattern opacity-10 z-0"></div>

      {/* Animated background elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-ghana-gold/5 dark:bg-ghana-gold/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-ghana-green/5 dark:bg-ghana-green/10 rounded-full blur-3xl"></div>

      {/* Animated dots */}
      <div className="absolute top-20 left-10 w-3 h-3 bg-ghana-gold/30 dark:bg-ghana-gold/40 rounded-full animate-pulse"></div>
      <div className="absolute top-40 right-20 w-2 h-2 bg-ghana-green/30 dark:bg-ghana-green/40 rounded-full animate-float"></div>
      <div className="absolute bottom-20 left-1/4 w-4 h-4 bg-ghana-green/20 dark:bg-ghana-green/30 rounded-full animate-bounce"></div>

      <div className="container relative z-10">
        <MotionDiv
          ref={ref}
          className="max-w-5xl mx-auto bg-gradient-to-br from-white to-ghana-gold/5 dark:from-gray-800 dark:to-ghana-gold/10 rounded-2xl p-8 md:p-12 border border-ghana-gold/20 dark:border-ghana-gold/30 shadow-xl relative overflow-hidden"
          initial={{ opacity: 0, y: 40 }}
          animate={{
            opacity: isInView ? 1 : 0,
            y: isInView ? 0 : 40,
            transition: { duration: 0.7 }
          }}
        >
          {/* Decorative elements */}
          <div className="absolute -top-20 -right-20 w-40 h-40 bg-ghana-gold/10 dark:bg-ghana-gold/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-ghana-green/10 dark:bg-ghana-green/20 rounded-full blur-3xl"></div>

          <div className="relative z-10">
            <div className="flex flex-col md:flex-row gap-8 items-center">
              <div className="md:w-3/5 text-left">
                <div className="inline-block bg-ghana-green/10 dark:bg-ghana-green/20 px-4 py-1.5 rounded-full mb-4">
                  <span className="text-sm font-semibold text-ghana-green dark:text-ghana-gold flex items-center">
                    <Zap className="h-4 w-4 mr-1.5" />
                    Start Today
                  </span>
                </div>

                <h2 className="text-3xl md:text-4xl font-bold font-display mb-4 dark:text-foreground">
                  Ready to <span className="text-ghana-green dark:text-ghana-gold">simplify</span> your invoicing?
                </h2>

                <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
                  Join thousands of Ghanaian businesses that are saving time and ensuring GRA compliance with our easy-to-use invoicing platform.
                </p>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-8">
                  <FeatureItem
                    icon={<CheckCircle className="h-4 w-4" />}
                    text="GRA VAT-compliant invoices"
                  />
                  <FeatureItem
                    icon={<CheckCircle className="h-4 w-4" />}
                    text="Real-time tax calculations"
                  />
                  <FeatureItem
                    icon={<CheckCircle className="h-4 w-4" />}
                    text="Client management"
                  />
                  <FeatureItem
                    icon={<CheckCircle className="h-4 w-4" />}
                    text="Paystack integration"
                  />
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Link to="/auth" className="group">
                    <Button className="bg-ghana-green hover:bg-ghana-green/90 text-white px-8 py-6 text-lg w-full sm:w-auto transition-all duration-300 transform group-hover:scale-105 shadow-lg hover:shadow-xl dark:bg-ghana-green/90 dark:hover:bg-ghana-green">
                      Get Started Free
                      <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                    </Button>
                  </Link>
                  <Link to="/demo">
                    <Button variant="outline" className="px-8 py-6 text-lg border-2 border-ghana-green text-ghana-green hover:bg-ghana-green hover:text-white transition-all duration-300 dark:border-ghana-gold dark:text-ghana-gold dark:hover:bg-ghana-gold/80 dark:hover:text-black">
                      Request Demo
                    </Button>
                  </Link>
                </div>

                <p className="mt-6 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                  <Shield className="h-4 w-4 mr-1.5 text-ghana-green dark:text-ghana-gold" />
                  No credit card required. Free plan available with 5 invoices per month.
                </p>
              </div>

              <div className="md:w-2/5 relative">
                <div className="absolute inset-0 bg-gradient-to-br from-ghana-green/20 to-ghana-gold/20 dark:from-ghana-green/30 dark:to-ghana-gold/30 rounded-full blur-3xl opacity-30"></div>
                <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                  <div className="bg-ghana-green dark:bg-ghana-green/90 py-2 px-4 text-white text-sm font-medium">
                    Invoice #2023-001
                  </div>
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-bold text-sm dark:text-white">Your Business</h3>
                        <p className="text-gray-500 dark:text-gray-400 text-xs">Accra, Ghana</p>
                      </div>
                      <div className="bg-ghana-gold/20 dark:bg-ghana-gold/30 px-2 py-1 rounded text-xs font-bold text-ghana-gold">
                        PAID
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-xs border-b border-dashed border-gray-200 dark:border-gray-700 pb-1">
                        <span className="text-gray-500 dark:text-gray-400">Web Development</span>
                        <span className="dark:text-white">₵2,500</span>
                      </div>
                      <div className="flex justify-between text-xs border-b border-dashed border-gray-200 dark:border-gray-700 pb-1">
                        <span className="text-gray-500 dark:text-gray-400">UI/UX Design</span>
                        <span className="dark:text-white">₵1,800</span>
                      </div>
                    </div>

                    <div className="flex justify-between text-sm font-bold dark:text-white">
                      <span>Total</span>
                      <span>₵4,300</span>
                    </div>

                    <div className="mt-4 flex justify-center">
                      <div className="bg-ghana-green/10 dark:bg-ghana-green/20 text-ghana-green dark:text-ghana-gold text-xs px-2 py-1 rounded flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        GRA Verified
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </MotionDiv>
      </div>
    </section>
  );
};

export default CTASection;
