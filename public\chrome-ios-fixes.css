/**
 * Special CSS fixes for Chrome on iOS
 * This file is loaded only for Chrome on iOS to fix rendering issues
 */

/* Force hardware acceleration */
body, html {
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}

/* Fix for CSS variables */
:root {
  --ghana-green: #00A651 !important;
  --ghana-gold: #FFD700 !important;
  --radius: 0.5rem !important;
  --background: 0 0% 100% !important;
  --foreground: 222.2 84% 4.9% !important;
  --card: 0 0% 100% !important;
  --card-foreground: 222.2 84% 4.9% !important;
  --popover: 0 0% 100% !important;
  --popover-foreground: 222.2 84% 4.9% !important;
  --primary: 142 100% 33% !important;
  --primary-foreground: 210 40% 98% !important;
  --secondary: 210 40% 96.1% !important;
  --secondary-foreground: 222.2 47.4% 11.2% !important;
  --muted: 210 40% 96.1% !important;
  --muted-foreground: 215.4 16.3% 46.9% !important;
  --accent: 210 40% 96.1% !important;
  --accent-foreground: 222.2 47.4% 11.2% !important;
  --destructive: 0 84.2% 60.2% !important;
  --destructive-foreground: 210 40% 98% !important;
  --border: 214.3 31.8% 91.4% !important;
  --input: 214.3 31.8% 91.4% !important;
  --ring: 142 100% 33% !important;
}

/* Dark mode fixes */
.dark {
  --background: 222.2 84% 4.9% !important;
  --foreground: 210 40% 98% !important;
  --card: 222.2 84% 4.9% !important;
  --card-foreground: 210 40% 98% !important;
  --popover: 222.2 84% 4.9% !important;
  --popover-foreground: 210 40% 98% !important;
  --primary: 51 100% 50% !important;
  --primary-foreground: 222.2 47.4% 11.2% !important;
  --secondary: 217.2 32.6% 17.5% !important;
  --secondary-foreground: 210 40% 98% !important;
  --muted: 217.2 32.6% 17.5% !important;
  --muted-foreground: 215 20.2% 65.1% !important;
  --accent: 217.2 32.6% 17.5% !important;
  --accent-foreground: 210 40% 98% !important;
  --destructive: 0 62.8% 30.6% !important;
  --destructive-foreground: 210 40% 98% !important;
  --border: 217.2 32.6% 17.5% !important;
  --input: 217.2 32.6% 17.5% !important;
  --ring: 51 100% 50% !important;
}

/* Fix for animations */
* {
  -webkit-animation-duration: 0.001s;
  animation-duration: 0.001s;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

/* Fix for flexbox issues */
.flex {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

.flex-col {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  -webkit-flex-direction: column !important;
  -ms-flex-direction: column !important;
  flex-direction: column !important;
}

/* Fix for grid issues */
.grid {
  display: -ms-grid !important;
  display: grid !important;
}

/* Fix for position sticky */
.sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

/* Fix for transitions */
.transition {
  -webkit-transition-property: none !important;
  transition-property: none !important;
}

/* Fix for transform */
.transform {
  -webkit-transform: none !important;
  transform: none !important;
}

/* Fix for opacity transitions */
.opacity-0 {
  opacity: 0 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

/* Fix for shadows */
.shadow, .shadow-sm, .shadow-md, .shadow-lg, .shadow-xl, .shadow-2xl {
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
}

/* Fix for rounded corners */
.rounded, .rounded-md, .rounded-lg, .rounded-xl, .rounded-2xl, .rounded-full {
  border-radius: 0.5rem !important;
}
