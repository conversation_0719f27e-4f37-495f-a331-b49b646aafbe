-- Migration to enhance subscription security and prevent payment gateway bypass

-- Update subscriptions table with additional security fields
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS payment_verification_token TEXT,
ADD COLUMN IF NOT EXISTS last_payment_transaction_id TEXT,
ADD COLUMN IF NOT EXISTS payment_method TEXT DEFAULT 'paystack',
ADD COLUMN IF NOT EXISTS upgrade_history JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS last_plan_change TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS plan_change_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_verification_ip TEXT;

-- Create subscription_transactions table to track all subscription-related transactions
CREATE TABLE IF NOT EXISTS subscription_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN (
    'create', 
    'upgrade', 
    'downgrade', 
    'renew', 
    'cancel', 
    'payment_success', 
    'payment_failure'
  )),
  previous_plan TEXT,
  new_plan TEXT,
  previous_billing_cycle TEXT,
  new_billing_cycle TEXT,
  amount DECIMAL(12, 2),
  currency TEXT DEFAULT 'GHS',
  payment_gateway TEXT DEFAULT 'paystack',
  payment_gateway_reference TEXT,
  payment_gateway_response JSONB,
  verification_status TEXT CHECK (verification_status IN (
    'pending', 
    'verified', 
    'failed'
  )) DEFAULT 'pending',
  client_ip TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CHECK (user_id IS NOT NULL OR organization_id IS NOT NULL)
);

-- Create function to update upgrade_history when subscription plan changes
CREATE OR REPLACE FUNCTION update_subscription_history()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.plan_type != NEW.plan_type OR OLD.billing_cycle != NEW.billing_cycle THEN
    -- Increment plan change count
    NEW.plan_change_count := COALESCE(OLD.plan_change_count, 0) + 1;
    
    -- Update last plan change timestamp
    NEW.last_plan_change := NOW();
    
    -- Add to upgrade history
    NEW.upgrade_history := COALESCE(OLD.upgrade_history, '[]'::jsonb) || jsonb_build_object(
      'timestamp', NOW(),
      'previous_plan', OLD.plan_type,
      'new_plan', NEW.plan_type,
      'previous_billing_cycle', OLD.billing_cycle,
      'new_billing_cycle', NEW.billing_cycle
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update upgrade_history
DROP TRIGGER IF EXISTS update_subscription_history_trigger ON subscriptions;
CREATE TRIGGER update_subscription_history_trigger
BEFORE UPDATE ON subscriptions
FOR EACH ROW
WHEN (OLD.plan_type IS DISTINCT FROM NEW.plan_type OR OLD.billing_cycle IS DISTINCT FROM NEW.billing_cycle)
EXECUTE FUNCTION update_subscription_history();

-- Create RLS policies for subscription_transactions
ALTER TABLE subscription_transactions ENABLE ROW LEVEL SECURITY;

-- Users can view their own subscription transactions
CREATE POLICY "Users can view their own subscription transactions"
ON subscription_transactions FOR SELECT
USING (auth.uid() = user_id);

-- Only authenticated users can insert their own subscription transactions
CREATE POLICY "Users can insert their own subscription transactions"
ON subscription_transactions FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Only authenticated users can update their own subscription transactions
CREATE POLICY "Users can update their own subscription transactions"
ON subscription_transactions FOR UPDATE
USING (auth.uid() = user_id);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_subscription_transactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update timestamps
CREATE TRIGGER update_subscription_transactions_updated_at
BEFORE UPDATE ON subscription_transactions
FOR EACH ROW
EXECUTE FUNCTION update_subscription_transactions_updated_at();

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_subscription_transactions_user_id ON subscription_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_transactions_subscription_id ON subscription_transactions(subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscription_transactions_payment_gateway_reference ON subscription_transactions(payment_gateway_reference);
