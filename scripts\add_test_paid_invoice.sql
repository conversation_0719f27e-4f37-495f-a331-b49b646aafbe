-- This script adds a test paid invoice for a specific user
-- Replace the values with actual values from your database

-- First, check if the client_paid_invoices table exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'client_paid_invoices'
  ) THEN
    RAISE EXCEPTION 'The client_paid_invoices table does not exist. Please run the migration script first.';
  END IF;
END
$$;

-- Replace these values with actual values from your database
-- 1. Replace '<EMAIL>' with the email of the user you want to test with
-- 2. Replace 'invoice-uuid-here' with an actual invoice ID from your invoices table
-- 3. Replace 'test-payment-reference' with any reference string

INSERT INTO public.client_paid_invoices (
  email,
  invoice_id,
  payment_reference,
  payment_date,
  claimed,
  claimed_by_user_id,
  claimed_at
)
VALUES (
  '<EMAIL>',  -- Replace with actual user email
  'invoice-uuid-here', -- Replace with actual invoice ID
  'test-payment-reference',
  NOW(),
  FALSE,
  NULL,
  NULL
)
ON CONFLICT (email, invoice_id) 
DO UPDATE SET 
  payment_reference = 'test-payment-reference',
  payment_date = NOW(),
  claimed = FALSE,
  claimed_by_user_id = NULL,
  claimed_at = NULL;

-- Verify the insertion
SELECT * FROM public.client_paid_invoices 
WHERE email = '<EMAIL>' -- Replace with the same email used above
ORDER BY payment_date DESC
LIMIT 10;
