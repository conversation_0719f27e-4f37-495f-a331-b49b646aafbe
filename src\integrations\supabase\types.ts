export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      analytics_data: {
        Row: {
          id: string
          user_id: string | null
          organization_id: string | null
          data_type: string
          time_period: string
          period_start: string
          period_end: string
          value: number
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          organization_id?: string | null
          data_type: string
          time_period: string
          period_start: string
          period_end: string
          value: number
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          organization_id?: string | null
          data_type?: string
          time_period?: string
          period_start?: string
          period_end?: string
          value?: number
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      clients: {
        Row: {
          id: string
          organization_id: string | null
          user_id: string | null
          name: string
          email: string | null
          phone: string | null
          address: string | null
          city: string | null
          country: string | null
          tax_identification_number: string | null
          tax_pin: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id?: string | null
          user_id?: string | null
          name: string
          email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          country?: string | null
          tax_identification_number?: string | null
          tax_pin?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string | null
          user_id?: string | null
          name?: string
          email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          country?: string | null
          tax_identification_number?: string | null
          tax_pin?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      gra_credentials: {
        Row: {
          id: string
          organization_id: string | null
          user_id: string | null
          company_tin: string
          company_name: string
          company_security_key: string
          is_test: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id?: string | null
          user_id?: string | null
          company_tin: string
          company_name: string
          company_security_key: string
          is_test?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string | null
          user_id?: string | null
          company_tin?: string
          company_name?: string
          company_security_key?: string
          is_test?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      invoice_items: {
        Row: {
          id: string
          invoice_id: string
          item_reference: string | null
          description: string
          quantity: number
          unit_price: number
          tax_code: string | null
          tax_rate: number | null
          levy_amount_a: number | null
          levy_amount_b: number | null
          levy_amount_c: number | null
          levy_amount_d: number | null
          discount: number | null
          item_category: string | null
          batch: string | null
          expire_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          invoice_id: string
          item_reference?: string | null
          description: string
          quantity?: number
          unit_price: number
          tax_code?: string | null
          tax_rate?: number | null
          levy_amount_a?: number | null
          levy_amount_b?: number | null
          levy_amount_c?: number | null
          levy_amount_d?: number | null
          discount?: number | null
          item_category?: string | null
          batch?: string | null
          expire_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          invoice_id?: string
          item_reference?: string | null
          description?: string
          quantity?: number
          unit_price?: number
          tax_code?: string | null
          tax_rate?: number | null
          levy_amount_a?: number | null
          levy_amount_b?: number | null
          levy_amount_c?: number | null
          levy_amount_d?: number | null
          discount?: number | null
          item_category?: string | null
          batch?: string | null
          expire_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      invoice_payments: {
        Row: {
          id: string
          invoice_id: string
          amount: number
          payment_date: string
          payment_method: string | null
          transaction_reference: string | null
          status: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          invoice_id: string
          amount: number
          payment_date?: string
          payment_method?: string | null
          transaction_reference?: string | null
          status?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          invoice_id?: string
          amount?: number
          payment_date?: string
          payment_method?: string | null
          transaction_reference?: string | null
          status?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      invoices: {
        Row: {
          id: string
          invoice_number: string
          organization_id: string | null
          user_id: string | null
          client_id: string | null
          status: string | null
          issue_date: string
          due_date: string | null
          subtotal: number
          tax_amount: number
          levy_amount: number
          discount_type: string | null
          discount_value: number | null
          total_amount: number
          notes: string | null
          terms: string | null
          currency: string | null
          exchange_rate: number | null
          computation_type: string | null
          gra_invoice_id: string | null
          gra_signature: string | null
          gra_qr_code_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          invoice_number: string
          organization_id?: string | null
          user_id?: string | null
          client_id?: string | null
          status?: string | null
          issue_date?: string
          due_date?: string | null
          subtotal?: number
          tax_amount?: number
          levy_amount?: number
          discount_type?: string | null
          discount_value?: number | null
          total_amount?: number
          notes?: string | null
          terms?: string | null
          currency?: string | null
          exchange_rate?: number | null
          computation_type?: string | null
          gra_invoice_id?: string | null
          gra_signature?: string | null
          gra_qr_code_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          invoice_number?: string
          organization_id?: string | null
          user_id?: string | null
          client_id?: string | null
          status?: string | null
          issue_date?: string
          due_date?: string | null
          subtotal?: number
          tax_amount?: number
          levy_amount?: number
          discount_type?: string | null
          discount_value?: number | null
          total_amount?: number
          notes?: string | null
          terms?: string | null
          currency?: string | null
          exchange_rate?: number | null
          computation_type?: string | null
          gra_invoice_id?: string | null
          gra_signature?: string | null
          gra_qr_code_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      organization_members: {
        Row: {
          id: string
          organization_id: string
          user_id: string
          role: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          user_id: string
          role?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          user_id?: string
          role?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      organizations: {
        Row: {
          id: string
          name: string
          tax_identification_number: string | null
          address: string | null
          city: string | null
          country: string | null
          phone: string | null
          email: string | null
          logo_url: string | null
          owner_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          tax_identification_number?: string | null
          address?: string | null
          city?: string | null
          country?: string | null
          phone?: string | null
          email?: string | null
          logo_url?: string | null
          owner_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          tax_identification_number?: string | null
          address?: string | null
          city?: string | null
          country?: string | null
          phone?: string | null
          email?: string | null
          logo_url?: string | null
          owner_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          full_name: string | null
          company_name: string | null
          phone: string | null
          address: string | null
          city: string | null
          country: string | null
          tax_identification_number: string | null
          role: string | null
          avatar_url: string | null
          postal_code: string | null
          business_email: string | null
          business_phone: string | null
          business_website: string | null
          business_registration_number: string | null
          business_logo_url: string | null
          vat_registered: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name?: string | null
          company_name?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          country?: string | null
          tax_identification_number?: string | null
          role?: string | null
          avatar_url?: string | null
          postal_code?: string | null
          business_email?: string | null
          business_phone?: string | null
          business_website?: string | null
          business_registration_number?: string | null
          business_logo_url?: string | null
          vat_registered?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string | null
          company_name?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          country?: string | null
          tax_identification_number?: string | null
          role?: string | null
          avatar_url?: string | null
          postal_code?: string | null
          business_email?: string | null
          business_phone?: string | null
          business_website?: string | null
          business_registration_number?: string | null
          business_logo_url?: string | null
          vat_registered?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          organization_id: string | null
          user_id: string | null
          plan_type: string | null
          status: string | null
          paystack_subscription_code: string | null
          paystack_customer_code: string | null
          current_period_start: string | null
          current_period_end: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id?: string | null
          user_id?: string | null
          plan_type?: string | null
          status?: string | null
          paystack_subscription_code?: string | null
          paystack_customer_code?: string | null
          current_period_start?: string | null
          current_period_end?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string | null
          user_id?: string | null
          plan_type?: string | null
          status?: string | null
          paystack_subscription_code?: string | null
          paystack_customer_code?: string | null
          current_period_start?: string | null
          current_period_end?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      email_notifications: {
        Row: {
          id: string
          user_id: string | null
          organization_id: string | null
          invoice_id: string | null
          notification_type: string
          recipient_email: string
          subject: string
          content: string
          status: string
          error_message: string | null
          sent_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          organization_id?: string | null
          invoice_id?: string | null
          notification_type: string
          recipient_email: string
          subject: string
          content: string
          status: string
          error_message?: string | null
          sent_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          organization_id?: string | null
          invoice_id?: string | null
          notification_type?: string
          recipient_email?: string
          subject?: string
          content?: string
          status?: string
          error_message?: string | null
          sent_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      reports: {
        Row: {
          id: string
          user_id: string | null
          organization_id: string | null
          report_type: string
          name: string
          description: string | null
          parameters: Json
          start_date: string
          end_date: string
          file_url: string | null
          status: string
          error_message: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          organization_id?: string | null
          report_type: string
          name: string
          description?: string | null
          parameters: Json
          start_date: string
          end_date: string
          file_url?: string | null
          status: string
          error_message?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          organization_id?: string | null
          report_type?: string
          name?: string
          description?: string | null
          parameters?: Json
          start_date?: string
          end_date?: string
          file_url?: string | null
          status?: string
          error_message?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      user_preferences: {
        Row: {
          id: string
          user_id: string
          email_notifications: Json
          dashboard_widgets: Json
          theme: string | null
          language: string | null
          timezone: string | null
          invoice_settings: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email_notifications?: Json
          dashboard_widgets?: Json
          theme?: string | null
          language?: string | null
          timezone?: string | null
          invoice_settings?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email_notifications?: Json
          dashboard_widgets?: Json
          theme?: string | null
          language?: string | null
          timezone?: string | null
          invoice_settings?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      user_subscriptions: {
        Row: {
          id: string
          user_id: string
          organization_id: string | null
          tier: string
          status: string
          subscription_start_date: string
          subscription_end_date: string | null
          billing_cycle: string
          auto_renew: boolean
          payment_method_id: string | null
          last_payment_date: string | null
          next_payment_date: string | null
          payment_status: string
          cancellation_date: string | null
          notification_sent: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          organization_id?: string | null
          tier: string
          status?: string
          subscription_start_date?: string
          subscription_end_date?: string | null
          billing_cycle?: string
          auto_renew?: boolean
          payment_method_id?: string | null
          last_payment_date?: string | null
          next_payment_date?: string | null
          payment_status?: string
          cancellation_date?: string | null
          notification_sent?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          organization_id?: string | null
          tier?: string
          status?: string
          subscription_start_date?: string
          subscription_end_date?: string | null
          billing_cycle?: string
          auto_renew?: boolean
          payment_method_id?: string | null
          last_payment_date?: string | null
          next_payment_date?: string | null
          payment_status?: string
          cancellation_date?: string | null
          notification_sent?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
