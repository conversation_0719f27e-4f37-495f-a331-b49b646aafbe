// This script fixes the position of the install button on iOS Chrome
(function() {
  // Check if it's iOS Chrome
  if (/CriOS/i.test(navigator.userAgent) && /iPad|iPhone|iPod/i.test(navigator.userAgent)) {
    // Create a style element
    var style = document.createElement('style');

    // Add CSS to fix the button position
    style.textContent = `
      #universal-install-button {
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        z-index: 999999 !important;
        transform: translateZ(0) !important;
        -webkit-transform: translateZ(0) !important;
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
        -webkit-perspective: 1000px !important;
        perspective: 1000px !important;
        visibility: visible !important;
        opacity: 1 !important;
        width: 56px !important;
        height: 56px !important;
      }
    `;

    // Add the style to the head
    document.head.appendChild(style);

    // Add a periodic check to ensure the button stays visible
    setInterval(function() {
      var button = document.getElementById('universal-install-button');
      if (button) {
        button.style.position = 'fixed';
        button.style.bottom = '20px';
        button.style.right = '20px';
        button.style.zIndex = '999999';
        button.style.visibility = 'visible';
        button.style.opacity = '1';
      }
    }, 1000);
  }
})();
