-- Create or update the gra_credentials table

-- First, check if the table exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'gra_credentials'
  ) THEN
    -- Create the table if it doesn't exist
    CREATE TABLE public.gra_credentials (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      company_name TEXT,
      company_tin TEXT,
      company_security_key TEXT,
      is_test BOOLEAN DEFAULT TRUE,
      is_mock BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Create an index on user_id for faster lookups
    CREATE INDEX idx_gra_credentials_user_id ON public.gra_credentials(user_id);

    -- Enable RLS
    ALTER TABLE public.gra_credentials ENABLE ROW LEVEL SECURITY;

    -- Create a policy to allow users to view their own credentials
    CREATE POLICY "Users can view their own GRA credentials" ON public.gra_credentials
      FOR SELECT
      USING (auth.uid() = user_id);

    -- Create a policy to allow users to update their own credentials
    CREATE POLICY "Users can update their own GRA credentials" ON public.gra_credentials
      FOR UPDATE
      USING (auth.uid() = user_id);

    -- Create a policy to allow users to insert their own credentials
    CREATE POLICY "Users can insert their own GRA credentials" ON public.gra_credentials
      FOR INSERT
      WITH CHECK (auth.uid() = user_id);

    -- Create a policy to allow users to delete their own credentials
    CREATE POLICY "Users can delete their own GRA credentials" ON public.gra_credentials
      FOR DELETE
      USING (auth.uid() = user_id);
  ELSE
    -- If the table exists, check if the is_mock column exists
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'gra_credentials'
      AND column_name = 'is_mock'
    ) THEN
      -- Add the is_mock column if it doesn't exist
      ALTER TABLE public.gra_credentials ADD COLUMN is_mock BOOLEAN DEFAULT TRUE;
    END IF;
  END IF;
END
$$;
