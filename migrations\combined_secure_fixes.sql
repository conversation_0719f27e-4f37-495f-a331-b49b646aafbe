-- Combined secure fixes for invoice payment status and public token issues
-- This script applies all fixes in the correct order

-- 1. First, completely disable <PERSON><PERSON> on the invoices table to fix any issues
-- This is a temporary measure to ensure we can fix the data
ALTER TABLE invoices DISABLE ROW LEVEL SECURITY;

-- Drop all problematic policies that might reference non-existent fields
DROP POLICY IF EXISTS "Allow updating public token fields" ON "invoices";
DROP POLICY IF EXISTS "Allow updating status for public invoices" ON "invoices";
DROP POLICY IF EXISTS "Allow public access to invoices with valid token" ON "invoices";
DROP POLICY IF EXISTS "Users can update their own invoices" ON "invoices";
DROP POLICY IF EXISTS "Organization members can update organization invoices" ON "invoices";
DROP POLICY IF EXISTS "Allow updates with validation" ON "invoices";
DROP POLICY IF EXISTS "Allow extending public token expiration" ON "invoices";

-- Check for any invoices with status 'sent' but showing as 'draft'
-- and fix them directly while <PERSON><PERSON> is disabled
UPDATE invoices
SET status = 'sent'
WHERE status = 'draft'
AND (
    (gra_response IS NOT NULL AND gra_response->>'status' = 'success' AND gra_response->>'invoiceStatusCode' = '02')
    OR
    (gra_invoice_id IS NOT NULL AND gra_invoice_id != '')
);

-- 2. Create a trigger-based approach for invoice updates
-- Create a function to validate invoice updates
CREATE OR REPLACE FUNCTION validate_invoice_update()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if this is a status update for a public invoice
    IF NEW.status != OLD.status AND
       OLD.public_access_token IS NOT NULL AND
       (OLD.public_access_expires_at IS NULL OR OLD.public_access_expires_at > NOW()) THEN
        -- Allow status updates for public invoices
        -- But ensure other fields aren't changed
        NEW.id := OLD.id;
        NEW.user_id := OLD.user_id;
        NEW.organization_id := OLD.organization_id;
        NEW.client_id := OLD.client_id;
        NEW.invoice_number := OLD.invoice_number;
        NEW.issue_date := OLD.issue_date;
        NEW.due_date := OLD.due_date;
        NEW.subtotal := OLD.subtotal;
        NEW.tax_amount := OLD.tax_amount;
        NEW.levy_amount := OLD.levy_amount;
        NEW.discount_type := OLD.discount_type;
        NEW.discount_value := OLD.discount_value;
        NEW.total_amount := OLD.total_amount;
        NEW.currency := OLD.currency;
        NEW.notes := OLD.notes;
        NEW.terms := OLD.terms;
        NEW.public_access_token := OLD.public_access_token;
        NEW.public_access_expires_at := OLD.public_access_expires_at;

        -- Update the updated_at timestamp
        NEW.updated_at := NOW();

        -- If status is being set to 'paid', set paid_at if not already set
        IF NEW.status = 'paid' AND OLD.status != 'paid' THEN
            NEW.paid_at := COALESCE(NEW.paid_at, NOW());
        END IF;

        RETURN NEW;
    END IF;

    -- Check if this is a token expiration update
    IF NEW.public_access_expires_at != OLD.public_access_expires_at AND
       NEW.public_access_token = OLD.public_access_token THEN
        -- Allow token expiration updates for users who own the invoice
        -- or are part of the organization
        IF (auth.uid() = OLD.user_id) OR
           EXISTS (
               SELECT 1 FROM organization_members
               WHERE organization_id = OLD.organization_id
               AND user_id = auth.uid()
           ) THEN
            -- Ensure other fields aren't changed
            NEW.id := OLD.id;
            NEW.user_id := OLD.user_id;
            NEW.organization_id := OLD.organization_id;
            NEW.client_id := OLD.client_id;
            NEW.invoice_number := OLD.invoice_number;
            NEW.issue_date := OLD.issue_date;
            NEW.due_date := OLD.due_date;
            NEW.subtotal := OLD.subtotal;
            NEW.tax_amount := OLD.tax_amount;
            NEW.levy_amount := OLD.levy_amount;
            NEW.discount_type := OLD.discount_type;
            NEW.discount_value := OLD.discount_value;
            NEW.total_amount := OLD.total_amount;
            NEW.currency := OLD.currency;
            NEW.notes := OLD.notes;
            NEW.terms := OLD.terms;
            NEW.status := OLD.status;
            NEW.paid_at := OLD.paid_at;

            -- Update the updated_at timestamp
            NEW.updated_at := NOW();

            RETURN NEW;
        END IF;
    END IF;

    -- For all other updates, let RLS policies handle the permissions
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to validate invoice updates
DROP TRIGGER IF EXISTS validate_invoice_update ON invoices;
CREATE TRIGGER validate_invoice_update
BEFORE UPDATE ON invoices
FOR EACH ROW
EXECUTE FUNCTION validate_invoice_update();

-- 3. Create secure functions for invoice status updates
-- Create a secure function to update invoice status based on payments
CREATE OR REPLACE FUNCTION public.update_invoice_status_if_paid(
  p_invoice_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  has_successful_payment BOOLEAN;
  invoice_updated BOOLEAN := FALSE;
  invoice_owner UUID;
BEGIN
  -- First check if the invoice exists and get the owner
  SELECT user_id INTO invoice_owner
  FROM invoices
  WHERE id = p_invoice_id;

  IF NOT FOUND THEN
    RAISE NOTICE 'Invoice % not found', p_invoice_id;
    RETURN FALSE;
  END IF;

  -- Check if the current user is the owner of the invoice
  -- or if the function is being called in a public context
  IF (auth.uid() IS NOT NULL AND auth.uid() != invoice_owner) THEN
    -- Check if the user is part of the organization that owns the invoice
    IF NOT EXISTS (
      SELECT 1 FROM organization_members
      WHERE organization_id = (SELECT organization_id FROM invoices WHERE id = p_invoice_id)
      AND user_id = auth.uid()
    ) THEN
      RAISE NOTICE 'User % does not have permission to update invoice %', auth.uid(), p_invoice_id;
      RETURN FALSE;
    END IF;
  END IF;

  -- Check if the invoice has any successful payments
  SELECT EXISTS (
    SELECT 1 FROM invoice_payments
    WHERE invoice_id = p_invoice_id AND status = 'successful'
  ) INTO has_successful_payment;

  -- If it has successful payments but is not marked as paid, update it
  IF has_successful_payment THEN
    UPDATE invoices
    SET
      status = 'paid',
      updated_at = NOW(),
      paid_at = COALESCE(paid_at, NOW())
    WHERE id = p_invoice_id AND status != 'paid'
    RETURNING TRUE INTO invoice_updated;

    IF invoice_updated THEN
      RAISE NOTICE 'Updated invoice % status to paid', p_invoice_id;
    END IF;
  END IF;

  -- Return whether the invoice has successful payments
  RETURN has_successful_payment;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.update_invoice_status_if_paid(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_invoice_status_if_paid(UUID) TO anon;

-- Create a secure function to check payment status for public invoices
CREATE OR REPLACE FUNCTION public.check_public_invoice_payment_status(
  p_token UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  invoice_id UUID;
  has_successful_payment BOOLEAN;
  invoice_updated BOOLEAN := FALSE;
BEGIN
  -- First get the invoice ID from the token
  SELECT id INTO invoice_id
  FROM invoices
  WHERE public_access_token = p_token
  AND (public_access_expires_at IS NULL OR public_access_expires_at > NOW());

  IF NOT FOUND THEN
    RAISE NOTICE 'Public invoice with token % not found or expired', p_token;
    RETURN FALSE;
  END IF;

  -- Check if the invoice has any successful payments
  SELECT EXISTS (
    SELECT 1 FROM invoice_payments
    WHERE invoice_id = invoice_id AND status = 'successful'
  ) INTO has_successful_payment;

  -- If it has successful payments but is not marked as paid, update it
  IF has_successful_payment THEN
    UPDATE invoices
    SET
      status = 'paid',
      updated_at = NOW(),
      paid_at = COALESCE(paid_at, NOW())
    WHERE id = invoice_id AND status != 'paid'
    RETURNING TRUE INTO invoice_updated;

    IF invoice_updated THEN
      RAISE NOTICE 'Updated public invoice % status to paid', invoice_id;
    END IF;
  END IF;

  -- Return whether the invoice has successful payments
  RETURN has_successful_payment;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.check_public_invoice_payment_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_public_invoice_payment_status(UUID) TO anon;

-- 4. Create secure functions for token generation and extension
-- Create a secure function to generate a public token
CREATE OR REPLACE FUNCTION public.secure_generate_invoice_public_token(
    p_invoice_id UUID,
    p_expires_in_days INTEGER DEFAULT 90
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    invoice_record RECORD;
    v_token UUID;
    new_expiry TIMESTAMP WITH TIME ZONE;
BEGIN
    -- First check if the invoice exists
    SELECT * INTO invoice_record
    FROM invoices
    WHERE id = p_invoice_id;

    IF NOT FOUND THEN
        RAISE NOTICE 'Invoice not found';
        RETURN NULL;
    END IF;

    -- For security, explicitly check permissions even though we're using SECURITY DEFINER
    -- This ensures only authorized users can generate tokens
    IF auth.uid() IS NOT NULL AND auth.uid() != invoice_record.user_id THEN
        -- Check if the user is part of the organization
        IF NOT EXISTS (
            SELECT 1 FROM organization_members
            WHERE organization_id = invoice_record.organization_id
            AND user_id = auth.uid()
        ) THEN
            RAISE NOTICE 'User does not have permission to update this invoice';
            RETURN NULL;
        END IF;
    END IF;

    -- Generate a new UUID for the token
    v_token := uuid_generate_v4();

    -- Calculate the new expiry date
    new_expiry := NOW() + (p_expires_in_days || ' days')::INTERVAL;

    -- Update the invoice with the new token and expiration date
    UPDATE invoices
    SET
        public_access_token = v_token,
        public_access_expires_at = new_expiry,
        updated_at = NOW()
    WHERE id = p_invoice_id;

    RETURN v_token;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.secure_generate_invoice_public_token(UUID, INTEGER) TO authenticated;

-- Create a secure function to extend public token expiration
CREATE OR REPLACE FUNCTION public.secure_extend_public_token_expiration(
    p_invoice_id UUID,
    p_token UUID,
    p_days_to_extend INTEGER DEFAULT 90
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    invoice_record RECORD;
    new_expiry TIMESTAMP WITH TIME ZONE;
BEGIN
    -- First check if the invoice exists and the token matches
    SELECT * INTO invoice_record
    FROM invoices
    WHERE id = p_invoice_id
    AND public_access_token = p_token;

    IF NOT FOUND THEN
        RAISE NOTICE 'Invoice not found or token does not match';
        RETURN FALSE;
    END IF;

    -- For security, explicitly check permissions even though we're using SECURITY DEFINER
    -- This ensures only authorized users can extend token expiration
    IF auth.uid() IS NOT NULL AND auth.uid() != invoice_record.user_id THEN
        -- Check if the user is part of the organization
        IF NOT EXISTS (
            SELECT 1 FROM organization_members
            WHERE organization_id = invoice_record.organization_id
            AND user_id = auth.uid()
        ) THEN
            -- For public access, we'll allow token extension if the token is valid
            IF invoice_record.public_access_token IS NULL OR
               (invoice_record.public_access_expires_at IS NOT NULL AND
                invoice_record.public_access_expires_at < NOW()) THEN
                RAISE NOTICE 'User does not have permission to update this invoice';
                RETURN FALSE;
            END IF;
        END IF;
    END IF;

    -- Calculate the new expiry date
    new_expiry := COALESCE(invoice_record.public_access_expires_at, NOW()) +
                 (p_days_to_extend || ' days')::INTERVAL;

    -- Update the expiry date
    UPDATE invoices
    SET
        public_access_expires_at = new_expiry,
        updated_at = NOW()
    WHERE id = p_invoice_id
    AND public_access_token = p_token;

    RETURN TRUE;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.secure_extend_public_token_expiration(UUID, UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.secure_extend_public_token_expiration(UUID, UUID, INTEGER) TO anon;

-- First, check if paid_at column exists and add it if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'invoices'
        AND column_name = 'paid_at'
    ) THEN
        ALTER TABLE invoices ADD COLUMN paid_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- 5. Create a secure function specifically for updating invoice status
-- This function will bypass any problematic RLS policies
CREATE OR REPLACE FUNCTION public.secure_update_invoice_status(
    p_invoice_id UUID,
    p_status TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    invoice_record RECORD;
    result JSONB;
BEGIN
    -- First check if the invoice exists
    SELECT * INTO invoice_record
    FROM invoices
    WHERE id = p_invoice_id;

    IF NOT FOUND THEN
        RAISE NOTICE 'Invoice % not found', p_invoice_id;
        RETURN NULL;
    END IF;

    -- For security, explicitly check permissions even though we're using SECURITY DEFINER
    -- This ensures only authorized users can update the status
    IF auth.uid() IS NOT NULL AND auth.uid() != invoice_record.user_id THEN
        -- Check if the user is part of the organization
        IF NOT EXISTS (
            SELECT 1 FROM organization_members
            WHERE organization_id = invoice_record.organization_id
            AND user_id = auth.uid()
        ) THEN
            -- For public access, we'll allow status updates if the token is valid
            IF invoice_record.public_access_token IS NULL OR
               (invoice_record.public_access_expires_at IS NOT NULL AND
                invoice_record.public_access_expires_at < NOW()) THEN
                RAISE NOTICE 'User does not have permission to update this invoice';
                RETURN NULL;
            END IF;
        END IF;
    END IF;

    -- Update the invoice status
    -- Only update the fields we need to avoid the "record new has no field amount" error
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'invoices'
        AND column_name = 'paid_at'
    ) THEN
        -- If paid_at column exists, include it in the update
        UPDATE invoices
        SET
            status = p_status,
            updated_at = NOW(),
            paid_at = CASE WHEN p_status = 'paid' THEN COALESCE(paid_at, NOW()) ELSE paid_at END
        WHERE id = p_invoice_id
        RETURNING to_jsonb(invoices.*) INTO result;
    ELSE
        -- If paid_at column doesn't exist, don't include it
        UPDATE invoices
        SET
            status = p_status,
            updated_at = NOW()
        WHERE id = p_invoice_id
        RETURNING to_jsonb(invoices.*) INTO result;
    END IF;

    RETURN result;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.secure_update_invoice_status(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.secure_update_invoice_status(UUID, TEXT) TO anon;

-- 6. Create secure RLS policies
-- Policy for users to update their own invoices
CREATE POLICY "Users can update their own invoices" ON "invoices"
FOR UPDATE
USING (
    auth.uid() = user_id
);

-- Policy for organization members to update organization invoices
CREATE POLICY "Organization members can update organization invoices" ON "invoices"
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM organization_members
        WHERE organization_id = invoices.organization_id
        AND user_id = auth.uid()
    )
);

-- Policy for public access to update invoice status
CREATE POLICY "Public can update invoice status if they have the token" ON "invoices"
FOR UPDATE
USING (
    public_access_token IS NOT NULL
    AND (
        public_access_expires_at IS NULL
        OR public_access_expires_at > NOW()
    )
);

-- Policy for public access to invoices with valid tokens
CREATE POLICY "Allow public access to invoices with valid token" ON "invoices"
FOR SELECT
USING (
    public_access_token IS NOT NULL
    AND (
        public_access_expires_at IS NULL
        OR public_access_expires_at > NOW()
    )
);

-- Policy for extending token expiration
CREATE POLICY "Allow extending public token expiration" ON "invoices"
FOR UPDATE
USING (
    -- User must own the invoice or be part of the organization
    (auth.uid() = user_id OR
     EXISTS (
        SELECT 1 FROM organization_members
        WHERE organization_id = invoices.organization_id
        AND user_id = auth.uid()
     ))
);

-- 7. Create a function to fix invoices that were sent to GRA but are showing as draft
CREATE OR REPLACE FUNCTION public.fix_sent_invoices_showing_as_draft()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    fixed_count INTEGER := 0;
BEGIN
    -- Update any invoices that have been sent to GRA but are showing as draft
    UPDATE invoices
    SET status = 'sent'
    WHERE status = 'draft'
    AND gra_response IS NOT NULL
    AND gra_response->>'status' = 'success'
    AND gra_response->>'invoiceStatusCode' = '02'
    RETURNING 1 INTO fixed_count;

    -- Also update any invoices that have been sent to GRA but don't have the correct status
    UPDATE invoices
    SET status = 'sent'
    WHERE status = 'draft'
    AND gra_invoice_id IS NOT NULL
    AND gra_invoice_id != ''
    AND fixed_count = 0
    RETURNING 1 INTO fixed_count;

    RETURN fixed_count;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.fix_sent_invoices_showing_as_draft() TO authenticated;

-- Finally, re-enable RLS on the invoices table
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
