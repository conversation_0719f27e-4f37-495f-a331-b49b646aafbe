-- Create a table to track invoices paid by clients before they create an account
CREATE TABLE IF NOT EXISTS public.client_paid_invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  invoice_id UUID NOT NULL REFERENCES public.invoices(id) ON DELETE CASCADE,
  payment_reference TEXT,
  payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  claimed BOOLEAN DEFAULT FALSE,
  claimed_by_user_id UUID REFERENCES auth.users(id),
  claimed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Add a unique constraint to prevent duplicate entries
  CONSTRAINT unique_email_invoice UNIQUE (email, invoice_id)
);

-- Create an index for faster lookups by email
CREATE INDEX IF NOT EXISTS idx_client_paid_invoices_email ON public.client_paid_invoices(email);

-- Create an index for faster lookups by invoice_id
CREATE INDEX IF NOT EXISTS idx_client_paid_invoices_invoice_id ON public.client_paid_invoices(invoice_id);

-- Create RLS policies for the new table
ALTER TABLE public.client_paid_invoices ENABLE ROW LEVEL SECURITY;

-- Allow users to see their own claimed invoices
CREATE POLICY "Users can view their own claimed invoices" ON public.client_paid_invoices
  FOR SELECT
  USING (claimed_by_user_id = auth.uid());

-- Allow users to see unclaimed invoices that match their email
CREATE POLICY "Users can view unclaimed invoices matching their email" ON public.client_paid_invoices
  FOR SELECT
  USING (
    claimed = FALSE AND 
    email = (SELECT email FROM auth.users WHERE id = auth.uid())
  );

-- Allow users to claim unclaimed invoices that match their email
CREATE POLICY "Users can claim unclaimed invoices matching their email" ON public.client_paid_invoices
  FOR UPDATE
  USING (
    claimed = FALSE AND 
    email = (SELECT email FROM auth.users WHERE id = auth.uid())
  )
  WITH CHECK (
    claimed = TRUE AND
    claimed_by_user_id = auth.uid() AND
    claimed_at IS NOT NULL
  );

-- Create a function to claim all unclaimed invoices for a user
CREATE OR REPLACE FUNCTION public.claim_paid_invoices_for_user()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_email TEXT;
  claim_count INTEGER := 0;
BEGIN
  -- Get the current user's email
  SELECT email INTO user_email FROM auth.users WHERE id = auth.uid();
  
  IF user_email IS NULL THEN
    RETURN 0;
  END IF;
  
  -- Update all unclaimed invoices matching the user's email
  WITH updated_rows AS (
    UPDATE public.client_paid_invoices
    SET 
      claimed = TRUE,
      claimed_by_user_id = auth.uid(),
      claimed_at = NOW()
    WHERE 
      email = user_email AND
      claimed = FALSE
    RETURNING id
  )
  SELECT COUNT(*) INTO claim_count FROM updated_rows;
  
  RETURN claim_count;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.claim_paid_invoices_for_user() TO authenticated;

-- Create a function to record a paid invoice for a client
CREATE OR REPLACE FUNCTION public.record_client_paid_invoice(
  p_email TEXT,
  p_invoice_id UUID,
  p_payment_reference TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_id UUID;
BEGIN
  -- Insert the record, handling the case where it might already exist
  INSERT INTO public.client_paid_invoices (email, invoice_id, payment_reference)
  VALUES (p_email, p_invoice_id, p_payment_reference)
  ON CONFLICT (email, invoice_id) 
  DO UPDATE SET 
    payment_reference = p_payment_reference,
    payment_date = NOW()
  RETURNING id INTO v_id;
  
  RETURN v_id;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.record_client_paid_invoice(TEXT, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.record_client_paid_invoice(TEXT, UUID, TEXT) TO anon;
