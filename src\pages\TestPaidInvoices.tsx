import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import TestAddPaidInvoice from '@/components/TestAddPaidInvoice';
import ClientPaidInvoices from '@/components/ClientPaidInvoices';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const TestPaidInvoices: React.FC = () => {
  return (
    <DashboardLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Test Paid Invoices</h1>
            <p className="text-gray-500">
              Testing tool for paid invoices functionality
            </p>
          </div>
          <Link to="/dashboard">
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">Add Test Paid Invoice</h2>
            <TestAddPaidInvoice />
          </div>
          
          <div>
            <h2 className="text-xl font-semibold mb-4">Your Paid Invoices</h2>
            <ClientPaidInvoices />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default TestPaidInvoices;
