
import React from 'react';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { Link } from 'react-router-dom';

interface PricingTierProps {
  name: string;
  price: string;
  description: string;
  features: string[];
  buttonText: string;
  buttonVariant: 'default' | 'outline';
  popular?: boolean;
  className?: string;
}

const PricingTier: React.FC<PricingTierProps> = ({
  name,
  price,
  description,
  features,
  buttonText,
  buttonVariant,
  popular = false,
  className = '',
}) => {
  return (
    <div className={`bg-white dark:bg-card rounded-xl shadow-sm border ${popular ? 'border-ghana-green ring-2 ring-ghana-green-light dark:ring-ghana-green/50' : 'border-gray-100 dark:border-border'} overflow-hidden ${className}`}>
      {popular && (
        <div className="bg-ghana-green text-white text-center py-2 text-sm font-semibold">
          Most Popular
        </div>
      )}

      <div className="p-6 md:p-8">
        <h3 className="text-lg font-bold mb-2 dark:text-foreground">{name}</h3>
        <div className="mb-4">
          <span className="text-3xl font-bold dark:text-foreground">{price}</span>
          {price !== 'Free' && <span className="text-gray-500 dark:text-gray-400">/month</span>}
        </div>
        <p className="text-gray-500 dark:text-gray-400 mb-6">{description}</p>

        <Link to="/auth">
          <Button
            variant={buttonVariant}
            className="w-full mb-6 bg-ghana-green hover:bg-ghana-green-light text-white"
          >
            {buttonText}
          </Button>
        </Link>

        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-2">
              <Check className="h-5 w-5 text-ghana-green dark:text-ghana-green/90 flex-shrink-0 mt-0.5" />
              <span className="text-sm dark:text-gray-300">{feature}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

const Pricing: React.FC = () => {
  return (
    <section id="pricing" className="py-16 md:py-24">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold font-display mb-4 dark:text-foreground">Simple, Transparent Pricing</h2>
          <p className="text-gray-600 dark:text-gray-300">Choose the plan that fits your business needs. All plans include GRA VAT compliance.</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
          <PricingTier
            name="Free"
            price="₵0"
            description="For individuals just getting started."
            features={[
              "Up to 5 invoices per month",
              "Up to 3 clients",
              "GRA VAT-compliant invoices",
              "Basic dashboard",
              "PDF downloads",
              "Email support"
            ]}
            buttonText="Get Started"
            buttonVariant="default"
          />

          <PricingTier
            name="Freelancer"
            price="₵49"
            description="Perfect for independent professionals."
            features={[
              "Up to 20 invoices per month",
              "Unlimited clients",
              "GRA VAT-compliant invoices",
              "GRA E-VAT API integration",
              "Advanced dashboard",
              "PDF downloads",
              "Email support"
            ]}
            buttonText="Sign Up Now"
            buttonVariant="default"
          />

          <PricingTier
            name="Business"
            price="₵99"
            description="For growing businesses with regular invoicing needs."
            features={[
              "Up to 50 invoices per month",
              "Unlimited clients",
              "GRA VAT-compliant invoices",
              "GRA E-VAT API integration",
              "Advanced dashboard",
              "Client management",
              "Paystack payment integration",
              "Priority support",
              "Team member (1)"
            ]}
            buttonText="Sign Up Now"
            buttonVariant="default"
            popular={true}
            className="md:-translate-y-4"
          />

          <PricingTier
            name="Enterprise"
            price="₵199"
            description="For established businesses with multiple team members."
            features={[
              "Unlimited invoices",
              "Unlimited clients",
              "GRA VAT-compliant invoices",
              "GRA E-VAT API integration",
              "Advanced reporting & analytics",
              "Team roles & permissions",
              "Paystack payment integration",
              "API access",
              "Phone support",
              "Team members (5)"
            ]}
            buttonText="Contact Sales"
            buttonVariant="default"
          />
        </div>

        <div className="mt-16 text-center">
          <p className="text-gray-500 dark:text-gray-400 mb-6">Need a custom plan for your large business?</p>
          <Button
            variant="outline"
            className="border-2 border-ghana-green text-ghana-green hover:bg-ghana-green hover:text-white px-6 py-2 dark:border-ghana-green/80 dark:text-ghana-gold dark:hover:bg-ghana-green/80"
          >
            Contact Our Sales Team
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
