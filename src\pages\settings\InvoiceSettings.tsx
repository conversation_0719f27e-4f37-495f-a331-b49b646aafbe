import React, { useEffect, useState } from 'react';
import { useSession } from '@/contexts/SessionContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, Lock, AlertTriangle } from 'lucide-react';
import { getInvoiceSettings, updateInvoiceSettings } from '@/services/invoiceSettingsService';
import { getRobustGraCredentials, saveRobustGraCredentials } from '@/services/graCredentialsService';

const InvoiceSettings: React.FC = () => {
  const { user } = useSession();
  const { canAccessFeature, tier } = useSubscription();
  const [settings, setSettings] = useState<any>({
    gra_mode_enabled: false,
    default_currency: 'GHS',
    auto_send_reminders: true,
    payment_terms_days: 14
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [graCredentials, setGraCredentials] = useState({
    company_tin: '',
    company_name: '',
    company_security_key: '',
    is_test: true
  });
  const [hasGraCredentials, setHasGraCredentials] = useState(false);

  // Check if user has access to GRA features
  const hasGraAccess = canAccessFeature('gra_evat_api');

  // Load settings and GRA credentials
  useEffect(() => {
    const loadData = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // Load invoice settings
        const invoiceSettings = await getInvoiceSettings(user.id);
        setSettings(invoiceSettings);

        // Check if user has GRA credentials
        const credentials = await getRobustGraCredentials(user.id);
        if (credentials) {
          setGraCredentials({
            company_tin: credentials.company_tin || '',
            company_name: credentials.company_name || '',
            company_security_key: credentials.company_security_key || '',
            is_test: credentials.is_test !== false
          });
          setHasGraCredentials(true);
        }
      } catch (error) {
        console.error('Error loading settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to load settings',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [user]);

  // Handle settings change
  const handleSettingsChange = async () => {
    if (!user) return;

    setIsSaving(true);
    try {
      const success = await updateInvoiceSettings(user.id, settings);
      if (success) {
        toast({
          title: 'Success',
          description: 'Invoice settings updated successfully',
          variant: 'default'
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update settings',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update settings',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle GRA credentials change
  const handleGraCredentialsChange = async () => {
    if (!user) return;

    setIsSaving(true);
    try {
      // Validate credentials
      if (!graCredentials.company_tin || !graCredentials.company_name || !graCredentials.company_security_key) {
        toast({
          title: 'Validation Error',
          description: 'All fields are required',
          variant: 'destructive'
        });
        return;
      }

      // Save using our robust function that handles errors and fallbacks
      const success = await saveRobustGraCredentials({
        user_id: user.id,
        company_tin: graCredentials.company_tin,
        company_name: graCredentials.company_name,
        company_security_key: graCredentials.company_security_key,
        is_test: graCredentials.is_test
      });

      if (success) {
        setHasGraCredentials(true);
        toast({
          title: 'Success',
          description: 'GRA credentials saved successfully',
          variant: 'default'
        });
      } else {
        toast({
          title: 'Warning',
          description: 'Failed to save GRA credentials to database, but they were saved to local storage as a backup.',
          variant: 'default'
        });
      }
    } catch (error) {
      console.error('Error saving GRA credentials:', error);
      toast({
        title: 'Error',
        description: 'Failed to save GRA credentials',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-ghana-green" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Invoice Mode</CardTitle>
          <CardDescription>
            Choose between standard invoices or GRA-compliant invoices
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {hasGraAccess ? (
            <>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">GRA-Compliant Mode</h3>
                  <p className="text-sm text-gray-500">
                    Enable this to generate GRA-compliant invoices with official signatures and QR codes
                  </p>
                </div>
                <Switch
                  checked={settings.gra_mode_enabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, gra_mode_enabled: checked })}
                />
              </div>

              <div className="pt-4">
                <Button onClick={handleSettingsChange} disabled={isSaving}>
                  {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Save Settings
                </Button>
              </div>
            </>
          ) : (
            <>
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Subscription Required</AlertTitle>
                <AlertDescription>
                  GRA-compliant invoices are only available on Freelancer, Business, and Enterprise plans.
                </AlertDescription>
              </Alert>

              <div className="flex items-center justify-between opacity-60">
                <div>
                  <h3 className="text-lg font-medium">GRA-Compliant Mode</h3>
                  <p className="text-sm text-gray-500">
                    Enable this to generate GRA-compliant invoices with official signatures and QR codes
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-gray-500" />
                  <Switch disabled checked={false} />
                </div>
              </div>

              <div className="pt-4">
                <Button
                  onClick={() => window.location.href = '/subscription-management'}
                  className="bg-ghana-green hover:bg-ghana-green/90"
                >
                  Upgrade Subscription
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {settings.gra_mode_enabled && hasGraAccess && (
        <Card>
          <CardHeader>
            <CardTitle>GRA Credentials</CardTitle>
            <CardDescription>
              {hasGraCredentials
                ? 'Your GRA credentials are set up. You can update them below.'
                : 'Enter your GRA credentials to enable GRA-compliant invoices'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                If you encounter errors saving your GRA credentials, please try the following:
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Make sure all fields are filled correctly</li>
                  <li>Try refreshing the page and trying again</li>
                  <li>Your credentials will also be saved to local storage as a backup</li>
                </ul>
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Label htmlFor="company_tin">Company TIN</Label>
              <Input
                id="company_tin"
                value={graCredentials.company_tin}
                onChange={(e) => setGraCredentials({ ...graCredentials, company_tin: e.target.value })}
                placeholder="Enter your company TIN"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="company_name">Company Name</Label>
              <Input
                id="company_name"
                value={graCredentials.company_name}
                onChange={(e) => setGraCredentials({ ...graCredentials, company_name: e.target.value })}
                placeholder="Enter your company name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="company_security_key">Security Key</Label>
              <Input
                id="company_security_key"
                value={graCredentials.company_security_key}
                onChange={(e) => setGraCredentials({ ...graCredentials, company_security_key: e.target.value })}
                placeholder="Enter your security key"
                type="password"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="is_test">Environment</Label>
              <Select
                value={graCredentials.is_test ? 'test' : 'production'}
                onValueChange={(value) => setGraCredentials({ ...graCredentials, is_test: value === 'test' })}
              >
                <SelectTrigger id="is_test">
                  <SelectValue placeholder="Select environment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="test">Test Environment</SelectItem>
                  <SelectItem value="production">Production Environment</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="pt-4">
              <Button onClick={handleGraCredentialsChange} disabled={isSaving}>
                {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Save GRA Credentials
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Invoice Defaults</CardTitle>
          <CardDescription>
            Set default values for your invoices
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="default_currency">Default Currency</Label>
            <Select
              value={settings.default_currency}
              onValueChange={(value) => setSettings({ ...settings, default_currency: value })}
            >
              <SelectTrigger id="default_currency">
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="GHS">Ghana Cedi (GHS)</SelectItem>
                <SelectItem value="USD">US Dollar (USD)</SelectItem>
                <SelectItem value="EUR">Euro (EUR)</SelectItem>
                <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                <SelectItem value="NGN">Nigerian Naira (NGN)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="payment_terms_days">Default Payment Terms (Days)</Label>
            <Input
              id="payment_terms_days"
              type="number"
              value={settings.payment_terms_days}
              onChange={(e) => setSettings({ ...settings, payment_terms_days: parseInt(e.target.value) || 0 })}
            />
          </div>

          <div className="flex items-center space-x-2 pt-2">
            <Switch
              id="auto_send_reminders"
              checked={settings.auto_send_reminders}
              onCheckedChange={(checked) => setSettings({ ...settings, auto_send_reminders: checked })}
            />
            <Label htmlFor="auto_send_reminders">Automatically send payment reminders</Label>
          </div>

          <div className="pt-4">
            <Button onClick={handleSettingsChange} disabled={isSaving}>
              {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Save Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default InvoiceSettings;
