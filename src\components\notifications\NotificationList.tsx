import React from 'react';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { Check, Trash2, Bell, CreditCard, FileText, AlertTriangle, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNotifications, NotificationType } from '@/contexts/NotificationContext';
import { Skeleton } from '@/components/ui/skeleton';

const NotificationList: React.FC = () => {
  const { notifications, isLoading, markAsRead, deleteNotification } = useNotifications();
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="p-2 space-y-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-start space-x-4 p-3">
            <Skeleton className="h-8 w-8 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-full" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className="py-6 px-4 text-center">
        <Bell className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground">No notifications yet</p>
        {isLoading && (
          <p className="text-xs text-muted-foreground mt-2">Loading notifications...</p>
        )}
      </div>
    );
  }

  // Get notification icon based on type
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'invoice_created':
      case 'invoice_sent':
      case 'invoice_paid':
      case 'invoice_overdue':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'payment_received':
        return <CreditCard className="h-5 w-5 text-green-500" />;
      case 'subscription_renewal':
      case 'subscription_expiring':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'system_message':
      case 'feature_update':
      default:
        return <Info className="h-5 w-5 text-ghana-green" />;
    }
  };

  const handleNotificationClick = (notification: any) => {
    markAsRead(notification.id);
    if (notification.link) {
      navigate(notification.link);
    }
  };

  return (
    <div className="py-1">
      {notifications.slice(0, 10).map((notification) => (
        <div
          key={notification.id}
          className={`flex items-start p-3 hover:bg-muted/50 transition-colors ${!notification.is_read ? 'bg-muted/30' : ''}`}
        >
          <div className="flex-shrink-0 mr-3 mt-0.5">
            {getNotificationIcon(notification.notification_type as NotificationType)}
          </div>
          <div
            className="flex-1 cursor-pointer"
            onClick={() => handleNotificationClick(notification)}
          >
            <p className={`text-sm ${!notification.is_read ? 'font-medium' : ''}`}>
              {notification.title}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {notification.message}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
            </p>
          </div>
          <div className="flex flex-col space-y-1 ml-2">
            {!notification.is_read && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={(e) => {
                  e.stopPropagation();
                  markAsRead(notification.id);
                }}
              >
                <Check className="h-4 w-4" />
                <span className="sr-only">Mark as read</span>
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                deleteNotification(notification.id);
              }}
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">Delete</span>
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default NotificationList;
