import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, X, ChevronRight, Share2, Plus, Menu, MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
// Import CSS directly
import './pwa-install.css';

type Platform = 'ios-safari' | 'ios-chrome' | 'android' | 'desktop' | 'other';

const PWAInstallPrompt: React.FC = () => {
  const [installPrompt, setInstallPrompt] = useState<any>(null);
  const [showButton, setShowButton] = useState<boolean>(false);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [platform, setPlatform] = useState<Platform>('other');
  const [isInstalled, setIsInstalled] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    try {
      // Check if already installed
      if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
        return;
      }

      // Check if user has dismissed the prompt recently
      const dismissedTime = parseInt(localStorage.getItem('pwa-prompt-dismissed-time') || '0', 10);
      const now = Date.now();

      // If dismissed less than 3 days ago, don't show
      if (now - dismissedTime < 3 * 24 * 60 * 60 * 1000) {
        return;
      }

      // Detect platform safely
      let detectedPlatform: Platform = 'other';
      try {
        const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera || '';

        // Check if device is iOS
        const isIOS = /iPad|iPhone|iPod/i.test(userAgent) && !(window as any).MSStream;

        if (isIOS) {
          // Check if browser is Chrome on iOS
          if (/CriOS/i.test(userAgent)) {
            detectedPlatform = 'ios-chrome';
          } else {
            // Default iOS browser (Safari)
            detectedPlatform = 'ios-safari';
          }

          // For iOS, check if the user has visited the site multiple times
          const visitCount = parseInt(localStorage.getItem('pwa-visit-count') || '0', 10);
          localStorage.setItem('pwa-visit-count', (visitCount + 1).toString());

          // Show the button after 2 visits
          if (visitCount >= 1) {
            setTimeout(() => {
              setShowButton(true);
            }, 3000);
          }
        } else if (/Android/i.test(userAgent)) {
          detectedPlatform = 'android';
        } else if (!/Mobile|Android|iP(ad|hone|od)/i.test(userAgent)) {
          detectedPlatform = 'desktop';
        }
        setPlatform(detectedPlatform);
      } catch (e) {
        console.warn('Error detecting platform:', e);
        // Default to 'other' if platform detection fails
        setPlatform('other');
      }

      // Listen for beforeinstallprompt event (Chrome, Edge, etc.)
      const handleBeforeInstallPrompt = (e: Event) => {
        try {
          // Prevent Chrome 67 and earlier from automatically showing the prompt
          e.preventDefault();
          // Stash the event so it can be triggered later
          setInstallPrompt(e);

          // Show the install button after a short delay
          setTimeout(() => {
            setShowButton(true);
          }, 3000);
        } catch (err) {
          console.error('Error in beforeinstallprompt handler:', err);
        }
      };

      // Only add event listener if it's supported
      if (window.addEventListener) {
        window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt as any);
      }

      // Listen for app installed event
      const handleAppInstalled = () => {
        try {
          setIsInstalled(true);
          setShowButton(false);
          setShowModal(false);
          localStorage.setItem('pwa-installed', 'true');
        } catch (err) {
          console.error('Error in appinstalled handler:', err);
        }
      };

      // Only add event listener if it's supported
      if (window.addEventListener) {
        window.addEventListener('appinstalled', handleAppInstalled);
      }

      return () => {
        // Only remove event listeners if they were added
        if (window.removeEventListener) {
          window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt as any);
          window.removeEventListener('appinstalled', handleAppInstalled);
        }
      };
    } catch (err) {
      console.error('Error in PWAInstallPrompt useEffect:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      return () => {}; // Return empty cleanup function
    }
  }, []);

  const handleInstall = async () => {
    try {
      // Always show the modal first for all platforms
      console.log('Install button clicked, showing modal');
      setShowModal(true);

      // Check for iOS devices using multiple detection methods
      const isIOS =
        platform === 'ios-safari' ||
        platform === 'ios-chrome' ||
        /iPad|iPhone|iPod/i.test(navigator.userAgent) ||
        (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) ||
        (navigator.userAgent.includes("Mac") && "ontouchend" in document);

      if (isIOS) {
        console.log('iOS device detected, showing iOS-specific guide');
        // Modal is already showing, no need to set it again
        return;
      }

      // If we have an install prompt, we can also try to use it
      if (installPrompt) {
        try {
          console.log('Native install prompt available, showing it');
          // Show the install prompt
          await installPrompt.prompt();

          // Wait for the user to respond to the prompt
          const { outcome } = await installPrompt.userChoice;
          console.log(`User response to the install prompt: ${outcome}`);

          // Clear the saved prompt
          setInstallPrompt(null);

          if (outcome === 'accepted') {
            setIsInstalled(true);
            setShowButton(false);
            localStorage.setItem('pwa-installed', 'true');
            // Close the modal if installation was successful
            setShowModal(false);
          }
        } catch (error) {
          console.error('Error during installation:', error);
          // Modal is already showing, so no need to set it again
        }
      } else {
        console.log('No native install prompt available, showing modal only');
      }
    } catch (err) {
      console.error('Error in handleInstall:', err);
      // Fallback to showing the modal with instructions
      setShowModal(true);
    }
  };

  const handleDismiss = () => {
    try {
      setShowButton(false);
      setShowModal(false);
      localStorage.setItem('pwa-prompt-dismissed-time', Date.now().toString());
    } catch (err) {
      console.error('Error in handleDismiss:', err);
      // Just hide the UI elements if localStorage fails
      setShowButton(false);
      setShowModal(false);
    }
  };

  // Don't render anything if there's an error or the app is already installed
  if (error || isInstalled) {
    return null;
  }

  // Safely check if we're in a browser environment
  if (typeof window === 'undefined') {
    return null;
  }

  // Check if this is an iOS device
  const isIOSDevice =
    platform === 'ios-safari' ||
    platform === 'ios-chrome' ||
    /iPad|iPhone|iPod/i.test(navigator.userAgent) ||
    (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) ||
    (navigator.userAgent.includes("Mac") && "ontouchend" in document);

  // Add console logs for debugging
  console.log('PWAInstallPrompt rendering, showButton:', showButton, 'isIOSDevice:', isIOSDevice);

  // Don't render on iOS devices (we'll use IOSInstallButton instead)
  if (isIOSDevice) {
    console.log('iOS device detected, not showing PWAInstallPrompt');
    return null;
  }

  try {
    return (
      <>
        {/* Sticky Install Button */}
        <div className="pwa-install-container">
          {showButton && (
            <div
              className="pwa-install-button"
              style={{
                position: 'fixed',
                bottom: '1.5rem',
                right: '1.5rem',
                zIndex: 99999
              }}
            >
              <Button
                onClick={handleInstall}
                className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900 rounded-full shadow-lg pwa-install-fab"
                aria-label="Install App"
              >
                <Download className="h-5 w-5 mr-1" />
                <span>Install App</span>
              </Button>
            </div>
          )}
        </div>

      {/* Installation Instructions Modal */}
      {/* Use a custom modal implementation for iOS compatibility */}
      {showModal && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-[999999] p-4"
          style={{
            WebkitBackdropFilter: 'blur(4px)',
            backdropFilter: 'blur(4px)'
          }}
          onClick={() => setShowModal(false)}
        >
          <div
            className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden max-w-md w-full"
            style={{
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
              maxWidth: '95vw',
              width: '450px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
          <div className="pwa-install-modal-header">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-white/20 p-2 rounded-full">
                  <img
                    src="/logo.png"
                    alt="Payvoicer Logo"
                    className="h-6 w-6 object-contain"
                    onError={(e) => {
                      // Fallback to icon if logo doesn't load
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        const icon = document.createElement('span');
                        icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-white dark:text-gray-900"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>';
                        parent.appendChild(icon);
                      }
                    }}
                  />
                </div>
                <h3 className="font-bold text-white dark:text-gray-900 text-lg">Install Payvoicer</h3>
              </div>
              <button
                onClick={handleDismiss}
                className="text-white/70 hover:text-white dark:text-gray-900/70 dark:hover:text-gray-900 transition-colors"
                aria-label="Close"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="p-6">
            {(platform === 'ios-safari' || platform === 'ios-chrome' ||
              /iPad|iPhone|iPod/i.test(navigator.userAgent) ||
              (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) ||
              (navigator.userAgent.includes("Mac") && "ontouchend" in document)) && (
              <div className="space-y-6 pwa-install-ios">
                <p className="text-gray-700 dark:text-gray-300 text-center font-medium text-lg">
                  Install Payvoicer on your iOS device
                </p>
                <p className="text-gray-500 dark:text-gray-400 text-center text-sm -mt-4">
                  iOS doesn't support automatic installation, please follow these steps:
                </p>

                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center mb-6">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0">
                      <span className="text-blue-600 dark:text-blue-300 font-bold text-lg">1</span>
                    </div>
                    <div className="ml-4">
                      <p className="font-medium text-base">
                        Tap the {platform === 'ios-chrome' ? 'Menu' : 'Share'} icon
                        {platform === 'ios-safari' || !platform ?
                          <Share2 className="h-5 w-5 text-blue-500 inline-block ml-2 align-text-bottom" /> :
                          <MoreVertical className="h-5 w-5 text-blue-500 inline-block ml-2 align-text-bottom" />}
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                        {platform === 'ios-chrome' ? 'Located at the top-right of your browser' : 'Located at the bottom of your browser'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center mb-6">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0">
                      <span className="text-blue-600 dark:text-blue-300 font-bold text-lg">2</span>
                    </div>
                    <div className="ml-4">
                      <p className="font-medium text-base">
                        {platform === 'ios-chrome' ? 'Tap "Share..." then' : 'Scroll down and tap'} "Add to Home Screen"
                        <Plus className="h-5 w-5 text-gray-500 inline-block ml-2 align-text-bottom" />
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                        You may need to scroll down to find this option
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0">
                      <span className="text-blue-600 dark:text-blue-300 font-bold text-lg">3</span>
                    </div>
                    <div className="ml-4">
                      <p className="font-medium text-base">
                        Tap "Add" in the top right corner
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                        The app will be added to your home screen
                      </p>
                    </div>
                  </div>
                </div>

                <div className="pt-6 flex justify-center">
                  <Button
                    onClick={handleDismiss}
                    className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900 px-12 py-3 text-base font-medium rounded-full shadow-md hover:shadow-lg transition-all"
                  >
                    Got it
                  </Button>
                </div>
              </div>
            )}

            {platform === 'android' && (
              <div className="space-y-6 pwa-install-android">
                <p className="text-gray-700 dark:text-gray-300">
                  Install Payvoicer on your Android device:
                </p>

                <div className="space-y-4">
                  <div className="pwa-install-step">
                    <div className="pwa-install-step-number">
                      <span>1</span>
                    </div>
                    <div className="pwa-install-step-content">
                      <p className="pwa-install-step-title">Tap the Menu button</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Menu className="h-6 w-6 text-gray-500" />
                        <span className="pwa-install-step-description">Tap the menu button in your browser</span>
                      </div>
                    </div>
                  </div>

                  <div className="pwa-install-step">
                    <div className="pwa-install-step-number">
                      <span>2</span>
                    </div>
                    <div className="pwa-install-step-content">
                      <p className="pwa-install-step-title">Tap "Install app" or "Add to Home screen"</p>
                      <span className="pwa-install-step-description">Look for the install option in the menu</span>
                    </div>
                  </div>
                </div>

                <div className="pt-4 flex justify-end">
                  <Button
                    onClick={handleDismiss}
                    className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900"
                  >
                    Got it
                  </Button>
                </div>
              </div>
            )}

            {platform === 'desktop' && (
              <div className="space-y-6 pwa-install-desktop">
                <p className="text-gray-700 dark:text-gray-300">
                  Install Payvoicer on your computer:
                </p>

                <div className="space-y-4">
                  <div className="pwa-install-step">
                    <div className="pwa-install-step-number">
                      <span>1</span>
                    </div>
                    <div className="pwa-install-step-content">
                      <p className="pwa-install-step-title">Click the install icon in the address bar</p>
                      <span className="pwa-install-step-description">Look for the install icon at the right side of the address bar</span>
                    </div>
                  </div>

                  <div className="pwa-install-step">
                    <div className="pwa-install-step-number">
                      <span>2</span>
                    </div>
                    <div className="pwa-install-step-content">
                      <p className="pwa-install-step-title">Click "Install"</p>
                      <span className="pwa-install-step-description">Follow the installation prompts</span>
                    </div>
                  </div>
                </div>

                <div className="pt-4 flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={handleDismiss}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700/50"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleInstall}
                    className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900"
                  >
                    Install
                  </Button>
                </div>
              </div>
            )}

            {platform === 'other' && (
              <div className="space-y-6">
                <p className="text-gray-700 dark:text-gray-300">
                  Install Payvoicer for offline access and a better experience:
                </p>

                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <ul className="space-y-3">
                    <li className="flex items-start gap-2">
                      <ChevronRight className="h-4 w-4 text-ghana-green dark:text-ghana-gold mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">Access invoices even when offline</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <ChevronRight className="h-4 w-4 text-ghana-green dark:text-ghana-gold mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">Faster loading and better performance</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <ChevronRight className="h-4 w-4 text-ghana-green dark:text-ghana-gold mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">Launch directly from your home screen</span>
                    </li>
                  </ul>
                </div>

                <div className="pt-4 flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={handleDismiss}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700/50"
                  >
                    Not Now
                  </Button>
                  <Button
                    onClick={handleInstall}
                    className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900"
                  >
                    Install App
                  </Button>
                </div>
              </div>
            )}
          </div>
          </div>
        </div>
      )}
    </>
  );
  } catch (err) {
    console.error('Error rendering PWAInstallPrompt:', err);
    return null;
  }
};

export default PWAInstallPrompt;
