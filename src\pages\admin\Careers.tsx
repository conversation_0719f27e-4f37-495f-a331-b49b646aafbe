import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Briefcase,
  MapPin,
  DollarSign,
  Clock
} from 'lucide-react';
import { useSession } from '@/contexts/SessionContext';
import { supabase } from '@/integrations/supabase/client';
import { isAdmin, isAdminEmail } from '@/utils/adminUtils';
import { getTableColumns } from '@/utils/databaseUtils';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface JobListing {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  location?: string;
  salary_range?: string;
  employment_type?: string;
  department?: string;
  status: 'draft' | 'published' | 'closed';
  created_at: string;
  updated_at: string;
}

interface JobFormData {
  title: string;
  description: string;
  requirements: string;
  location: string;
  salary_range: string;
  employment_type: string;
  department: string;
  status: 'draft' | 'published' | 'closed';
}

const CareersAdminPage = () => {
  const navigate = useNavigate();
  const { session, user } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [jobListings, setJobListings] = useState<JobListing[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedJob, setSelectedJob] = useState<JobListing | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [formData, setFormData] = useState<JobFormData>({
    title: '',
    description: '',
    requirements: '',
    location: '',
    salary_range: '',
    employment_type: 'full-time',
    department: '',
    status: 'draft'
  });

  useEffect(() => {
    const checkAdminAndFetchJobs = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!session || !user) {
          setIsLoading(false);
          setError('You must be logged in to access the admin dashboard.');
          return;
        }

        const userId = session.user.id;
        const userEmail = user.email || '';

        // Check if user is admin
        try {
          const adminStatus = await isAdmin(userId, userEmail);

          if (adminStatus) {
            setIsAuthorized(true);
            fetchJobListings();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        } catch (adminError) {
          console.error('Error checking admin status:', adminError);

          if (isAdminEmail(userEmail)) {
            setIsAuthorized(true);
            fetchJobListings();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error in admin verification:', error);
        setIsAuthorized(false);
        setError('An error occurred while verifying admin permissions.');
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAndFetchJobs();
  }, [session, user, navigate]);

  const fetchJobListings = async () => {
    try {
      const { data: jobsData, error: jobsError } = await supabase
        .from('job_listings')
        .select('*')
        .order('created_at', { ascending: false });

      if (jobsError) {
        console.error('Error fetching job listings:', jobsError);
        setError('Error fetching job listings.');
        return;
      }

      const transformedJobs = jobsData?.map(job => ({
        id: job.id,
        title: job.title || '',
        description: job.description || '',
        requirements: job.requirements || '',
        location: job.location || '',
        salary_range: job.salary_range || '',
        employment_type: job.employment_type || '',
        department: job.department || '',
        status: job.status || 'draft',
        created_at: job.created_at || '',
        updated_at: job.updated_at || ''
      })) || [];

      setJobListings(transformedJobs);
    } catch (error) {
      console.error('Error fetching job listings:', error);
      setError('An error occurred while fetching job listings.');
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const filteredJobs = jobListings.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.department?.toLowerCase().includes(searchQuery.toLowerCase());

    if (activeTab === 'all') return matchesSearch;
    return matchesSearch && job.status === activeTab;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      published: 'default',
      closed: 'outline'
    } as const;

    return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>{status}</Badge>;
  };

  const createJobListing = async () => {
    try {
      // Get the actual table columns
      const tableColumns = await getTableColumns('job_listings');
      console.log('Available job_listings columns:', tableColumns);

      if (tableColumns.length === 0) {
        setError('Unable to determine table structure. Please check if the job_listings table exists.');
        return;
      }

      // Create insert data with available columns
      const insertData: any = {};

      if (tableColumns.includes('title')) insertData.title = formData.title;
      if (tableColumns.includes('description')) insertData.description = formData.description;
      if (tableColumns.includes('requirements') && formData.requirements) insertData.requirements = formData.requirements;
      if (tableColumns.includes('location') && formData.location) insertData.location = formData.location;
      if (tableColumns.includes('salary_range') && formData.salary_range) insertData.salary_range = formData.salary_range;
      if (tableColumns.includes('employment_type') && formData.employment_type) insertData.employment_type = formData.employment_type;
      if (tableColumns.includes('department') && formData.department) insertData.department = formData.department;
      if (tableColumns.includes('status')) insertData.status = formData.status;

      console.log('Job listing insert data:', insertData);

      const { error } = await supabase
        .from('job_listings')
        .insert(insertData);

      if (error) {
        console.error('Error creating job listing:', error);
        setError(`Error creating job listing: ${error.message}`);
        return;
      }

      setFormData({
        title: '',
        description: '',
        requirements: '',
        location: '',
        salary_range: '',
        employment_type: 'full-time',
        department: '',
        status: 'draft'
      });
      setIsCreateDialogOpen(false);
      fetchJobListings();
    } catch (error) {
      console.error('Error creating job listing:', error);
      setError('An error occurred while creating the job listing.');
    }
  };

  const deleteJobListing = async (id: string) => {
    try {
      const { error } = await supabase
        .from('job_listings')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting job listing:', error);
        setError('Error deleting job listing.');
        return;
      }

      setIsDeleteDialogOpen(false);
      setSelectedJob(null);
      fetchJobListings();
    } catch (error) {
      console.error('Error deleting job listing:', error);
      setError('An error occurred while deleting the job listing.');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!isAuthorized) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-5">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Briefcase className="h-6 w-6" />
            <h1 className="text-3xl font-bold tracking-tight">Job Listings</h1>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Job
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Career Opportunities</CardTitle>
            <CardDescription>
              Manage job postings and career opportunities at your company.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <div className="flex items-center justify-between">
                <TabsList>
                  <TabsTrigger value="all">All Jobs</TabsTrigger>
                  <TabsTrigger value="draft">Drafts</TabsTrigger>
                  <TabsTrigger value="published">Published</TabsTrigger>
                  <TabsTrigger value="closed">Closed</TabsTrigger>
                </TabsList>
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search jobs..."
                    value={searchQuery}
                    onChange={handleSearch}
                    className="w-64"
                  />
                </div>
              </div>

              <TabsContent value={activeTab} className="space-y-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredJobs.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8">
                            No job listings found.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredJobs.map((job) => (
                          <TableRow key={job.id}>
                            <TableCell className="font-medium">
                              <div>
                                <div className="font-medium">{job.title}</div>
                                <div className="text-sm text-muted-foreground truncate max-w-xs">
                                  {job.description.substring(0, 100)}...
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {job.department ? (
                                <Badge variant="outline">{job.department}</Badge>
                              ) : (
                                <span className="text-muted-foreground">Not specified</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {job.location ? (
                                <div className="flex items-center">
                                  <MapPin className="mr-2 h-4 w-4" />
                                  {job.location}
                                </div>
                              ) : (
                                <span className="text-muted-foreground">Remote</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {job.employment_type ? (
                                <div className="flex items-center">
                                  <Clock className="mr-2 h-4 w-4" />
                                  {job.employment_type}
                                </div>
                              ) : (
                                <span className="text-muted-foreground">Not specified</span>
                              )}
                            </TableCell>
                            <TableCell>{getStatusBadge(job.status)}</TableCell>
                            <TableCell>
                              {new Date(job.created_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedJob(job);
                                    setIsViewDialogOpen(true);
                                  }}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedJob(job);
                                    setIsEditDialogOpen(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setSelectedJob(job);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Create Job Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Job Listing</DialogTitle>
            <DialogDescription>
              Create a new job posting for your company.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Job Title</Label>
              <Input
                id="title"
                placeholder="Enter job title..."
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="department">Department</Label>
                <Input
                  id="department"
                  placeholder="e.g., Engineering, Marketing..."
                  value={formData.department}
                  onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  placeholder="e.g., Remote, New York, London..."
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="employment_type">Employment Type</Label>
                <select
                  id="employment_type"
                  value={formData.employment_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, employment_type: e.target.value }))}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="full-time">Full-time</option>
                  <option value="part-time">Part-time</option>
                  <option value="contract">Contract</option>
                  <option value="internship">Internship</option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="salary_range">Salary Range</Label>
                <Input
                  id="salary_range"
                  placeholder="e.g., $50,000 - $80,000"
                  value={formData.salary_range}
                  onChange={(e) => setFormData(prev => ({ ...prev, salary_range: e.target.value }))}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="closed">Closed</option>
              </select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Job Description</Label>
              <Textarea
                id="description"
                placeholder="Describe the role, responsibilities, and what you're looking for..."
                className="min-h-[150px]"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="requirements">Requirements</Label>
              <Textarea
                id="requirements"
                placeholder="List the required skills, experience, and qualifications..."
                className="min-h-[100px]"
                value={formData.requirements}
                onChange={(e) => setFormData(prev => ({ ...prev, requirements: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsCreateDialogOpen(false);
              setFormData({
                title: '',
                description: '',
                requirements: '',
                location: '',
                salary_range: '',
                employment_type: 'full-time',
                department: '',
                status: 'draft'
              });
            }}>
              Cancel
            </Button>
            <Button onClick={createJobListing} disabled={!formData.title || !formData.description}>
              Create Job
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Job Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Job Listing Details</DialogTitle>
          </DialogHeader>
          {selectedJob && (
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">{selectedJob.title}</h3>
                <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                  {selectedJob.department && (
                    <div className="flex items-center">
                      <Briefcase className="mr-1 h-4 w-4" />
                      {selectedJob.department}
                    </div>
                  )}
                  {selectedJob.location && (
                    <div className="flex items-center">
                      <MapPin className="mr-1 h-4 w-4" />
                      {selectedJob.location}
                    </div>
                  )}
                  {selectedJob.employment_type && (
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4" />
                      {selectedJob.employment_type}
                    </div>
                  )}
                  {selectedJob.salary_range && (
                    <div className="flex items-center">
                      <DollarSign className="mr-1 h-4 w-4" />
                      {selectedJob.salary_range}
                    </div>
                  )}
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-sm whitespace-pre-wrap">{selectedJob.description}</p>
              </div>
              {selectedJob.requirements && (
                <div>
                  <h4 className="font-medium mb-2">Requirements</h4>
                  <p className="text-sm whitespace-pre-wrap">{selectedJob.requirements}</p>
                </div>
              )}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Status:</span>
                {getStatusBadge(selectedJob.status)}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Job Listing</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedJob?.title}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedJob && deleteJobListing(selectedJob.id)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default CareersAdminPage;
