-- Add missing RPC functions for public invoice payment processing

-- Create a function to update invoice status for public invoices
CREATE OR REPLACE FUNCTION public.update_public_invoice_status(
  invoice_id UUID,
  new_status TEXT
)
RETURNS SETOF invoices
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update the invoice status
  RETURN QUERY
  UPDATE invoices
  SET 
    status = new_status,
    updated_at = NOW(),
    paid_at = CASE WHEN new_status = 'paid' THEN NOW() ELSE paid_at END
  WHERE id = invoice_id
  AND public_access_token IS NOT NULL
  AND (
    public_access_expires_at IS NULL
    OR public_access_expires_at > NOW()
  )
  RETURNING *;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.update_public_invoice_status(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_public_invoice_status(UUID, TEXT) TO anon;

-- Create a function to create payment records for public invoices
CREATE OR REPLACE FUNCTION public.create_public_payment_record(
  p_invoice_id UUID,
  p_amount NUMERIC,
  p_payment_method TEXT,
  p_status TEXT,
  p_transaction_reference TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_id UUID;
BEGIN
  -- Insert the payment record
  INSERT INTO invoice_payments (
    invoice_id,
    amount,
    payment_method,
    status,
    transaction_reference,
    created_at
  )
  VALUES (
    p_invoice_id,
    p_amount,
    p_payment_method,
    p_status,
    p_transaction_reference,
    NOW()
  )
  RETURNING id INTO v_id;
  
  RETURN v_id;
END;
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.create_public_payment_record(UUID, NUMERIC, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_public_payment_record(UUID, NUMERIC, TEXT, TEXT, TEXT) TO anon;

-- Fix the RLS policy for updating invoice status
DROP POLICY IF EXISTS "Allow updating status for public invoices" ON "invoices";

-- Create a new policy that doesn't reference the non-existent "amount" field
CREATE POLICY "Allow updating status for public invoices" ON "invoices"
FOR UPDATE
USING (
  public_access_token IS NOT NULL
  AND (
    public_access_expires_at IS NULL
    OR public_access_expires_at > NOW()
  )
)
WITH CHECK (
  -- Only allow updating specific fields
  (
    (NEW.status IS NOT NULL) AND
    (NEW.updated_at IS NOT NULL OR NEW.updated_at IS NULL) AND
    (NEW.paid_at IS NOT NULL OR NEW.paid_at IS NULL)
  )
);

-- Create a policy to allow generating public tokens
CREATE POLICY IF NOT EXISTS "Allow users to generate public tokens for their invoices" ON "invoices"
FOR UPDATE
USING (
  auth.uid() = user_id
)
WITH CHECK (
  -- Only allow updating public token fields
  (
    (NEW.public_access_token IS NOT NULL) AND
    (NEW.public_access_expires_at IS NOT NULL)
  )
);

-- Create a policy to allow updating invoice status by the owner
CREATE POLICY IF NOT EXISTS "Allow users to update their invoice status" ON "invoices"
FOR UPDATE
USING (
  auth.uid() = user_id
);

-- Create a policy to allow public access to invoice payments for public invoices
CREATE POLICY IF NOT EXISTS "Allow public access to invoice payments for public invoices" ON "invoice_payments"
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM invoices
    WHERE invoices.id = invoice_payments.invoice_id
      AND invoices.public_access_token IS NOT NULL
      AND (
        invoices.public_access_expires_at IS NULL
        OR invoices.public_access_expires_at > NOW()
      )
  )
);

-- Create a policy to allow creating payment records for public invoices
CREATE POLICY IF NOT EXISTS "Allow creating payment records for public invoices" ON "invoice_payments"
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM invoices
    WHERE invoices.id = invoice_payments.invoice_id
      AND invoices.public_access_token IS NOT NULL
      AND (
        invoices.public_access_expires_at IS NULL
        OR invoices.public_access_expires_at > NOW()
      )
  )
);
