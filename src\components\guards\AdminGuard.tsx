import { ReactNode, useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { useSession } from '@/contexts/SessionContext';
import { isAdmin } from '@/utils/adminUtils';

interface AdminGuardProps {
  children: ReactNode;
}

/**
 * AdminGuard component to protect routes that should only be accessible by admin users
 * Redirects to dashboard if the user is not an admin
 */
const AdminGuard = ({ children }: AdminGuardProps) => {
  const { session, user } = useSession();
  const [isAdminUser, setIsAdminUser] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const checkAdminStatus = async () => {
      setIsLoading(true);

      try {
        if (!session || !user) {
          setIsAdminUser(false);
          setIsLoading(false);
          // Use navigate instead of Navigate component to avoid location object cloning issues
          navigate('/auth', { replace: true });
          return;
        }

        const userId = session.user.id;
        const userEmail = user.email || '';

        const adminStatus = await isAdmin(userId, userEmail);
        setIsAdminUser(adminStatus);

        if (!adminStatus) {
          // Use navigate instead of Navigate component to avoid location object cloning issues
          navigate('/dashboard', { replace: true });
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdminUser(false);
        // Use navigate instead of Navigate component to avoid location object cloning issues
        navigate('/dashboard', { replace: true });
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminStatus();
  }, [session, user, navigate]);

  // Still loading, show nothing yet to avoid flashing content
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If not admin or not authenticated, we've already navigated away in the useEffect
  // So if we're still here, we're an admin
  if (!isAdminUser) {
    return null; // This will be rendered briefly before the navigation happens
  }

  // Admin user, render the protected content
  return <>{children}</>;
};

export default AdminGuard;
