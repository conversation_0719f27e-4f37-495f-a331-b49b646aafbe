import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Tables } from '@/integrations/supabase/types';
import { getUserPaidInvoices } from '@/services/clientPaidInvoiceService';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, FileText, ExternalLink, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';

interface ClientPaidInvoicesProps {
  limit?: number;
}

const ClientPaidInvoices: React.FC<ClientPaidInvoicesProps> = ({ limit }) => {
  const [paidInvoices, setPaidInvoices] = useState<Tables<'client_paid_invoices'>[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadPaidInvoices = async () => {
      setIsLoading(true);
      try {
        // Check if the user is authenticated
        const { data: session } = await supabase.auth.getSession();
        if (!session?.session?.user) {
          console.log('User not authenticated, skipping paid invoices fetch');
          setIsLoading(false);
          return;
        }

        console.log('Fetching paid invoices for user:', session.session.user.email);
        const invoices = await getUserPaidInvoices();
        console.log('Fetched paid invoices:', invoices);
        setPaidInvoices(invoices);
      } catch (error) {
        console.error('Error loading paid invoices:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPaidInvoices();
  }, []);

  // Apply limit if specified
  const displayedInvoices = limit ? paidInvoices.slice(0, limit) : paidInvoices;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-ghana-green" />
      </div>
    );
  }

  if (displayedInvoices.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Paid Invoices</CardTitle>
          <CardDescription>
            Invoices you've paid before creating your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-gray-500">No paid invoices found</p>
            <p className="text-xs text-gray-400 mt-2">
              If you've paid invoices before creating your account, they will appear here.
            </p>
            <div className="mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                <Loader2 className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Paid Invoices</CardTitle>
        <CardDescription>
          Invoices you've paid before creating your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayedInvoices.map((paidInvoice) => {
            const invoice = paidInvoice.invoices as any;
            return (
              <div
                key={paidInvoice.id}
                className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start gap-3 mb-3 sm:mb-0">
                  <div className="bg-ghana-green/10 p-2 rounded-full">
                    <FileText className="h-5 w-5 text-ghana-green" />
                  </div>
                  <div>
                    <h3 className="font-medium">
                      Invoice #{invoice?.invoice_number || 'Unknown'}
                    </h3>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 text-sm text-gray-500">
                      <span>
                        Paid on{' '}
                        {paidInvoice.payment_date
                          ? format(new Date(paidInvoice.payment_date), 'MMM dd, yyyy')
                          : 'Unknown date'}
                      </span>
                      <span className="hidden sm:inline">•</span>
                      <span>
                        Amount: {invoice?.currency || 'GHS'}{' '}
                        {invoice?.total_amount?.toFixed(2) || '0.00'}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 w-full sm:w-auto">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Paid
                  </Badge>
                  <Link
                    to={`/invoices/${invoice?.id}`}
                    className="ml-auto sm:ml-0"
                  >
                    <Button variant="outline" size="sm" className="gap-1">
                      <span className="hidden sm:inline">View</span>
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            );
          })}
        </div>

        {limit && paidInvoices.length > limit && (
          <div className="mt-4 text-center">
            <Link to="/paid-invoices">
              <Button variant="outline">
                View All Paid Invoices ({paidInvoices.length})
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ClientPaidInvoices;
